@extends('layouts.admin')

@section('title', 'تفاصيل المصروف - نظام إدارة المطعم')

@section('page-title', 'تفاصيل المصروف')

@section('content')
<div class="space-y-6">
    <!-- العنوان الرئيسي -->
    <div class="flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                <i class="fas fa-receipt text-blue-500 ml-3"></i>
                تفاصيل المصروف
            </h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">عرض تفاصيل المصروف #{{ $expense->expense_id }}</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('admin.expenses') }}" 
               class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg flex items-center transition-colors">
                <i class="fas fa-arrow-right ml-2"></i>
                <span>العودة للقائمة</span>
            </a>
            @if(!isset($expense->is_automatic) || !$expense->is_automatic)
            <a href="{{ route('admin.expenses.edit', $expense->expense_id) }}" 
               class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg flex items-center transition-colors">
                <i class="fas fa-edit ml-2"></i>
                <span>تعديل</span>
            </a>
            @endif
        </div>
    </div>

    <!-- بطاقة تفاصيل المصروف -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white flex items-center">
                <i class="fas fa-info-circle text-blue-500 ml-2"></i>
                معلومات المصروف
            </h3>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- الوصف -->
                <div class="space-y-2">
                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">الوصف</label>
                    <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <p class="text-gray-900 dark:text-white">{{ $expense->description }}</p>
                    </div>
                </div>

                <!-- المبلغ -->
                <div class="space-y-2">
                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">المبلغ</label>
                    <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <p class="text-2xl font-bold text-green-600 dark:text-green-400">
                            {{ number_format($expense->amount, 2) }} د.ل
                        </p>
                    </div>
                </div>

                <!-- الفئة -->
                <div class="space-y-2">
                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">الفئة</label>
                    <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        @php
                            $categoryData = [
                                'inventory_auto' => ['name' => 'صرف المخزون (تلقائي)', 'color' => 'blue', 'icon' => 'warehouse'],
                                'ingredients' => ['name' => 'المكونات (يدوي)', 'color' => 'green', 'icon' => 'leaf'],
                                'utilities' => ['name' => 'المرافق', 'color' => 'yellow', 'icon' => 'bolt'],
                                'salaries' => ['name' => 'الرواتب', 'color' => 'indigo', 'icon' => 'users'],
                                'maintenance' => ['name' => 'الصيانة', 'color' => 'red', 'icon' => 'tools'],
                                'other' => ['name' => 'أخرى', 'color' => 'gray', 'icon' => 'ellipsis-h']
                            ];
                            $categoryInfo = $categoryData[$expense->category] ?? $categoryData['other'];
                        @endphp
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-{{ $categoryInfo['color'] }}-100 dark:bg-{{ $categoryInfo['color'] }}-900/30 text-{{ $categoryInfo['color'] }}-800 dark:text-{{ $categoryInfo['color'] }}-300">
                            <i class="fas fa-{{ $categoryInfo['icon'] }} ml-2"></i>
                            {{ $categoryInfo['name'] }}
                        </span>
                    </div>
                </div>

                <!-- طريقة الدفع -->
                <div class="space-y-2">
                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">طريقة الدفع</label>
                    <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        @if(isset($expense->is_automatic) && $expense->is_automatic)
                        <span class="text-blue-600 dark:text-blue-400">
                            <i class="fas fa-warehouse ml-1"></i>
                            صرف مخزون تلقائي
                        </span>
                        @else
                        <p class="text-gray-900 dark:text-white">{{ $expense->payment_method ?? 'غير محدد' }}</p>
                        @endif
                    </div>
                </div>

                <!-- تاريخ المصروف -->
                <div class="space-y-2">
                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">تاريخ المصروف</label>
                    <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <p class="text-gray-900 dark:text-white">
                            {{ $expense->expense_date ? $expense->expense_date->format('d/m/Y') : 'غير محدد' }}
                        </p>
                        @if($expense->expense_date)
                        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                            {{ $expense->expense_date->format('l') }} - {{ $expense->expense_date->format('H:i') }}
                        </p>
                        @endif
                    </div>
                </div>

                <!-- المسجل -->
                <div class="space-y-2">
                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">سجل بواسطة</label>
                    <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        @if(isset($expense->is_automatic) && $expense->is_automatic)
                        <span class="text-blue-600 dark:text-blue-400">
                            <i class="fas fa-robot ml-1"></i>
                            النظام (تلقائي)
                        </span>
                        @else
                        <p class="text-gray-900 dark:text-white">
                            {{ $expense->recorder ? $expense->recorder->first_name . ' ' . $expense->recorder->last_name : 'غير معروف' }}
                        </p>
                        @endif
                    </div>
                </div>
            </div>

            <!-- معلومات إضافية -->
            @if(isset($expense->order_id) || isset($expense->is_automatic))
            <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">معلومات إضافية</h4>
                
                @if(isset($expense->order_id))
                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-4">
                    <div class="flex items-center">
                        <i class="fas fa-receipt text-blue-500 text-lg ml-3"></i>
                        <div>
                            <h5 class="font-medium text-blue-800 dark:text-blue-200">مرتبط بطلب</h5>
                            <p class="text-sm text-blue-600 dark:text-blue-300">رقم الطلب: #{{ $expense->order_id }}</p>
                        </div>
                    </div>
                </div>
                @endif

                @if(isset($expense->is_automatic) && $expense->is_automatic)
                <div class="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-info-circle text-amber-500 text-lg ml-3"></i>
                        <div>
                            <h5 class="font-medium text-amber-800 dark:text-amber-200">مصروف تلقائي</h5>
                            <p class="text-sm text-amber-600 dark:text-amber-300">تم إنشاء هذا المصروف تلقائياً من النظام عند استخدام المخزون</p>
                        </div>
                    </div>
                </div>
                @endif
            </div>
            @endif

            <!-- معلومات التوقيت -->
            <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">معلومات التوقيت</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700 dark:text-gray-300">تاريخ الإنشاء</label>
                        <p class="text-gray-900 dark:text-white">{{ $expense->created_at->format('d/m/Y H:i:s') }}</p>
                    </div>
                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700 dark:text-gray-300">آخر تحديث</label>
                        <p class="text-gray-900 dark:text-white">{{ $expense->updated_at->format('d/m/Y H:i:s') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
