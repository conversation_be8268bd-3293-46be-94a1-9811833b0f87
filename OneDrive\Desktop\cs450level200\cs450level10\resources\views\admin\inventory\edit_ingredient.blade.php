@extends('layouts.admin')

@section('title', 'تعديل المكون')

@section('content')
<div class="mb-6">
    <h2 class="text-2xl font-bold text-gray-800 dark:text-white">تعديل المكون</h2>
    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">تعديل معلومات المكون</p>
</div>

<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
    <div class="p-6">
        <form action="{{ route('admin.ingredients.update', $ingredient->ingredient_id) }}" method="POST">
            @csrf
            @method('PUT')
            
            <div class="mb-6">
                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">اسم المكون</label>
                <input type="text" id="name" name="name" value="{{ old('name', $ingredient->name) }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                @error('name')
                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                @enderror
            </div>
            
            <div class="mb-6">
                <label for="unit" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">وحدة القياس</label>
                <select id="unit" name="unit" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                    <option value="">اختر وحدة القياس</option>
                    <option value="كجم" {{ old('unit', $ingredient->unit) == 'كجم' ? 'selected' : '' }}>كيلوجرام (كجم)</option>
                    <option value="جم" {{ old('unit', $ingredient->unit) == 'جم' ? 'selected' : '' }}>جرام (جم)</option>
                    <option value="لتر" {{ old('unit', $ingredient->unit) == 'لتر' ? 'selected' : '' }}>لتر (لتر)</option>
                    <option value="مل" {{ old('unit', $ingredient->unit) == 'مل' ? 'selected' : '' }}>ميليلتر (مل)</option>
                    <option value="قطعة" {{ old('unit', $ingredient->unit) == 'قطعة' ? 'selected' : '' }}>قطعة</option>
                    <option value="علبة" {{ old('unit', $ingredient->unit) == 'علبة' ? 'selected' : '' }}>علبة</option>
                    <option value="كوب" {{ old('unit', $ingredient->unit) == 'كوب' ? 'selected' : '' }}>كوب</option>
                    <option value="ملعقة" {{ old('unit', $ingredient->unit) == 'ملعقة' ? 'selected' : '' }}>ملعقة</option>
                    <option value="حبة" {{ old('unit', $ingredient->unit) == 'حبة' ? 'selected' : '' }}>حبة</option>
                </select>
                @error('unit')
                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                @enderror
            </div>
            
            <div class="mb-6">
                <div class="flex items-center">
                    <input type="checkbox" id="is_active" name="is_active" value="1" {{ old('is_active', $ingredient->is_active) ? 'checked' : '' }} class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                    <label for="is_active" class="mr-2 block text-sm text-gray-700 dark:text-gray-300">نشط</label>
                </div>
                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">المكونات النشطة فقط يمكن استخدامها في الوصفات الجديدة</p>
            </div>
            
            <div class="flex justify-end space-x-2 space-x-reverse">
                <a href="{{ route('admin.inventory') }}" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">إلغاء</a>
                <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-all">تحديث المكون</button>
            </div>
        </form>
    </div>
</div>
@endsection
