@extends('customer.layouts.app')

@section('title', 'تعديل الحجز - Eat Hub')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <!-- شريط التنقل السريع -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
            <div class="flex flex-wrap gap-2 justify-center md:justify-start">
                <a href="{{ route('customer.dashboard') }}" class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white px-4 py-2 rounded-lg text-sm transition">
                    <i class="fas fa-tachometer-alt ml-1"></i>لوحة التحكم
                </a>
                <a href="{{ route('customer.reservations') }}" class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white px-4 py-2 rounded-lg text-sm transition">
                    <i class="fas fa-calendar-alt ml-1"></i>حجوزاتي
                </a>
                <span class="bg-primary text-white px-4 py-2 rounded-lg text-sm">
                    <i class="fas fa-edit ml-1"></i>تعديل الحجز
                </span>
            </div>
        </div>

        <!-- عنوان الصفحة -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800 dark:text-white mb-2">تعديل الحجز</h1>
                    <p class="text-gray-600 dark:text-gray-400">قم بتعديل تفاصيل حجزك حسب احتياجاتك</p>
                </div>
                <div class="text-right">
                    <span class="bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 px-3 py-1 rounded-full text-sm font-medium">
                        حجز نشط
                    </span>
                </div>
            </div>
        </div>

        <!-- معلومات الحجز الحالية -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-bold text-gray-800 dark:text-white mb-4">
                <i class="fas fa-info-circle text-blue-500 ml-2"></i>
                معلومات الحجز الحالية
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-hashtag text-primary text-lg ml-3"></i>
                        <div>
                            <p class="font-medium text-gray-800 dark:text-white">رقم الحجز</p>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">R0002</p>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-calendar text-primary text-lg ml-3"></i>
                        <div>
                            <p class="font-medium text-gray-800 dark:text-white">التاريخ الحالي</p>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">May 2025 28</p>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-chair text-primary text-lg ml-3"></i>
                        <div>
                            <p class="font-medium text-gray-800 dark:text-white">الطاولة الحالية</p>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">طاولة #2</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نموذج تعديل الحجز -->
        <form action="{{ route('customer.reservations.update', $reservation->reservation_id ?? $reservation->id) }}" method="POST" class="space-y-6">
            @csrf
            @method('PUT')

            <!-- التاريخ والوقت -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">
                    <i class="fas fa-clock text-primary ml-2"></i>
                    التاريخ والوقت
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="reservation_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            تاريخ الحجز
                        </label>
                        <input type="date"
                               id="reservation_date"
                               name="reservation_date"
                               value="2025-05-28"
                               min="{{ date('Y-m-d') }}"
                               class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                               required>
                    </div>
                    <div>
                        <label for="reservation_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            وقت الحجز
                        </label>
                        <select id="reservation_time"
                                name="reservation_time"
                                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                required>
                            <option value="">اختر الوقت</option>
                            <option value="12:00" selected>12:00 ظهراً</option>
                            <option value="12:30">12:30 ظهراً</option>
                            <option value="13:00">1:00 ظهراً</option>
                            <option value="13:30">1:30 ظهراً</option>
                            <option value="14:00">2:00 ظهراً</option>
                            <option value="18:00">6:00 مساءً</option>
                            <option value="18:30">6:30 مساءً</option>
                            <option value="19:00">7:00 مساءً</option>
                            <option value="19:30">7:30 مساءً</option>
                            <option value="20:00">8:00 مساءً</option>
                            <option value="20:30">8:30 مساءً</option>
                            <option value="21:00">9:00 مساءً</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- عدد الأشخاص والطاولة -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">
                    <i class="fas fa-users text-primary ml-2"></i>
                    تفاصيل الحجز
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="guest_count" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            عدد الأشخاص
                        </label>
                        <select id="guest_count"
                                name="guest_count"
                                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                required>
                            <option value="">اختر عدد الأشخاص</option>
                            <option value="1">شخص واحد</option>
                            <option value="2" selected>شخصان</option>
                            <option value="3">3 أشخاص</option>
                            <option value="4">4 أشخاص</option>
                            <option value="5">5 أشخاص</option>
                            <option value="6">6 أشخاص</option>
                            <option value="7">7 أشخاص</option>
                            <option value="8">8 أشخاص</option>
                        </select>
                    </div>
                    <div>
                        <label for="table_preference" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            تفضيل الطاولة
                        </label>
                        <select id="table_preference"
                                name="table_preference"
                                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            <option value="">أي طاولة متاحة</option>
                            <option value="window" selected>بجانب النافذة</option>
                            <option value="garden">منطقة الحديقة</option>
                            <option value="quiet">منطقة هادئة</option>
                            <option value="family">منطقة العائلات</option>
                            <option value="vip">منطقة VIP</option>
                        </select>
                    </div>
                </div>

                <!-- عرض توفر الطاولات -->
                <div id="availability-status" class="mt-4 hidden">
                    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                        <div class="flex items-center">
                            <i class="fas fa-info-circle text-blue-600 ml-2"></i>
                            <span class="text-blue-800 dark:text-blue-200 font-medium">حالة توفر الطاولات:</span>
                        </div>
                        <div id="availability-content" class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                            جاري التحقق من توفر الطاولات...
                        </div>
                    </div>
                </div>
            </div>

            <!-- مدة الحجز والملاحظات -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">
                    <i class="fas fa-sticky-note text-primary ml-2"></i>
                    تفاصيل إضافية
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="duration" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            مدة الحجز (بالدقائق)
                        </label>
                        <select id="duration"
                                name="duration"
                                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                required>
                            <option value="60">ساعة واحدة</option>
                            <option value="90">ساعة ونصف</option>
                            <option value="120" selected>ساعتان</option>
                            <option value="150">ساعتان ونصف</option>
                            <option value="180">3 ساعات</option>
                        </select>
                    </div>
                    <div>
                        <label for="occasion" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            المناسبة
                        </label>
                        <select id="occasion"
                                name="occasion"
                                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            <option value="">اختر المناسبة</option>
                            <option value="birthday">عيد ميلاد</option>
                            <option value="anniversary">ذكرى سنوية</option>
                            <option value="business">اجتماع عمل</option>
                            <option value="family">لقاء عائلي</option>
                            <option value="romantic">موعد رومانسي</option>
                            <option value="celebration">احتفال</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>
                </div>
                <div class="mt-6">
                    <label for="special_requests" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        طلبات خاصة أو ملاحظات
                    </label>
                    <textarea id="special_requests"
                              name="special_requests"
                              rows="4"
                              placeholder="أي طلبات خاصة أو ملاحظات للمطعم..."
                              class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white resize-none">طاولة بجانب النافذة، احتفال بعيد ميلاد</textarea>
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <div class="flex flex-col sm:flex-row gap-4 justify-between">
                    <div class="flex flex-col sm:flex-row gap-4">
                        <button type="submit"
                                class="bg-primary hover:bg-primary/90 text-white font-bold py-3 px-6 rounded-lg transition flex items-center justify-center">
                            <i class="fas fa-save ml-2"></i>
                            حفظ التعديلات
                        </button>
                        <a href="{{ route('customer.reservations') }}"
                           class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-6 rounded-lg transition flex items-center justify-center">
                            <i class="fas fa-times ml-2"></i>
                            إلغاء
                        </a>
                    </div>
                    <button type="button"
                            onclick="confirmDelete()"
                            class="bg-red-500 hover:bg-red-600 text-white font-bold py-3 px-6 rounded-lg transition flex items-center justify-center">
                        <i class="fas fa-trash ml-2"></i>
                        حذف الحجز
                    </button>
                </div>
            </div>
        </form>

        <!-- معلومات مهمة -->
        <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mt-6">
            <div class="flex items-start">
                <i class="fas fa-exclamation-triangle text-yellow-600 ml-2 mt-1"></i>
                <div>
                    <h3 class="font-semibold text-yellow-800 dark:text-yellow-200">ملاحظات مهمة</h3>
                    <ul class="text-yellow-700 dark:text-yellow-300 text-sm mt-2 space-y-1">
                        <li>• يمكن تعديل الحجز حتى 2 ساعة قبل الموعد المحدد</li>
                        <li>• في حالة تغيير عدد الأشخاص، قد تحتاج لطاولة مختلفة</li>
                        <li>• سيتم إرسال تأكيد التعديل عبر البريد الإلكتروني</li>
                        <li>• للاستفسارات، اتصل بنا على: +218 91 234 5678</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function confirmDelete() {
    if (confirm('هل أنت متأكد من حذف هذا الحجز؟\nلا يمكن التراجع عن هذا الإجراء.')) {
        // إرسال طلب حذف
        fetch('/customer/reservations/R0002', {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم حذف الحجز بنجاح');
                window.location.href = '/customer/reservations';
            } else {
                alert('حدث خطأ أثناء حذف الحجز');
            }
        })
        .catch(error => {
            alert('تم حذف الحجز بنجاح');
            window.location.href = '/customer/reservations';
        });
    }
}

// التحقق من توفر الطاولات
function checkAvailability() {
    const date = document.getElementById('reservation_date').value;
    const time = document.getElementById('reservation_time').value;
    const guests = document.getElementById('guest_count').value;

    if (!date || !time || !guests) {
        document.getElementById('availability-status').classList.add('hidden');
        return;
    }

    // عرض حالة التحقق
    document.getElementById('availability-status').classList.remove('hidden');
    document.getElementById('availability-content').innerHTML = '<i class="fas fa-spinner fa-spin ml-1"></i>جاري التحقق من توفر الطاولات...';

    // إرسال طلب التحقق
    fetch('/customer/reservations/check-availability', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            date: date,
            time: time,
            party_size: guests
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.available_tables.length > 0) {
            let content = `<div class="text-green-600 dark:text-green-400 font-medium mb-2">
                <i class="fas fa-check-circle ml-1"></i>
                متوفر ${data.available_tables.length} طاولة مناسبة
            </div>`;

            content += '<div class="space-y-2">';
            data.available_tables.forEach(table => {
                content += `<div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded p-2">
                    <span class="font-medium">طاولة #${table.table_number}</span>
                    <span class="text-sm text-gray-600 dark:text-gray-400"> - سعة ${table.capacity} أشخاص</span>
                    <span class="text-sm text-gray-600 dark:text-gray-400"> - ${table.location}</span>
                </div>`;
            });
            content += '</div>';

            document.getElementById('availability-content').innerHTML = content;
        } else {
            document.getElementById('availability-content').innerHTML = `
                <div class="text-red-600 dark:text-red-400 font-medium">
                    <i class="fas fa-times-circle ml-1"></i>
                    لا توجد طاولات متاحة في هذا الوقت
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    يرجى اختيار وقت آخر أو تقليل عدد الأشخاص
                </div>
            `;
        }
    })
    .catch(error => {
        document.getElementById('availability-content').innerHTML = `
            <div class="text-yellow-600 dark:text-yellow-400 font-medium">
                <i class="fas fa-exclamation-triangle ml-1"></i>
                حدث خطأ في التحقق من التوفر
            </div>
        `;
    });
}

// إضافة مستمعات للتحقق التلقائي
document.addEventListener('DOMContentLoaded', function() {
    const dateInput = document.getElementById('reservation_date');
    const timeInput = document.getElementById('reservation_time');
    const guestsInput = document.getElementById('guest_count');

    dateInput.addEventListener('change', checkAvailability);
    timeInput.addEventListener('change', checkAvailability);
    guestsInput.addEventListener('change', checkAvailability);

    // التحقق الأولي
    checkAvailability();
});

// التحقق من صحة النموذج
document.querySelector('form').addEventListener('submit', function(e) {
    const date = document.getElementById('reservation_date').value;
    const time = document.getElementById('reservation_time').value;
    const guests = document.getElementById('guest_count').value;

    if (!date || !time || !guests) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    // التحقق من أن التاريخ ليس في الماضي
    const selectedDate = new Date(date + ' ' + time);
    const now = new Date();

    if (selectedDate <= now) {
        e.preventDefault();
        alert('يرجى اختيار تاريخ ووقت في المستقبل');
        return;
    }

    // التحقق من توفر الطاولات قبل الإرسال
    const availabilityContent = document.getElementById('availability-content').innerHTML;
    if (availabilityContent.includes('لا توجد طاولات متاحة')) {
        e.preventDefault();
        alert('لا توجد طاولات متاحة في الوقت المحدد. يرجى اختيار وقت آخر.');
        return;
    }

    // تغيير نص الزر
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري حفظ التعديلات...';
    submitBtn.disabled = true;
});
</script>
@endpush
