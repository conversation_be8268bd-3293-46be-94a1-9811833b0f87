@extends('employee.layouts.app')

@section('title', 'تفاصيل الطاولة #' . $table->table_number)

@section('content')
<div id="show-table-page" class="page fade-in">
    <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-2">تفاصيل الطاولة <span class="text-primary">#{{ $table->table_number }}</span></h2>
            <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                <a href="{{ route('employee.dashboard') }}" class="hover:text-primary">لوحة التحكم</a>
                <i class="fas fa-chevron-left mx-2 text-xs"></i>
                <a href="{{ route('employee.tables') }}" class="hover:text-primary">حالة الطاولات</a>
                <i class="fas fa-chevron-left mx-2 text-xs"></i>
                <span>تفاصيل الطاولة #{{ $table->table_number }}</span>
            </div>
        </div>
        <div class="mt-4 md:mt-0">
            @if($table->status == 'available')
                <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                    <i class="fas fa-check-circle ml-1.5"></i>متاح
                </span>
            @elseif($table->status == 'occupied')
                <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                    <i class="fas fa-times-circle ml-1.5"></i>مشغول
                </span>
            @elseif($table->status == 'reserved')
                <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">
                    <i class="fas fa-clock ml-1.5"></i>محجوز
                </span>
            @endif
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div class="lg:col-span-2">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                    <h3 class="text-lg font-bold text-gray-800 dark:text-white flex items-center">
                        <i class="fas fa-info-circle text-primary ml-2"></i>
                        معلومات الطاولة
                    </h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div class="bg-gray-50 dark:bg-gray-700/30 rounded-lg p-4">
                            <h4 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                                <i class="fas fa-table text-primary ml-2"></i>
                                التفاصيل الأساسية
                            </h4>
                            <div class="space-y-3 text-gray-600 dark:text-gray-300">
                                <div class="flex">
                                    <span class="font-semibold w-32">رقم الطاولة:</span>
                                    <span class="text-primary font-bold">{{ $table->table_number }}</span>
                                </div>
                                <div class="flex">
                                    <span class="font-semibold w-32">السعة:</span>
                                    <span>{{ $table->capacity }} أشخاص</span>
                                </div>
                                <div class="flex">
                                    <span class="font-semibold w-32">الموقع:</span>
                                    <span>{{ $table->location }}</span>
                                </div>
                                <div class="flex">
                                    <span class="font-semibold w-32">تاريخ الإضافة:</span>
                                    <span>{{ $table->created_at->format('Y-m-d H:i') }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700/30 rounded-lg p-4">
                            <h4 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                                <i class="fas fa-history text-primary ml-2"></i>
                                سجل الاستخدام
                            </h4>
                            <div class="space-y-3 text-gray-600 dark:text-gray-300">
                                <div class="flex">
                                    <span class="font-semibold w-32">عدد الطلبات:</span>
                                    <span class="text-primary font-bold">{{ $ordersCount }}</span>
                                </div>
                                <div class="flex">
                                    <span class="font-semibold w-32">آخر استخدام:</span>
                                    <span>{{ $lastOrder ? $lastOrder->created_at->format('Y-m-d H:i') : 'لا يوجد' }}</span>
                                </div>
                                <div class="flex">
                                    <span class="font-semibold w-32">إجمالي المبيعات:</span>
                                    <span class="text-primary font-bold">{{ number_format($totalSales, 2) }} <span class="text-xs text-gray-500 dark:text-gray-400">د.ل</span></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($currentOrder)
                    <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 mb-6">
                        <h4 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                            <i class="fas fa-receipt text-primary ml-2"></i>
                            الطلب الحالي
                        </h4>
                        <div class="space-y-3 text-gray-600 dark:text-gray-300">
                            <div class="flex">
                                <span class="font-semibold w-32">رقم الطلب:</span>
                                <a href="{{ route('employee.orders.show', $currentOrder->order_id) }}" class="text-primary hover:underline">#{{ $currentOrder->order_id }}</a>
                            </div>
                            <div class="flex">
                                <span class="font-semibold w-32">العميل:</span>
                                <span>{{ $currentOrder->user->first_name }} {{ $currentOrder->user->last_name }}</span>
                            </div>
                            <div class="flex">
                                <span class="font-semibold w-32">وقت الطلب:</span>
                                <span>{{ $currentOrder->created_at->format('Y-m-d H:i') }}</span>
                            </div>
                            <div class="flex">
                                <span class="font-semibold w-32">المبلغ:</span>
                                <span class="text-primary font-bold">{{ number_format($currentOrder->total_amount, 2) }} <span class="text-xs text-gray-500 dark:text-gray-400">د.ل</span></span>
                            </div>
                        </div>
                    </div>
                    @endif

                    <h4 class="text-lg font-semibold text-gray-800 dark:text-white mt-8 mb-4 flex items-center">
                        <i class="fas fa-history text-primary ml-2"></i>
                        آخر الطلبات
                    </h4>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">رقم الطلب</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">العميل</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">التاريخ</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">المبلغ</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الحالة</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                @forelse($recentOrders as $order)
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                                        <a href="{{ route('employee.orders.show', $order->order_id) }}" class="text-primary hover:underline">#{{ $order->order_id }}</a>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                                        {{ $order->user->first_name }} {{ $order->user->last_name }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                                        {{ $order->created_at->format('Y-m-d H:i') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                                        {{ number_format($order->total_amount, 2) }} د.ل
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        @if($order->status == 'pending')
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">
                                                قيد الانتظار
                                            </span>
                                        @elseif($order->status == 'preparing')
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                                                قيد التحضير
                                            </span>
                                        @elseif($order->status == 'completed')
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                                مكتمل
                                            </span>
                                        @elseif($order->status == 'canceled')
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                                                ملغي
                                            </span>
                                        @endif
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="5" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                                        لا توجد طلبات سابقة لهذه الطاولة
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="lg:col-span-1">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden sticky top-6">
                <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                    <h3 class="text-lg font-bold text-gray-800 dark:text-white flex items-center">
                        <i class="fas fa-cog text-primary ml-2"></i>
                        الإجراءات
                    </h3>
                </div>
                <div class="p-6">
                    <div class="space-y-3">
                        <a href="{{ route('employee.tables.edit', $table->table_id) }}" class="w-full bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
                            <i class="fas fa-edit ml-2"></i>
                            <span>تعديل الطاولة</span>
                        </a>
                        <button type="button" class="w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all" onclick="document.getElementById('changeStatusModal').classList.remove('hidden')">
                            <i class="fas fa-exchange-alt ml-2"></i>
                            <span>تغيير الحالة</span>
                        </button>
                        <button type="button" class="w-full bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all" onclick="confirmDelete('{{ $table->table_id }}', '{{ $table->table_number }}')">
                            <i class="fas fa-trash-alt ml-2"></i>
                            <span>حذف الطاولة</span>
                        </button>
                        <a href="{{ route('employee.tables') }}" class="w-full bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
                            <i class="fas fa-arrow-right ml-2"></i>
                            <span>العودة للطاولات</span>
                        </a>
                    </div>
                    
                    @if($table->status == 'available')
                    <div class="mt-6 border-t border-gray-200 dark:border-gray-700 pt-6">
                        <h4 class="text-md font-semibold text-gray-800 dark:text-white mb-4">إنشاء طلب جديد</h4>
                        <a href="{{ route('employee.orders.create', ['table_id' => $table->table_id]) }}" class="w-full bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
                            <i class="fas fa-plus ml-2"></i>
                            <span>طلب جديد</span>
                        </a>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal: Change Status -->
<div id="changeStatusModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
        </div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:text-right sm:w-full">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                            تغيير حالة الطاولة #{{ $table->table_number }}
                        </h3>
                        <div class="mt-6">
                            <form id="changeStatusForm" action="{{ route('employee.tables.update-status', $table->table_id) }}" method="POST">
                                @csrf
                                @method('PUT')
                                <div class="mb-4">
                                    <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الحالة الجديدة</label>
                                    <select id="status" name="status" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                                        <option value="available" {{ $table->status == 'available' ? 'selected' : '' }}>متاح</option>
                                        <option value="occupied" {{ $table->status == 'occupied' ? 'selected' : '' }}>مشغول</option>
                                        <option value="reserved" {{ $table->status == 'reserved' ? 'selected' : '' }}>محجوز</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="confirmChangeStatus" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary/90 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm">
                    تأكيد
                </button>
                <button type="button" id="cancelChangeStatus" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    إلغاء
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal: Delete Confirmation -->
<div id="deleteConfirmationModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
        </div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/30 sm:mx-0 sm:h-10 sm:w-10">
                        <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:mr-4 sm:text-right">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                            حذف الطاولة
                        </h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                هل أنت متأكد من رغبتك في حذف الطاولة رقم {{ $table->table_number }}؟ لا يمكن التراجع عن هذا الإجراء.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <form id="deleteForm" action="{{ route('employee.tables.destroy', $table->table_id) }}" method="POST">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm">
                        حذف
                    </button>
                </form>
                <button type="button" id="cancelDelete" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    إلغاء
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Change Status Modal
    const changeStatusModal = document.getElementById('changeStatusModal');
    const confirmChangeStatus = document.getElementById('confirmChangeStatus');
    const cancelChangeStatus = document.getElementById('cancelChangeStatus');
    const changeStatusForm = document.getElementById('changeStatusForm');

    confirmChangeStatus.addEventListener('click', () => {
        changeStatusForm.submit();
    });

    cancelChangeStatus.addEventListener('click', () => {
        changeStatusModal.classList.add('hidden');
    });

    // Delete Confirmation Modal
    function confirmDelete(tableId, tableNumber) {
        document.getElementById('deleteConfirmationModal').classList.remove('hidden');
    }

    document.getElementById('cancelDelete').addEventListener('click', () => {
        document.getElementById('deleteConfirmationModal').classList.add('hidden');
    });

    // Close modals when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target === changeStatusModal) {
            changeStatusModal.classList.add('hidden');
        }
        if (e.target === document.getElementById('deleteConfirmationModal')) {
            document.getElementById('deleteConfirmationModal').classList.add('hidden');
        }
    });
</script>
@endsection
