@include('customer.partials.head')

<body class="bg-gray-50 dark:bg-gray-900">

@include('customer.partials.header')

<!-- المحتوى الرئيسي -->
<main class="min-h-screen pt-20">
    <div class="container mx-auto px-4 py-8">
        <!-- عنوان الصفحة -->
        <div class="flex items-center mb-8">
            <a href="{{ route('customer.reservations') }}"
               class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 p-2 rounded-lg transition ml-4">
                <i class="fas fa-arrow-right"></i>
            </a>
            <div>
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-2">تفاصيل الحجز</h1>
                <p class="text-gray-600 dark:text-gray-400">حجز رقم #{{ $reservation->reservation_id }}</p>
            </div>
        </div>

        <div class="max-w-4xl mx-auto">
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                <!-- حالة الحجز -->
                <div class="bg-gradient-to-r from-primary to-primary/80 text-white p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-2xl font-bold mb-2">حجز رقم #{{ $reservation->reservation_id }}</h2>
                            <p class="opacity-90">{{ \Carbon\Carbon::parse($reservation->reservation_time)->format('l, F j, Y') }}</p>
                        </div>
                        <div class="text-left">
                            <span class="inline-block px-4 py-2 rounded-full text-sm font-medium bg-white/20 backdrop-blur-sm">
                                @if($reservation->status == 'confirmed')
                                    <i class="fas fa-check-circle ml-1"></i>مؤكد
                                @elseif($reservation->status == 'pending')
                                    <i class="fas fa-clock ml-1"></i>في الانتظار
                                @elseif($reservation->status == 'canceled')
                                    <i class="fas fa-times-circle ml-1"></i>ملغي
                                @else
                                    {{ $reservation->status }}
                                @endif
                            </span>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل الحجز -->
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <!-- معلومات التوقيت -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                                <i class="fas fa-calendar-alt text-primary ml-2"></i>
                                معلومات التوقيت
                            </h3>

                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-gray-600 dark:text-gray-400">التاريخ:</span>
                                    <span class="font-medium text-gray-800 dark:text-white">
                                        {{ \Carbon\Carbon::parse($reservation->reservation_time)->format('Y-m-d') }}
                                    </span>
                                </div>
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-gray-600 dark:text-gray-400">الوقت:</span>
                                    <span class="font-medium text-gray-800 dark:text-white">
                                        {{ \Carbon\Carbon::parse($reservation->reservation_time)->format('H:i') }}
                                    </span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">عدد الأشخاص:</span>
                                    <span class="font-medium text-gray-800 dark:text-white">
                                        {{ $reservation->party_size ?? 'غير محدد' }} أشخاص
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات الطاولة -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                                <i class="fas fa-chair text-primary ml-2"></i>
                                معلومات الطاولة
                            </h3>

                            @if($reservation->table)
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-gray-600 dark:text-gray-400">رقم الطاولة:</span>
                                    <span class="font-medium text-gray-800 dark:text-white">
                                        {{ $reservation->table->table_number }}
                                    </span>
                                </div>
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-gray-600 dark:text-gray-400">السعة:</span>
                                    <span class="font-medium text-gray-800 dark:text-white">
                                        {{ $reservation->table->capacity }} أشخاص
                                    </span>
                                </div>
                                @if($reservation->table->location)
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">الموقع:</span>
                                    <span class="font-medium text-gray-800 dark:text-white">
                                        {{ $reservation->table->location }}
                                    </span>
                                </div>
                                @endif
                            </div>
                            @else
                            <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 p-4 rounded-lg">
                                <p class="text-yellow-800 dark:text-yellow-200">
                                    <i class="fas fa-exclamation-triangle ml-2"></i>
                                    لم يتم تحديد طاولة بعد
                                </p>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- معلومات العرض -->
                    @if($reservation->offer_title)
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                            <i class="fas fa-tag text-primary ml-2"></i>
                            معلومات العرض
                        </h3>
                        <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 p-4 rounded-lg">
                            <div class="space-y-3">
                                <div class="flex items-center">
                                    <i class="fas fa-gift text-green-600 ml-2"></i>
                                    <span class="font-medium text-green-800 dark:text-green-200">
                                        {{ $reservation->offer_title }}
                                    </span>
                                </div>
                                @if($reservation->contact_phone)
                                <div class="flex items-center">
                                    <i class="fas fa-phone text-green-600 ml-2"></i>
                                    <span class="text-green-700 dark:text-green-300">
                                        رقم التواصل: {{ $reservation->contact_phone }}
                                    </span>
                                </div>
                                @endif
                                @if($reservation->offer_slug)
                                <div class="flex items-center">
                                    <i class="fas fa-link text-green-600 ml-2"></i>
                                    <a href="{{ route('customer.offers.show', $reservation->offer_slug) }}"
                                       class="text-green-700 dark:text-green-300 hover:underline">
                                        عرض تفاصيل العرض
                                    </a>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- الملاحظات الخاصة -->
                    @if($reservation->special_requests)
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                            <i class="fas fa-sticky-note text-primary ml-2"></i>
                            الملاحظات الخاصة
                        </h3>
                        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 p-4 rounded-lg">
                            <p class="text-blue-800 dark:text-blue-200">{{ $reservation->special_requests }}</p>
                        </div>
                    </div>
                    @endif

                    <!-- معلومات إضافية -->
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                            <i class="fas fa-info-circle text-primary ml-2"></i>
                            معلومات إضافية
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600 dark:text-gray-400">تاريخ الحجز:</span>
                                <span class="text-gray-800 dark:text-white">
                                    {{ $reservation->created_at->format('Y-m-d H:i') }}
                                </span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600 dark:text-gray-400">آخر تحديث:</span>
                                <span class="text-gray-800 dark:text-white">
                                    {{ $reservation->updated_at->format('Y-m-d H:i') }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-6 mt-6">
                        <div class="flex flex-col sm:flex-row gap-4">
                            @if($reservation->status == 'pending' || $reservation->status == 'confirmed')
                                @if(\Carbon\Carbon::parse($reservation->reservation_time)->isFuture())
                                <form action="{{ route('customer.reservations.cancel', $reservation->reservation_id) }}"
                                      method="POST"
                                      onsubmit="return confirm('هل أنت متأكد من إلغاء هذا الحجز؟')"
                                      class="flex-1">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit"
                                            class="w-full bg-red-500 hover:bg-red-600 text-white font-bold py-3 px-6 rounded-lg transition">
                                        <i class="fas fa-times ml-2"></i>
                                        إلغاء الحجز
                                    </button>
                                </form>
                                @endif
                            @endif

                            <a href="{{ route('customer.reservations') }}"
                               class="flex-1 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-bold py-3 px-6 rounded-lg transition text-center">
                                <i class="fas fa-list ml-2"></i>
                                العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

@include('customer.partials.footer')
@include('customer.partials.scripts')

</body>
</html>
