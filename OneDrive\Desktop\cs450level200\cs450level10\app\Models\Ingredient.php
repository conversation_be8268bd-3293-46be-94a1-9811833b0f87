<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Ingredient extends Model
{
    use HasFactory;

    protected $primaryKey = 'ingredient_id';

    protected $fillable = [
        'name',
        'unit',
        'cost_per_unit',
        'is_active',
        'expiry_date'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'expiry_date' => 'date'
    ];

    public function inventory()
    {
        return $this->hasMany(Inventory::class, 'ingredient_id');
    }

    public function recipes()
    {
        return $this->hasMany(Recipe::class, 'ingredient_id');
    }

    public function inventoryTransactions()
    {
        return $this->hasManyThrough(InventoryTransaction::class, Inventory::class);
    }
}