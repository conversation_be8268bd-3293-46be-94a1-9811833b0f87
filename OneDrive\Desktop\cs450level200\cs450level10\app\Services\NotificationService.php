<?php

namespace App\Services;

use App\Models\Notification;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    /**
     * إنشاء إشعار جديد
     *
     * @param int $userId معرف المستخدم
     * @param string $title عنوان الإشعار
     * @param string $message نص الإشعار
     * @param string|null $type نوع الإشعار (order, reservation, inventory, system)
     * @param array|null $data بيانات إضافية للإشعار
     * @param string|null $actionUrl رابط الإجراء
     * @param string|null $actionText نص الإجراء
     * @return Notification|null
     */
    public function create(
        int $userId,
        string $title,
        string $message,
        ?string $type = null,
        ?array $data = null,
        ?string $actionUrl = null,
        ?string $actionText = null
    ): ?Notification {
        try {
            return Notification::create([
                'user_id' => $userId,
                'title' => $title,
                'type' => $type,
                'message' => $message,
                'data' => $data ? json_encode($data) : null,
                'action_url' => $actionUrl,
                'action_text' => $actionText,
                'is_read' => false
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating notification: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * إنشاء إشعار لمستخدم محدد
     *
     * @param User $user المستخدم
     * @param string $title عنوان الإشعار
     * @param string $message نص الإشعار
     * @param string|null $type نوع الإشعار
     * @param array|null $data بيانات إضافية للإشعار
     * @param string|null $actionUrl رابط الإجراء
     * @param string|null $actionText نص الإجراء
     * @return Notification|null
     */
    public function notifyUser(
        User $user,
        string $title,
        string $message,
        ?string $type = null,
        ?array $data = null,
        ?string $actionUrl = null,
        ?string $actionText = null
    ): ?Notification {
        return $this->create(
            $user->user_id,
            $title,
            $message,
            $type,
            $data,
            $actionUrl,
            $actionText
        );
    }

    /**
     * إنشاء إشعار لمجموعة من المستخدمين
     *
     * @param array $userIds معرفات المستخدمين
     * @param string $title عنوان الإشعار
     * @param string $message نص الإشعار
     * @param string|null $type نوع الإشعار
     * @param array|null $data بيانات إضافية للإشعار
     * @param string|null $actionUrl رابط الإجراء
     * @param string|null $actionText نص الإجراء
     * @return array
     */
    public function notifyUsers(
        array $userIds,
        string $title,
        string $message,
        ?string $type = null,
        ?array $data = null,
        ?string $actionUrl = null,
        ?string $actionText = null
    ): array {
        $notifications = [];

        foreach ($userIds as $userId) {
            $notification = $this->create(
                $userId,
                $title,
                $message,
                $type,
                $data,
                $actionUrl,
                $actionText
            );

            if ($notification) {
                $notifications[] = $notification;
            }
        }

        return $notifications;
    }

    /**
     * إنشاء إشعار لجميع المستخدمين من نوع معين
     *
     * @param string $role نوع المستخدم (admin, employee, customer)
     * @param string $title عنوان الإشعار
     * @param string $message نص الإشعار
     * @param string|null $type نوع الإشعار
     * @param array|null $data بيانات إضافية للإشعار
     * @param string|null $actionUrl رابط الإجراء
     * @param string|null $actionText نص الإجراء
     * @return array
     */
    public function notifyRole(
        string $role,
        string $title,
        string $message,
        ?string $type = null,
        ?array $data = null,
        ?string $actionUrl = null,
        ?string $actionText = null
    ): array {
        $users = User::where('role', $role)->get();
        $userIds = $users->pluck('user_id')->toArray();

        return $this->notifyUsers(
            $userIds,
            $title,
            $message,
            $type,
            $data,
            $actionUrl,
            $actionText
        );
    }

    /**
     * إنشاء إشعار للمسؤولين
     *
     * @param string $title عنوان الإشعار
     * @param string $message نص الإشعار
     * @param string|null $type نوع الإشعار
     * @param array|null $data بيانات إضافية للإشعار
     * @param string|null $actionUrl رابط الإجراء
     * @param string|null $actionText نص الإجراء
     * @return array
     */
    public function notifyAdmins(
        string $title,
        string $message,
        ?string $type = null,
        ?array $data = null,
        ?string $actionUrl = null,
        ?string $actionText = null
    ): array {
        return $this->notifyRole('admin', $title, $message, $type, $data, $actionUrl, $actionText);
    }

    /**
     * إنشاء إشعار للموظفين
     *
     * @param string $title عنوان الإشعار
     * @param string $message نص الإشعار
     * @param string|null $type نوع الإشعار
     * @param array|null $data بيانات إضافية للإشعار
     * @param string|null $actionUrl رابط الإجراء
     * @param string|null $actionText نص الإجراء
     * @return array
     */
    public function notifyEmployees(
        string $title,
        string $message,
        ?string $type = null,
        ?array $data = null,
        ?string $actionUrl = null,
        ?string $actionText = null
    ): array {
        return $this->notifyRole('employee', $title, $message, $type, $data, $actionUrl, $actionText);
    }

    /**
     * إنشاء إشعار للعملاء
     *
     * @param string $title عنوان الإشعار
     * @param string $message نص الإشعار
     * @param string|null $type نوع الإشعار
     * @param array|null $data بيانات إضافية للإشعار
     * @param string|null $actionUrl رابط الإجراء
     * @param string|null $actionText نص الإجراء
     * @return array
     */
    public function notifyCustomers(
        string $title,
        string $message,
        ?string $type = null,
        ?array $data = null,
        ?string $actionUrl = null,
        ?string $actionText = null
    ): array {
        return $this->notifyRole('customer', $title, $message, $type, $data, $actionUrl, $actionText);
    }

    /**
     * إنشاء إشعار لجميع المستخدمين
     *
     * @param string $title عنوان الإشعار
     * @param string $message نص الإشعار
     * @param string|null $type نوع الإشعار
     * @param array|null $data بيانات إضافية للإشعار
     * @param string|null $actionUrl رابط الإجراء
     * @param string|null $actionText نص الإجراء
     * @return array
     */
    public function notifyAll(
        string $title,
        string $message,
        ?string $type = null,
        ?array $data = null,
        ?string $actionUrl = null,
        ?string $actionText = null
    ): array {
        $userIds = User::pluck('user_id')->toArray();

        return $this->notifyUsers(
            $userIds,
            $title,
            $message,
            $type,
            $data,
            $actionUrl,
            $actionText
        );
    }

    /**
     * إنشاء إشعار للمسؤولين والموظفين (طاقم العمل)
     *
     * @param string $title عنوان الإشعار
     * @param string $message نص الإشعار
     * @param string|null $type نوع الإشعار
     * @param array|null $data بيانات إضافية للإشعار
     * @param string|null $actionUrl رابط الإجراء
     * @param string|null $actionText نص الإجراء
     * @return array
     */
    public function notifyStaff(
        string $title,
        string $message,
        ?string $type = null,
        ?array $data = null,
        ?string $actionUrl = null,
        ?string $actionText = null
    ): array {
        $userIds = User::whereIn('role', ['admin', 'employee'])->pluck('user_id')->toArray();

        return $this->notifyUsers(
            $userIds,
            $title,
            $message,
            $type,
            $data,
            $actionUrl,
            $actionText
        );
    }
}
