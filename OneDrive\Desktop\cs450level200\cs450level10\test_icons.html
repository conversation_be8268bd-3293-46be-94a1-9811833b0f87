<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأيقونات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .btn-magical {
            transition: all 0.3s ease;
        }
        .btn-magical:hover {
            transform: scale(1.05);
        }
        .cart-count, .notification-count {
            transition: all 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8">اختبار الأيقونات والوظائف</h1>
        
        <!-- شريط الأيقونات -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="flex items-center justify-center space-x-4 space-x-reverse">
                
                <!-- زر القائمة الجانبية -->
                <button id="sidebarToggle" class="btn-magical p-3 rounded-xl bg-gradient-to-r from-orange-100 to-red-100 text-orange-600 hover:from-orange-200 hover:to-red-200 transition-all duration-300 shadow-lg hover:shadow-xl border border-orange-200">
                    <i class="fas fa-bars text-xl"></i>
                </button>

                <!-- زر سلة الطلبات -->
                <div class="relative">
                    <button id="cartButton" class="btn-magical relative p-3 rounded-xl bg-gradient-to-r from-green-100 to-emerald-100 text-green-600 hover:from-green-200 hover:to-emerald-200 transition-all duration-300 shadow-lg hover:shadow-xl inline-block group border border-green-200">
                        <i class="fas fa-shopping-cart text-lg group-hover:scale-110 transition-transform duration-300"></i>
                        <span class="cart-count absolute -top-1 -right-1 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-full text-xs w-6 h-6 flex items-center justify-center shadow-lg animate-bounce font-bold">3</span>
                    </button>
                    <!-- قائمة سلة الطلبات -->
                    <div id="cartMenu" class="absolute left-0 mt-2 w-80 bg-white rounded-md shadow-lg py-1 z-10 hidden">
                        <div class="px-4 py-2 border-b border-gray-200 flex justify-between items-center">
                            <h3 class="font-bold text-gray-800">سلة الطلبات</h3>
                            <span class="text-primary text-sm hover:underline cursor-pointer">إنشاء طلب</span>
                        </div>
                        <div id="cartItemsList" class="max-h-64 overflow-y-auto">
                            <div class="px-4 py-3 border-b border-gray-200">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h4 class="font-medium text-gray-800">برجر لحم</h4>
                                        <div class="flex items-center mt-1">
                                            <span class="text-sm text-gray-500">الكمية: 2</span>
                                            <span class="mx-2 text-gray-300">|</span>
                                            <span class="text-sm text-gray-500">15.00 د.ل</span>
                                        </div>
                                    </div>
                                    <button class="text-red-500 hover:text-red-700">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- زر الإشعارات -->
                <div class="relative">
                    <button id="notificationBtn" class="btn-magical relative p-3 rounded-xl bg-gradient-to-r from-purple-100 to-pink-100 text-purple-600 hover:from-purple-200 hover:to-pink-200 transition-all duration-300 shadow-lg hover:shadow-xl group border border-purple-200">
                        <i class="fas fa-bell text-lg group-hover:animate-pulse"></i>
                        <span id="notificationCount" class="absolute -top-1 -right-1 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-full text-xs min-w-[24px] h-6 flex items-center justify-center shadow-lg animate-bounce font-bold">5</span>
                    </button>
                    <!-- قائمة الإشعارات -->
                    <div id="notificationsMenu" class="absolute left-0 mt-2 w-80 bg-white rounded-md shadow-lg py-1 z-10 hidden">
                        <div class="px-4 py-2 border-b border-gray-200 flex justify-between items-center">
                            <h3 class="font-bold text-gray-800">الإشعارات</h3>
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <button onclick="markAllNotificationsAsRead()" class="text-xs text-gray-500 hover:text-primary transition-colors" title="تعليم الكل كمقروء">
                                    <i class="fas fa-check-double"></i>
                                </button>
                                <span class="text-primary text-sm hover:underline cursor-pointer">عرض الكل</span>
                            </div>
                        </div>
                        <div id="notificationsList" class="max-h-64 overflow-y-auto">
                            <div class="px-4 py-3 hover:bg-gray-50 border-b border-gray-100 cursor-pointer">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 ml-3">
                                        <div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-500">
                                            <i class="fas fa-shopping-cart"></i>
                                        </div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center justify-between">
                                            <p class="text-sm font-medium text-gray-900 truncate">طلب جديد</p>
                                            <div class="w-2 h-2 bg-primary rounded-full"></div>
                                        </div>
                                        <p class="text-sm text-gray-500 line-clamp-2">تم استلام طلب جديد من العميل أحمد</p>
                                        <p class="text-xs text-gray-400 mt-1">منذ 5 دقائق</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- زر تبديل الثيم -->
                <button data-theme-toggle class="btn-magical theme-toggle p-3 rounded-xl bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-600 hover:from-yellow-200 hover:to-orange-200 transition-all duration-300 shadow-lg hover:shadow-xl group border border-yellow-200" title="تبديل الثيم">
                    <i class="theme-icon fas fa-adjust text-lg group-hover:rotate-180 transition-transform duration-500"></i>
                </button>

                <!-- زر المستخدم -->
                <div class="relative">
                    <button id="userMenuBtn" class="btn-magical flex items-center p-3 rounded-xl bg-gradient-to-r from-indigo-100 to-blue-100 text-indigo-600 hover:from-indigo-200 hover:to-blue-200 transition-all duration-300 shadow-lg hover:shadow-xl group border border-indigo-200">
                        <div class="relative">
                            <div class="w-12 h-12 rounded-full bg-gradient-to-r from-indigo-500 to-blue-500 flex items-center justify-center text-white font-bold overflow-hidden shadow-lg group-hover:scale-110 transition-transform duration-300 border-2 border-white">
                                <span class="text-lg">أ</span>
                            </div>
                            <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white shadow-sm animate-pulse"></div>
                        </div>
                        <div class="mr-3">
                            <div class="text-right">
                                <span class="font-bold text-lg">أحمد محمد</span>
                                <div class="flex items-center justify-end mt-1">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border border-blue-200 shadow-sm">
                                        <i class="fas fa-user-tie mr-1"></i>
                                        موظف
                                    </span>
                                </div>
                            </div>
                        </div>
                        <i class="fas fa-chevron-down text-sm mr-2 group-hover:rotate-180 transition-transform duration-300 text-indigo-500"></i>
                    </button>
                    <!-- قائمة المستخدم -->
                    <div id="userMenu" class="absolute left-0 mt-2 w-64 bg-white rounded-md shadow-lg py-1 z-10 hidden">
                        <div class="px-4 py-3 border-b border-gray-200">
                            <p class="text-sm font-medium text-gray-900">أحمد محمد</p>
                            <p class="text-xs text-gray-500 truncate"><EMAIL></p>
                        </div>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">
                            <i class="fas fa-user-circle ml-2"></i>الملف الشخصي
                        </a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">
                            <i class="fas fa-cog ml-2"></i>الإعدادات
                        </a>
                        <div class="border-t border-gray-200 my-1"></div>
                        <button class="w-full text-right block px-4 py-2 text-gray-700 hover:bg-gray-100">
                            <i class="fas fa-sign-out-alt ml-2"></i>تسجيل الخروج
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- منطقة الاختبار -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold mb-4">منطقة الاختبار</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button onclick="addToCart('1', 'برجر لحم', 15.00)" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    إضافة إلى السلة
                </button>
                <button onclick="showSuccess('تم بنجاح!')" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    إظهار رسالة نجاح
                </button>
                <button onclick="showError('حدث خطأ!')" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                    إظهار رسالة خطأ
                </button>
                <button onclick="showWarning('تحذير!')" class="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600">
                    إظهار تحذير
                </button>
            </div>
        </div>
    </div>

    <!-- القائمة الجانبية للموبايل -->
    <div id="mobileMenu" class="fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform translate-x-full transition-transform duration-300 ease-in-out">
        <div class="flex items-center justify-between p-4 border-b">
            <h2 class="text-lg font-semibold">القائمة</h2>
            <button id="closeMobileMenu" class="text-gray-500 hover:text-gray-700">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <nav class="p-4">
            <a href="#" class="block py-2 text-gray-700 hover:text-blue-600">لوحة التحكم</a>
            <a href="#" class="block py-2 text-gray-700 hover:text-blue-600">الطلبات</a>
            <a href="#" class="block py-2 text-gray-700 hover:text-blue-600">الحجوزات</a>
        </nav>
    </div>

    <script>
        // نسخ الكود من ملف scripts.blade.php
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded');

            setTimeout(function() {
                console.log('Initializing scripts...');
                
                // إضافة تأثيرات بصرية للأزرار
                const buttons = document.querySelectorAll('.btn-magical');
                buttons.forEach(button => {
                    button.addEventListener('mouseenter', function() {
                        this.style.transform = 'scale(1.05)';
                    });
                    
                    button.addEventListener('mouseleave', function() {
                        this.style.transform = 'scale(1)';
                    });
                });

                // إدارة القائمة الجانبية للموبايل
                const sidebarToggle = document.getElementById('sidebarToggle');
                const mobileMenu = document.getElementById('mobileMenu');
                const closeMobileMenu = document.getElementById('closeMobileMenu');

                if (sidebarToggle && mobileMenu) {
                    sidebarToggle.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        mobileMenu.classList.remove('translate-x-full');
                        
                        // إضافة تأثير بصري للأيقونة
                        const icon = sidebarToggle.querySelector('i');
                        if (icon) {
                            icon.classList.add('animate-spin');
                            setTimeout(() => {
                                icon.classList.remove('animate-spin');
                            }, 300);
                        }
                    });
                }

                if (closeMobileMenu && mobileMenu) {
                    closeMobileMenu.addEventListener('click', function(e) {
                        e.preventDefault();
                        mobileMenu.classList.add('translate-x-full');
                    });
                }

                // إغلاق القائمة الجانبية عند النقر خارجها
                if (mobileMenu) {
                    document.addEventListener('click', function(event) {
                        if (!mobileMenu.contains(event.target) && !sidebarToggle.contains(event.target)) {
                            mobileMenu.classList.add('translate-x-full');
                        }
                    });
                }

                // إدارة قائمة المستخدم
                const userMenuBtn = document.getElementById('userMenuBtn');
                const userMenu = document.getElementById('userMenu');

                if (userMenuBtn && userMenu) {
                    userMenuBtn.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        userMenu.classList.toggle('hidden');
                        
                        // إضافة تأثير بصري للسهم
                        const arrow = userMenuBtn.querySelector('.fa-chevron-down');
                        if (arrow) {
                            arrow.classList.toggle('rotate-180');
                        }
                    });
                }

                // إغلاق قائمة المستخدم عند النقر خارجها
                document.addEventListener('click', function(event) {
                    if (userMenuBtn && userMenu && !userMenuBtn.contains(event.target) && !userMenu.contains(event.target)) {
                        userMenu.classList.add('hidden');
                        const arrow = userMenuBtn.querySelector('.fa-chevron-down');
                        if (arrow) {
                            arrow.classList.remove('rotate-180');
                        }
                    }
                });

                // إدارة سلة الطلبات
                const cartButton = document.getElementById('cartButton');
                const cartMenu = document.getElementById('cartMenu');

                if (cartButton && cartMenu) {
                    cartButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        cartMenu.classList.toggle('hidden');

                        // إضافة تأثير بصري للسلة
                        const cart = cartButton.querySelector('.fa-shopping-cart');
                        if (cart) {
                            cart.classList.add('animate-bounce');
                            setTimeout(() => {
                                cart.classList.remove('animate-bounce');
                            }, 500);
                        }
                    });

                    // إغلاق قائمة سلة الطلبات عند النقر خارجها
                    document.addEventListener('click', function(event) {
                        if (!cartButton.contains(event.target) && !cartMenu.contains(event.target)) {
                            cartMenu.classList.add('hidden');
                        }
                    });
                }

                // إدارة الإشعارات
                const notificationBtn = document.getElementById('notificationBtn');
                const notificationsMenu = document.getElementById('notificationsMenu');

                if (notificationBtn && notificationsMenu) {
                    notificationBtn.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        notificationsMenu.classList.toggle('hidden');

                        // إضافة تأثير بصري للجرس
                        const bell = notificationBtn.querySelector('.fa-bell');
                        if (bell) {
                            bell.classList.add('animate-pulse');
                            setTimeout(() => {
                                bell.classList.remove('animate-pulse');
                            }, 300);
                        }
                    });
                    
                    // إغلاق الإشعارات عند النقر خارجها
                    document.addEventListener('click', function(event) {
                        if (!notificationBtn.contains(event.target) && !notificationsMenu.contains(event.target)) {
                            notificationsMenu.classList.add('hidden');
                        }
                    });
                }

                console.log('All scripts initialized successfully!');

            }, 100);
        });

        // دوال مساعدة
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-md shadow-lg transition-all duration-300 transform translate-x-full`;
            
            const bgColor = type === 'success' ? 'bg-green-500' : 
                           type === 'error' ? 'bg-red-500' : 
                           type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500';
            
            notification.classList.add(bgColor, 'text-white');
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${type === 'success' ? 'fa-check' : type === 'error' ? 'fa-times' : type === 'warning' ? 'fa-exclamation' : 'fa-info'} ml-2"></i>
                    <span>${message}</span>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);
            
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        function addToCart(itemId, itemName, itemPrice) {
            let cartItems = JSON.parse(localStorage.getItem('cartItems')) || [];
            
            const existingItem = cartItems.find(item => item.id === itemId);
            
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cartItems.push({
                    id: itemId,
                    name: itemName,
                    price: parseFloat(itemPrice),
                    quantity: 1
                });
            }
            
            localStorage.setItem('cartItems', JSON.stringify(cartItems));
            
            // تحديث عدد العناصر
            const cartCount = document.querySelector('.cart-count');
            if (cartCount) {
                cartCount.textContent = cartItems.length;
                cartCount.classList.add('animate-pulse', 'bg-green-500');
                setTimeout(() => {
                    cartCount.classList.remove('animate-pulse');
                }, 1000);
            }
            
            showNotification('تم إضافة العنصر إلى السلة', 'success');
            
            // تأثير بصري على زر السلة
            const cartButton = document.getElementById('cartButton');
            if (cartButton) {
                const cart = cartButton.querySelector('.fa-shopping-cart');
                if (cart) {
                    cart.classList.add('animate-bounce', 'text-green-500');
                    setTimeout(() => {
                        cart.classList.remove('animate-bounce', 'text-green-500');
                    }, 500);
                }
            }
        }

        function showSuccess(message) {
            showNotification(message, 'success');
        }

        function showError(message) {
            showNotification(message, 'error');
        }

        function showWarning(message) {
            showNotification(message, 'warning');
        }

        function markAllNotificationsAsRead() {
            showNotification('تم تعليم جميع الإشعارات كمقروءة', 'success');
        }
    </script>
</body>
</html>
