<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class PermissionSeeder extends Seeder
{
    public function run()
    {
        // إنشاء الصلاحيات المفصلة
        $permissions = [
            // إدارة المستخدمين
            'users.view' => 'عرض قائمة المستخدمين',
            'users.view.details' => 'عرض تفاصيل المستخدم',
            'users.create' => 'إضافة مستخدمين جدد',
            'users.edit' => 'تعديل بيانات المستخدمين',
            'users.edit.status' => 'تفعيل/إلغاء تفعيل المستخدمين',
            'users.delete' => 'حذف المستخدمين',
            'users.permissions' => 'إدارة صلاحيات المستخدمين',
            'users.export' => 'تصدير بيانات المستخدمين',

            // إدارة القائمة
            'menu.view' => 'عرض قائمة الطعام',
            'menu.view.details' => 'عرض تفاصيل عنصر القائمة',
            'menu.create' => 'إضافة عناصر جديدة للقائمة',
            'menu.edit' => 'تعديل عناصر القائمة',
            'menu.edit.price' => 'تعديل أسعار القائمة',
            'menu.edit.availability' => 'تغيير حالة توفر العناصر',
            'menu.delete' => 'حذف عناصر من القائمة',
            'menu.export' => 'تصدير قائمة الطعام',

            // إدارة الطلبات
            'orders.view' => 'عرض قائمة الطلبات',
            'orders.view.details' => 'عرض تفاصيل الطلب',
            'orders.view.all' => 'عرض جميع الطلبات (وليس فقط طلبات المستخدم)',
            'orders.create' => 'إنشاء طلبات جديدة',
            'orders.edit' => 'تعديل الطلبات',
            'orders.edit.items' => 'تعديل عناصر الطلب',
            'orders.edit.customer' => 'تعديل بيانات العميل',
            'orders.delete' => 'حذف الطلبات',
            'orders.cancel' => 'إلغاء الطلبات',
            'orders.status' => 'تغيير حالة الطلبات',
            'orders.payment' => 'إدارة مدفوعات الطلبات',
            'orders.print' => 'طباعة الطلبات والفواتير',
            'orders.export' => 'تصدير بيانات الطلبات',

            // إدارة الحجوزات
            'reservations.view' => 'عرض قائمة الحجوزات',
            'reservations.view.details' => 'عرض تفاصيل الحجز',
            'reservations.view.all' => 'عرض جميع الحجوزات',
            'reservations.create' => 'إنشاء حجوزات جديدة',
            'reservations.edit' => 'تعديل الحجوزات',
            'reservations.edit.time' => 'تعديل وقت الحجز',
            'reservations.edit.table' => 'تغيير طاولة الحجز',
            'reservations.delete' => 'حذف الحجوزات',
            'reservations.cancel' => 'إلغاء الحجوزات',
            'reservations.status' => 'تغيير حالة الحجوزات',
            'reservations.confirm' => 'تأكيد الحجوزات',
            'reservations.export' => 'تصدير بيانات الحجوزات',

            // إدارة المخزون
            'inventory.view' => 'عرض قائمة المخزون',
            'inventory.view.details' => 'عرض تفاصيل عنصر المخزون',
            'inventory.view.transactions' => 'عرض حركات المخزون',
            'inventory.create' => 'إضافة عناصر جديدة للمخزون',
            'inventory.edit' => 'تعديل بيانات المخزون',
            'inventory.edit.quantity' => 'تعديل كميات المخزون',
            'inventory.edit.price' => 'تعديل أسعار المخزون',
            'inventory.delete' => 'حذف عناصر من المخزون',
            'inventory.transfer' => 'نقل المخزون',
            'inventory.adjust' => 'تسوية المخزون',
            'inventory.export' => 'تصدير بيانات المخزون',
            'inventory.import' => 'استيراد بيانات المخزون',

            // إدارة المكونات
            'ingredients.view' => 'عرض قائمة المكونات',
            'ingredients.view.details' => 'عرض تفاصيل المكون',
            'ingredients.create' => 'إضافة مكونات جديدة',
            'ingredients.edit' => 'تعديل بيانات المكونات',
            'ingredients.edit.recipe' => 'تعديل وصفات المكونات',
            'ingredients.delete' => 'حذف المكونات',
            'ingredients.export' => 'تصدير بيانات المكونات',

            // إدارة المصروفات
            'expenses.view' => 'عرض قائمة المصروفات',
            'expenses.view.details' => 'عرض تفاصيل المصروف',
            'expenses.view.all' => 'عرض جميع المصروفات',
            'expenses.create' => 'إضافة مصروفات جديدة',
            'expenses.edit' => 'تعديل المصروفات',
            'expenses.edit.amount' => 'تعديل مبلغ المصروف',
            'expenses.edit.category' => 'تعديل فئة المصروف',
            'expenses.delete' => 'حذف المصروفات',
            'expenses.approve' => 'الموافقة على المصروفات',
            'expenses.export' => 'تصدير بيانات المصروفات',

            // التقارير
            'reports.view' => 'عرض التقارير الأساسية',
            'reports.view.detailed' => 'عرض التقارير المفصلة',
            'reports.financial' => 'عرض التقارير المالية',
            'reports.financial.profit' => 'عرض تقارير الأرباح والخسائر',
            'reports.sales' => 'عرض تقارير المبيعات',
            'reports.sales.daily' => 'تقارير المبيعات اليومية',
            'reports.sales.monthly' => 'تقارير المبيعات الشهرية',
            'reports.inventory' => 'عرض تقارير المخزون',
            'reports.inventory.low' => 'تقارير المخزون المنخفض',
            'reports.customers' => 'تقارير العملاء',
            'reports.employees' => 'تقارير الموظفين',
            'reports.export' => 'تصدير التقارير',
            'reports.print' => 'طباعة التقارير',

            // إدارة الطاولات
            'tables.view' => 'عرض قائمة الطاولات',
            'tables.view.details' => 'عرض تفاصيل الطاولة',
            'tables.create' => 'إضافة طاولات جديدة',
            'tables.edit' => 'تعديل بيانات الطاولات',
            'tables.edit.capacity' => 'تعديل سعة الطاولة',
            'tables.edit.location' => 'تعديل موقع الطاولة',
            'tables.delete' => 'حذف الطاولات',
            'tables.status' => 'تغيير حالة الطاولات',
            'tables.reserve' => 'حجز الطاولات',
            'tables.free' => 'تحرير الطاولات',

            // إدارة المدفوعات
            'payments.view' => 'عرض قائمة المدفوعات',
            'payments.view.details' => 'عرض تفاصيل المدفوعة',
            'payments.view.all' => 'عرض جميع المدفوعات',
            'payments.create' => 'إنشاء مدفوعات جديدة',
            'payments.edit' => 'تعديل المدفوعات',
            'payments.edit.amount' => 'تعديل مبلغ المدفوعة',
            'payments.edit.method' => 'تعديل طريقة الدفع',
            'payments.delete' => 'حذف المدفوعات',
            'payments.refund' => 'استرداد المدفوعات',
            'payments.export' => 'تصدير بيانات المدفوعات',

            // إدارة الإشعارات
            'notifications.view' => 'عرض الإشعارات',
            'notifications.view.all' => 'عرض جميع الإشعارات',
            'notifications.create' => 'إنشاء إشعارات جديدة',
            'notifications.edit' => 'تعديل الإشعارات',
            'notifications.send' => 'إرسال الإشعارات',
            'notifications.send.all' => 'إرسال إشعارات جماعية',
            'notifications.send.targeted' => 'إرسال إشعارات مستهدفة',
            'notifications.delete' => 'حذف الإشعارات',
            'notifications.schedule' => 'جدولة الإشعارات',

            // الإعدادات
            'settings.view' => 'عرض الإعدادات',
            'settings.edit' => 'تعديل الإعدادات العامة',
            'settings.edit.system' => 'تعديل إعدادات النظام',
            'settings.edit.security' => 'تعديل إعدادات الأمان',
            'settings.edit.appearance' => 'تعديل إعدادات المظهر',
            'settings.backup' => 'إنشاء نسخ احتياطية',
            'settings.restore' => 'استعادة النسخ الاحتياطية',

            // لوحة التحكم
            'dashboard.admin' => 'الوصول للوحة تحكم المدير',
            'dashboard.employee' => 'الوصول للوحة تحكم الموظف',
            'dashboard.analytics' => 'عرض التحليلات في لوحة التحكم',
            'dashboard.statistics' => 'عرض الإحصائيات المفصلة',
        ];

        // إنشاء الصلاحيات
        foreach ($permissions as $name => $description) {
            Permission::firstOrCreate([
                'name' => $name,
                'guard_name' => 'web'
            ]);
        }

        // إنشاء الأدوار الأساسية
        $adminRole = Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'web']);
        $employeeRole = Role::firstOrCreate(['name' => 'employee', 'guard_name' => 'web']);
        $managerRole = Role::firstOrCreate(['name' => 'manager', 'guard_name' => 'web']);

        // إنشاء أدوار مخصصة أكثر تفصيلاً
        $cashierRole = Role::firstOrCreate(['name' => 'cashier', 'guard_name' => 'web']); // أمين الصندوق
        $waiterRole = Role::firstOrCreate(['name' => 'waiter', 'guard_name' => 'web']); // النادل
        $kitchenRole = Role::firstOrCreate(['name' => 'kitchen', 'guard_name' => 'web']); // المطبخ
        $inventoryRole = Role::firstOrCreate(['name' => 'inventory_manager', 'guard_name' => 'web']); // مدير المخزون
        $salesRole = Role::firstOrCreate(['name' => 'sales_manager', 'guard_name' => 'web']); // مدير المبيعات
        $supervisorRole = Role::firstOrCreate(['name' => 'supervisor', 'guard_name' => 'web']); // المشرف

        // إعطاء جميع الصلاحيات للمدير
        $adminRole->givePermissionTo(Permission::all());

        // إعطاء صلاحيات محددة للموظف العادي (صلاحيات أساسية فقط)
        $employeePermissions = [
            // الطلبات - عرض وإنشاء فقط
            'orders.view', 'orders.view.details', 'orders.create', 'orders.status',
            // الحجوزات - عرض وإنشاء فقط
            'reservations.view', 'reservations.view.details', 'reservations.create', 'reservations.status', 'reservations.confirm',
            // المخزون - عرض فقط
            'inventory.view', 'inventory.view.details',
            // الطاولات - عرض وتغيير الحالة
            'tables.view', 'tables.view.details', 'tables.status', 'tables.reserve', 'tables.free',
            // المدفوعات - عرض وإنشاء
            'payments.view', 'payments.view.details', 'payments.create',
            // لوحة التحكم
            'dashboard.employee'
        ];
        $employeeRole->givePermissionTo($employeePermissions);

        // إعطاء صلاحيات للمدير المساعد (صلاحيات واسعة لكن محدودة)
        $managerPermissions = [
            'dashboard.admin', 'dashboard.analytics', // الوصول للوحة الإدارة مع التحليلات

            // إدارة المستخدمين - بدون حذف أو إدارة صلاحيات
            'users.view', 'users.view.details', 'users.create', 'users.edit', 'users.edit.status', 'users.export',

            // إدارة القائمة - صلاحيات كاملة
            'menu.view', 'menu.view.details', 'menu.create', 'menu.edit', 'menu.edit.price', 'menu.edit.availability', 'menu.export',

            // إدارة الطلبات - صلاحيات كاملة ما عدا الحذف
            'orders.view', 'orders.view.details', 'orders.view.all', 'orders.create', 'orders.edit', 'orders.edit.items', 'orders.edit.customer',
            'orders.cancel', 'orders.status', 'orders.payment', 'orders.print', 'orders.export',

            // إدارة الحجوزات - صلاحيات كاملة ما عدا الحذف
            'reservations.view', 'reservations.view.details', 'reservations.view.all', 'reservations.create', 'reservations.edit',
            'reservations.edit.time', 'reservations.edit.table', 'reservations.cancel', 'reservations.status', 'reservations.confirm', 'reservations.export',

            // إدارة المخزون - صلاحيات كاملة ما عدا الحذف
            'inventory.view', 'inventory.view.details', 'inventory.view.transactions', 'inventory.create', 'inventory.edit',
            'inventory.edit.quantity', 'inventory.edit.price', 'inventory.transfer', 'inventory.adjust', 'inventory.export', 'inventory.import',

            // إدارة المصروفات - بدون موافقة أو حذف
            'expenses.view', 'expenses.view.details', 'expenses.view.all', 'expenses.create', 'expenses.edit', 'expenses.edit.amount', 'expenses.edit.category', 'expenses.export',

            // التقارير - معظم التقارير
            'reports.view', 'reports.view.detailed', 'reports.sales', 'reports.sales.daily', 'reports.sales.monthly',
            'reports.inventory', 'reports.inventory.low', 'reports.customers', 'reports.employees', 'reports.export', 'reports.print',

            // إدارة الطاولات - صلاحيات كاملة ما عدا الحذف
            'tables.view', 'tables.view.details', 'tables.create', 'tables.edit', 'tables.edit.capacity', 'tables.edit.location',
            'tables.status', 'tables.reserve', 'tables.free',

            // إدارة المدفوعات - بدون حذف أو استرداد
            'payments.view', 'payments.view.details', 'payments.view.all', 'payments.create', 'payments.edit', 'payments.edit.amount', 'payments.edit.method', 'payments.export',

            // إدارة الإشعارات - صلاحيات كاملة
            'notifications.view', 'notifications.view.all', 'notifications.create', 'notifications.edit', 'notifications.send',
            'notifications.send.all', 'notifications.send.targeted', 'notifications.schedule'
        ];
        $managerRole->givePermissionTo($managerPermissions);

        // صلاحيات أمين الصندوق (التركيز على المدفوعات والطلبات)
        $cashierPermissions = [
            'dashboard.employee',
            'orders.view', 'orders.view.details', 'orders.create', 'orders.status', 'orders.payment', 'orders.print',
            'payments.view', 'payments.view.details', 'payments.create', 'payments.edit.method',
            'tables.view', 'tables.view.details', 'tables.status',
            'menu.view', 'menu.view.details'
        ];
        $cashierRole->givePermissionTo($cashierPermissions);

        // صلاحيات النادل (التركيز على الطلبات والحجوزات والطاولات)
        $waiterPermissions = [
            'dashboard.employee',
            'orders.view', 'orders.view.details', 'orders.create', 'orders.edit.items', 'orders.status',
            'reservations.view', 'reservations.view.details', 'reservations.create', 'reservations.edit.time', 'reservations.status', 'reservations.confirm',
            'tables.view', 'tables.view.details', 'tables.status', 'tables.reserve', 'tables.free',
            'menu.view', 'menu.view.details', 'menu.edit.availability',
            'payments.view', 'payments.view.details'
        ];
        $waiterRole->givePermissionTo($waiterPermissions);

        // صلاحيات المطبخ (التركيز على الطلبات والمخزون)
        $kitchenPermissions = [
            'dashboard.employee',
            'orders.view', 'orders.view.details', 'orders.status', // تغيير حالة الطلب للتحضير
            'menu.view', 'menu.view.details', 'menu.edit.availability', // تغيير توفر الأصناف
            'inventory.view', 'inventory.view.details', 'inventory.edit.quantity', // تحديث كميات المخزون
            'ingredients.view', 'ingredients.view.details', 'ingredients.edit'
        ];
        $kitchenRole->givePermissionTo($kitchenPermissions);

        // صلاحيات مدير المخزون (التركيز على المخزون والمكونات)
        $inventoryPermissions = [
            'dashboard.admin', // وصول للوحة الإدارة
            'inventory.view', 'inventory.view.details', 'inventory.view.transactions', 'inventory.create', 'inventory.edit',
            'inventory.edit.quantity', 'inventory.edit.price', 'inventory.transfer', 'inventory.adjust', 'inventory.export', 'inventory.import',
            'ingredients.view', 'ingredients.view.details', 'ingredients.create', 'ingredients.edit', 'ingredients.edit.recipe', 'ingredients.export',
            'menu.view', 'menu.view.details', 'menu.edit.availability', // تحديث توفر الأصناف
            'reports.view', 'reports.inventory', 'reports.inventory.low', 'reports.export'
        ];
        $inventoryRole->givePermissionTo($inventoryPermissions);

        // صلاحيات مدير المبيعات (التركيز على الطلبات والتقارير)
        $salesPermissions = [
            'dashboard.admin', 'dashboard.analytics', // وصول للوحة الإدارة مع التحليلات
            'orders.view', 'orders.view.details', 'orders.view.all', 'orders.create', 'orders.edit', 'orders.edit.items', 'orders.edit.customer',
            'orders.cancel', 'orders.status', 'orders.payment', 'orders.print', 'orders.export',
            'menu.view', 'menu.view.details', 'menu.create', 'menu.edit', 'menu.edit.price', 'menu.export',
            'reports.view', 'reports.view.detailed', 'reports.sales', 'reports.sales.daily', 'reports.sales.monthly',
            'reports.customers', 'reports.export', 'reports.print',
            'payments.view', 'payments.view.details', 'payments.view.all', 'payments.export',
            'notifications.view', 'notifications.create', 'notifications.send'
        ];
        $salesRole->givePermissionTo($salesPermissions);

        // صلاحيات المشرف (صلاحيات واسعة للإشراف اليومي)
        $supervisorPermissions = [
            'dashboard.admin', 'dashboard.analytics',
            'orders.view', 'orders.view.details', 'orders.view.all', 'orders.create', 'orders.edit', 'orders.cancel', 'orders.status', 'orders.payment', 'orders.print',
            'reservations.view', 'reservations.view.details', 'reservations.view.all', 'reservations.create', 'reservations.edit',
            'reservations.edit.time', 'reservations.edit.table', 'reservations.cancel', 'reservations.status', 'reservations.confirm',
            'tables.view', 'tables.view.details', 'tables.edit', 'tables.status', 'tables.reserve', 'tables.free',
            'menu.view', 'menu.view.details', 'menu.edit.availability',
            'inventory.view', 'inventory.view.details', 'inventory.edit.quantity',
            'payments.view', 'payments.view.details', 'payments.view.all', 'payments.create', 'payments.edit.method',
            'reports.view', 'reports.sales', 'reports.sales.daily', 'reports.inventory.low',
            'notifications.view', 'notifications.create', 'notifications.send'
        ];
        $supervisorRole->givePermissionTo($supervisorPermissions);

        // تعيين الأدوار للمستخدمين الموجودين
        $adminUsers = User::where('user_type', 'admin')->get();
        foreach ($adminUsers as $user) {
            $user->assignRole('admin');
        }

        $employeeUsers = User::where('user_type', 'employee')->get();
        foreach ($employeeUsers as $user) {
            $user->assignRole('employee');
        }
    }
}
