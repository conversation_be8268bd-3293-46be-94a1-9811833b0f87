<?php

namespace App\Http\Controllers;

use App\Models\MenuItem;
use App\Models\Recipe;
use App\Models\Ingredient;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Storage;

class MenuController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth', ['except' => ['publicIndex']]);
    }

    // في MenuController.php
    public function index()
    {
        $categories = [
            'main' => 'الأطباق الرئيسية',
            'appetizer' => 'المقبلات',
            'dessert' => 'الحلويات',
            'beverage' => 'المشروبات'
        ];

        $menuItems = MenuItem::where('is_available', true)
            ->orderBy('category')
            ->get();

        return view('menu.index', compact('menuItems', 'categories'));
    }

    public function show($id)
    {
        $item = MenuItem::with('recipe.ingredient')->findOrFail($id);

        // Get related items from the same category
        $relatedItems = MenuItem::where('category', $item->category)
            ->where('item_id', '!=', $id)
            ->where('is_available', true)
            ->take(4)
            ->get();

        return view('menu.show', compact('item', 'relatedItems'));
    }

    // Admin Methods
    public function adminIndex(Request $request)
    {
        $query = MenuItem::query();

        // البحث
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = $request->search;
            $query->where(function($q) use ($searchTerm) {
                $q->where('name', 'like', '%' . $searchTerm . '%')
                  ->orWhere('description', 'like', '%' . $searchTerm . '%');
            });
        }

        // التصفية حسب الفئة
        if ($request->has('category') && !empty($request->category) && $request->category !== 'all') {
            $query->where('category', $request->category);
        }

        // الترتيب
        $query->orderBy('category')
              ->orderBy('name');

        // ترقيم الصفحات
        $menuItems = $query->paginate(15)->withQueryString();

        // قائمة الفئات
        $categories = [
            'main' => 'الأطباق الرئيسية',
            'appetizer' => 'المقبلات',
            'dessert' => 'الحلويات',
            'beverage' => 'المشروبات'
        ];

        return view('admin.menu.index', compact('menuItems', 'categories'));
    }

    public function create()
    {
        $ingredients = Ingredient::where('is_active', true)->orderBy('name')->get();

        return view('admin.menu.create', compact('ingredients'));
    }

    public function store(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100|unique:menu_items,name',
            'price' => 'required|numeric|min:0',
            'category' => 'required|in:main,appetizer,dessert,beverage',
            'description' => 'nullable|string|max:500',
            'is_available' => 'boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'ingredients' => 'required|array|min:1',
            'ingredients.*.id' => 'required|exists:ingredients,ingredient_id',
            'ingredients.*.quantity' => 'required|numeric|min:0.01',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        DB::beginTransaction();

        try {
            // Create menu item
            $menuItem = MenuItem::create([
                'name' => $request->name,
                'price' => $request->price,
                'category' => $request->category,
                'description' => $request->description,
                'is_available' => $request->has('is_available'),
            ]);
            if ($request->hasFile('image')) {
                $path = $request->file('image')->store('menu-items', 'public');
                $menuItem->image_path = $path;
            }
            // Upload image if provided
            if ($request->hasFile('image')) {
                $imagePath = $request->file('image')->store('menu', 'public');
                $menuItem->image_path = $imagePath;
                $menuItem->save();
            }

            // Add ingredients to recipe
            foreach ($request->ingredients as $ingredient) {
                Recipe::create([
                    'menu_item_id' => $menuItem->item_id,
                    'ingredient_id' => $ingredient['id'],
                    'quantity' => $ingredient['quantity'],
                ]);
            }

            DB::commit();

            return redirect()->route('admin.menu')->with('success', 'تم إضافة عنصر القائمة بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'حدث خطأ أثناء إضافة عنصر القائمة: ' . $e->getMessage())->withInput();
        }
    }

    public function edit($id)
    {
        $menuItem = MenuItem::with('recipe.ingredient')->findOrFail($id);
        $ingredients = Ingredient::where('is_active', true)->orderBy('name')->get();

        return view('admin.menu.edit', compact('menuItem', 'ingredients'));
    }

    public function update(Request $request, $id)
    {

        $menuItem = MenuItem::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100|unique:menu_items,name,' . $id . ',item_id',
            'price' => 'required|numeric|min:0',
            'category' => 'required|in:main,appetizer,dessert,beverage',
            'description' => 'nullable|string|max:500',
            'is_available' => 'boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'ingredients' => 'required|array|min:1',
            'ingredients.*.id' => 'required|exists:ingredients,ingredient_id',
            'ingredients.*.quantity' => 'required|numeric|min:0.01',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        DB::beginTransaction();

        try {
            // Update menu item
            $menuItem->update([
                'name' => $request->name,
                'price' => $request->price,
                'category' => $request->category,
                'description' => $request->description,
                'is_available' => $request->has('is_available'),
            ]);

            // Upload image if provided
            if ($request->hasFile('image')) {
                // Delete old image if exists
                if ($menuItem->image_path) {
                    Storage::disk('public')->delete($menuItem->image_path);
                }

                $imagePath = $request->file('image')->store('menu', 'public');
                $menuItem->image_path = $imagePath;
                $menuItem->save();
            }

            // Update recipe
            // First, delete existing recipe
            Recipe::where('menu_item_id', $menuItem->item_id)->delete();

            // Then add new ingredients
            foreach ($request->ingredients as $ingredient) {
                Recipe::create([
                    'menu_item_id' => $menuItem->item_id,
                    'ingredient_id' => $ingredient['id'],
                    'quantity' => $ingredient['quantity'],
                ]);
            }

            DB::commit();

            return redirect()->route('admin.menu')->with('success', 'تم تحديث عنصر القائمة بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'حدث خطأ أثناء تحديث عنصر القائمة: ' . $e->getMessage())->withInput();
        }
    }

    public function adminShow($id)
    {
        $menuItem = MenuItem::with('recipe.ingredient')->findOrFail($id);

        // حساب عدد الطلبات وتقييمات المنتج
        $menuItem->orders_count = DB::table('order_items')->where('menu_item_id', $id)->count();
        $menuItem->average_rating = $menuItem->reviews()->avg('rating') ?? 0;

        // حساب تكلفة المكونات
        $ingredientsCost = 0;
        foreach ($menuItem->recipe as $recipe) {
            $ingredientsCost += $recipe->quantity * $recipe->ingredient->cost_per_unit;
        }
        $menuItem->ingredients_cost = $ingredientsCost;

        return view('admin.menu.show', compact('menuItem'));
    }

    public function delete($id)
    {
        $menuItem = MenuItem::findOrFail($id);

        // Check if the menu item is used in any orders
        $hasOrders = DB::table('order_items')->where('menu_item_id', $id)->exists();

        if ($hasOrders) {
            return redirect()->back()->with('error', 'لا يمكن حذف هذا العنصر لأنه مرتبط بطلبات');
        }

        // Delete the menu item and its recipe
        DB::beginTransaction();

        try {
            // Delete image if exists
            if ($menuItem->image_path) {
                Storage::disk('public')->delete($menuItem->image_path);
            }

            // Delete recipe
            Recipe::where('menu_item_id', $id)->delete();

            // Delete menu item
            $menuItem->delete();

            DB::commit();

            return redirect()->route('admin.menu')->with('success', 'تم حذف عنصر القائمة بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'حدث خطأ أثناء حذف عنصر القائمة: ' . $e->getMessage());
        }
    }

    // Customer Methods
    public function customerIndex()
    {
        $categories = [
            'main' => 'الأطباق الرئيسية',
            'appetizer' => 'المقبلات',
            'dessert' => 'الحلويات',
            'beverage' => 'المشروبات'
        ];

        $menuItems = MenuItem::where('is_available', true)
            ->orderBy('category')
            ->get()
            ->groupBy('category');

        return view('customer.menu.index', compact('menuItems', 'categories'));
    }

    public function customerShow($id)
    {
        $item = MenuItem::with('recipe.ingredient')->findOrFail($id);

        // Get related items from the same category
        $relatedItems = MenuItem::where('category', $item->category)
            ->where('item_id', '!=', $id)
            ->where('is_available', true)
            ->take(4)
            ->get();

        return view('customer.menu.show', compact('item', 'relatedItems'));
    }

    // Public Methods - للزوار
    public function publicIndex()
    {
        $categories = [
            'main' => 'الأطباق الرئيسية',
            'appetizer' => 'المقبلات',
            'dessert' => 'الحلويات',
            'beverage' => 'المشروبات'
        ];

        $menuItems = MenuItem::where('is_available', true)
            ->orderBy('category')
            ->get()
            ->groupBy('category');

        // الأطباق المميزة
        $featuredItems = MenuItem::where('is_available', true)
            ->inRandomOrder()
            ->take(6)
            ->get();

        return view('menu.index', compact('menuItems', 'categories', 'featuredItems'));
    }
}