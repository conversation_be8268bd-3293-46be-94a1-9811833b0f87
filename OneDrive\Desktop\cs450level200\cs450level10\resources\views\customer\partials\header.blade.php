<header id="navbar" class="w-full bg-white dark:bg-gray-800 shadow-md z-30 transition-all duration-300">
    <!-- القائمة العلوية -->
    <div class="container mx-auto px-4 py-2">
        <div class="flex justify-between items-center">
            <!-- الشعار -->
            <div class="flex items-center">
                <div class="text-2xl font-bold text-primary flex items-center">
                    <i class="fas fa-utensils ml-2"></i>
                    <span>Eat Hub</span>
                </div>
            </div>

            <!-- القائمة الرئيسية - تظهر فقط على الشاشات المتوسطة والكبيرة -->
            <nav class="hidden md:flex space-x-6 space-x-reverse items-center">
                <a href="{{ route('customer.index') }}" class="nav-link px-2 py-2 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary">الرئيسية</a>
                <a href="{{ route('customer.menu') }}" class="nav-link px-2 py-2 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary">قائمة الطعام</a>
                <a href="{{ route('customer.offers.index') }}" class="nav-link px-2 py-2 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary">العروض</a>
                <a href="{{ route('customer.reservations') }}" class="nav-link px-2 py-2 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary">الحجوزات</a>
                <a href="{{ route('customer.orders') }}" class="nav-link px-2 py-2 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary">طلباتي</a>
            </nav>

            <!-- أزرار إجراءات المستخدم -->
            <div class="flex items-center space-x-3 space-x-reverse">
                <!-- زر البحث -->
                <div class="relative">
                    <button id="searchToggle" class="p-2 rounded-full text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700">
                        <i class="fas fa-search"></i>
                    </button>

                    <!-- نافذة البحث -->
                    <div id="searchModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
                        <div class="flex items-start justify-center pt-20">
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl mx-4">
                                <div class="p-6">
                                    <div class="flex items-center justify-between mb-4">
                                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white">البحث</h3>
                                        <button id="closeSearch" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>

                                    <div class="relative">
                                        <input type="text"
                                               id="searchInput"
                                               placeholder="ابحث عن الأطباق، الطلبات، الحجوزات..."
                                               class="w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                                        <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                                            <i class="fas fa-search text-gray-400"></i>
                                        </div>
                                    </div>

                                    <!-- نتائج البحث -->
                                    <div id="searchResults" class="mt-4 max-h-96 overflow-y-auto hidden">
                                        <!-- سيتم ملؤها بـ JavaScript -->
                                    </div>

                                    <!-- رسالة عدم وجود نتائج -->
                                    <div id="noResults" class="mt-4 text-center text-gray-500 dark:text-gray-400 hidden">
                                        <i class="fas fa-search text-3xl mb-2"></i>
                                        <p>لا توجد نتائج للبحث</p>
                                    </div>

                                    <!-- حالة التحميل -->
                                    <div id="searchLoading" class="mt-4 text-center hidden">
                                        <i class="fas fa-spinner fa-spin text-primary text-xl"></i>
                                        <p class="text-gray-600 dark:text-gray-400 mt-2">جاري البحث...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- زر الوضع المظلم -->
                <button id="darkModeToggle" class="p-2 rounded-full text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700" onclick="window.simpleDarkModeToggle()">
                    <i id="darkModeIcon" class="fas fa-moon"></i>
                </button>

                <!-- زر سلة التسوق - يظهر فقط للمستخدمين المسجلين -->
                @auth
                <div id="cartButton" class="relative">
                    <a href="{{ route('customer.cart') }}" class="p-2 rounded-full text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700">
                        <i class="fas fa-shopping-cart"></i>
                        <span id="cartCount" class="absolute -top-1 -right-1 bg-primary text-white rounded-full text-xs w-5 h-5 flex items-center justify-center">0</span>
                    </a>
                </div>
                @endauth

                <!-- زر الإشعارات - يظهر فقط للمستخدمين المسجلين -->
                @auth
                <div id="notificationsButton" class="relative">
                    <button id="notificationsToggle" class="p-2 rounded-full text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700">
                        <i class="fas fa-bell"></i>
                        <span id="notificationCount" class="absolute -top-1 -right-1 bg-primary text-white rounded-full text-xs w-5 h-5 flex items-center justify-center hidden">0</span>
                    </button>

                    <!-- قائمة الإشعارات -->
                    <div id="notificationsDropdown" class="absolute left-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg py-2 z-10 hidden">
                        <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                            <h3 class="text-sm font-medium text-gray-800 dark:text-white">الإشعارات</h3>
                            <button id="markAllRead" class="text-xs text-primary hover:text-primary/80">تحديد الكل كمقروء</button>
                        </div>

                        <div id="notificationsList" class="max-h-96 overflow-y-auto">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>

                        <div class="px-4 py-3 border-t border-gray-200 dark:border-gray-700">
                            <a href="{{ route('customer.notifications') }}" class="text-sm text-primary hover:text-primary/80">عرض جميع الإشعارات</a>
                        </div>
                    </div>
                </div>
                @endauth

                <!-- حالة تسجيل الدخول -->
                @guest
                <div id="guestControls">
                    <a href="{{ route('login') }}" class="btn-hover-effect mr-2 px-4 py-2 bg-white dark:bg-gray-700 text-primary border border-primary dark:border-primary hover:bg-primary/5 dark:hover:bg-gray-600 transition rounded-md">
                        تسجيل دخول
                    </a>
                    <a href="{{ route('register') }}" class="btn-hover-effect px-4 py-2 bg-primary text-white hover:bg-primary/90 transition rounded-md">
                        إنشاء حساب
                    </a>
                </div>
                @endguest

                <!-- معلومات المستخدم بعد تسجيل الدخول -->
                @auth
                <div id="userControls" class="relative">
                    <button id="userMenuBtn" class="flex items-center p-1 rounded-full text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700">
                        <div class="w-8 h-8 rounded-full bg-primary flex items-center justify-center text-white font-bold">
                            {{ auth()->check() ? substr(auth()->user()->first_name ?? 'أ', 0, 1) : 'أ' }}
                        </div>
                        <span class="mr-2 hidden md:block">{{ auth()->check() ? (auth()->user()->first_name ?? 'المستخدم') . ' ' . (auth()->user()->last_name ?? '') : 'المستخدم' }}</span>
                        <i class="fas fa-chevron-down text-xs mr-1 hidden md:block"></i>
                    </button>

                    <!-- قائمة المستخدم -->
                    <div id="userMenu" class="absolute left-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg py-2 z-10 hidden">
                        <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                            <p class="text-sm font-medium text-gray-800 dark:text-white">{{ auth()->check() ? (auth()->user()->first_name ?? 'المستخدم') . ' ' . (auth()->user()->last_name ?? '') : 'المستخدم' }}</p>
                            <p class="text-xs text-gray-500 dark:text-gray-400">{{ auth()->check() ? (auth()->user()->email ?? '<EMAIL>') : '<EMAIL>' }}</p>
                        </div>
                        <a href="{{ route('customer.dashboard') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i class="fas fa-tachometer-alt ml-2"></i>لوحة التحكم
                        </a>
                        <a href="{{ route('customer.profile') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i class="fas fa-user-circle ml-2"></i>الملف الشخصي
                        </a>
                        <a href="{{ route('customer.orders') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i class="fas fa-clipboard-list ml-2"></i>طلباتي
                        </a>
                        <a href="{{ route('customer.reservations') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i class="fas fa-calendar-check ml-2"></i>حجوزاتي
                        </a>
                        <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
                        <form action="{{ route('logout') }}" method="POST" class="block">
                            @csrf
                            <button type="submit" class="block w-full text-right px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                <i class="fas fa-sign-out-alt ml-2"></i>تسجيل الخروج
                            </button>
                        </form>
                    </div>
                </div>
                @endauth

                <!-- زر القائمة للجوال -->
                <button id="mobileMenuToggle" class="md:hidden p-2 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
    </div>

    @include('customer.partials.mobile-menu')
</header>
