@extends('layouts.admin')

@section('title', 'تقرير أداء الموظفين')

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- العنوان الرئيسي -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 dark:text-white">تقرير أداء الموظفين</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-2">تحليل شامل لأداء الموظفين ومبيعاتهم</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <button onclick="window.print()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center">
                <i class="fas fa-print mr-2"></i>
                طباعة
            </button>
            <button onclick="exportToExcel()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center">
                <i class="fas fa-file-excel mr-2"></i>
                تصدير Excel
            </button>
        </div>
    </div>

    <!-- فلاتر التاريخ -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
        <form method="GET" action="{{ route('admin.reports.employee-performance') }}" class="flex flex-wrap items-end gap-4">
            <div class="flex-1 min-w-48">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">من تاريخ</label>
                <input type="date" name="start_date" value="{{ $startDate->format('Y-m-d') }}" 
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
            </div>
            <div class="flex-1 min-w-48">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">إلى تاريخ</label>
                <input type="date" name="end_date" value="{{ $endDate->format('Y-m-d') }}" 
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
            </div>
            <div>
                <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg flex items-center">
                    <i class="fas fa-search mr-2"></i>
                    فلترة
                </button>
            </div>
        </form>
    </div>

    <!-- الإحصائيات العامة -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <!-- إجمالي الموظفين -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">إجمالي الموظفين</p>
                    <h3 class="text-2xl font-bold text-gray-800 dark:text-white">{{ $totalEmployees }}</h3>
                    <p class="text-blue-500 text-sm mt-2 flex items-center">
                        <i class="fas fa-users mr-1"></i>
                        <span>موظف نشط</span>
                    </p>
                </div>
                <div class="rounded-full bg-blue-100 dark:bg-blue-900/30 p-3">
                    <i class="fas fa-users text-blue-500 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- إجمالي الطلبات -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">إجمالي الطلبات</p>
                    <h3 class="text-2xl font-bold text-gray-800 dark:text-white">{{ number_format($totalOrdersAllEmployees) }}</h3>
                    <p class="text-green-500 text-sm mt-2 flex items-center">
                        <i class="fas fa-shopping-cart mr-1"></i>
                        <span>طلب</span>
                    </p>
                </div>
                <div class="rounded-full bg-green-100 dark:bg-green-900/30 p-3">
                    <i class="fas fa-shopping-cart text-green-500 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- إجمالي المبيعات -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">إجمالي المبيعات</p>
                    <h3 class="text-2xl font-bold text-gray-800 dark:text-white">{{ number_format($totalSalesAllEmployees, 2) }} د.ل</h3>
                    <p class="text-purple-500 text-sm mt-2 flex items-center">
                        <i class="fas fa-money-bill-wave mr-1"></i>
                        <span>مبيعات</span>
                    </p>
                </div>
                <div class="rounded-full bg-purple-100 dark:bg-purple-900/30 p-3">
                    <i class="fas fa-money-bill-wave text-purple-500 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- متوسط الطلبات لكل موظف -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">متوسط الطلبات/موظف</p>
                    <h3 class="text-2xl font-bold text-gray-800 dark:text-white">{{ number_format($avgOrdersPerEmployee, 1) }}</h3>
                    <p class="text-orange-500 text-sm mt-2 flex items-center">
                        <i class="fas fa-calculator mr-1"></i>
                        <span>طلب/موظف</span>
                    </p>
                </div>
                <div class="rounded-full bg-orange-100 dark:bg-orange-900/30 p-3">
                    <i class="fas fa-calculator text-orange-500 text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- أفضل موظف -->
    @if($topEmployee)
    <div class="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-gray-800 dark:to-gray-700 rounded-lg p-6 mb-6 border-2 border-yellow-200 dark:border-yellow-600">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="rounded-full bg-yellow-100 dark:bg-yellow-900/30 p-4 mr-4">
                    <i class="fas fa-trophy text-yellow-500 text-2xl"></i>
                </div>
                <div>
                    <h3 class="text-xl font-bold text-gray-800 dark:text-white">🏆 أفضل موظف في الفترة</h3>
                    <p class="text-lg font-semibold text-yellow-600 dark:text-yellow-400">{{ $topEmployee['employee_name'] }}</p>
                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ $topEmployee['employee_email'] }}</p>
                </div>
            </div>
            <div class="text-right">
                <p class="text-sm text-gray-600 dark:text-gray-400">إجمالي المبيعات</p>
                <p class="text-2xl font-bold text-green-600">{{ number_format($topEmployee['total_sales'], 2) }} د.ل</p>
                <p class="text-sm text-gray-600 dark:text-gray-400">{{ $topEmployee['orders_created'] }} طلب</p>
            </div>
        </div>
    </div>
    @endif

    <!-- جدول أداء الموظفين -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white">تفاصيل أداء الموظفين</h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead>
                    <tr class="bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                        <th class="py-3 px-4 text-right font-medium">#</th>
                        <th class="py-3 px-4 text-right font-medium">الموظف</th>
                        <th class="py-3 px-4 text-right font-medium">الطلبات المدخلة</th>
                        <th class="py-3 px-4 text-right font-medium">الطلبات المكتملة</th>
                        <th class="py-3 px-4 text-right font-medium">قيد التحضير</th>
                        <th class="py-3 px-4 text-right font-medium">الملغية</th>
                        <th class="py-3 px-4 text-right font-medium">إجمالي المبيعات</th>
                        <th class="py-3 px-4 text-right font-medium">متوسط قيمة الطلب</th>
                        <th class="py-3 px-4 text-right font-medium">معدل الإكمال</th>
                        <th class="py-3 px-4 text-right font-medium">متوسط وقت الإكمال</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse($employeePerformance as $index => $employee)
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 {{ $index === 0 ? 'bg-yellow-50 dark:bg-yellow-900/20' : '' }}">
                        <td class="py-4 px-4 text-gray-600 dark:text-gray-300">
                            @if($index === 0)
                                <i class="fas fa-crown text-yellow-500 mr-1"></i>
                            @endif
                            {{ $index + 1 }}
                        </td>
                        <td class="py-4 px-4">
                            <div>
                                <p class="font-medium text-gray-800 dark:text-white">{{ $employee['employee_name'] }}</p>
                                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $employee['employee_email'] }}</p>
                            </div>
                        </td>
                        <td class="py-4 px-4 text-gray-600 dark:text-gray-300 font-medium">
                            {{ number_format($employee['orders_created']) }}
                        </td>
                        <td class="py-4 px-4">
                            <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                                {{ number_format($employee['orders_completed']) }}
                            </span>
                        </td>
                        <td class="py-4 px-4">
                            <span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                                {{ number_format($employee['orders_in_progress']) }}
                            </span>
                        </td>
                        <td class="py-4 px-4">
                            <span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400">
                                {{ number_format($employee['cancelled_orders']) }}
                            </span>
                        </td>
                        <td class="py-4 px-4 font-bold text-green-600 dark:text-green-400">
                            {{ number_format($employee['total_sales'], 2) }} د.ل
                        </td>
                        <td class="py-4 px-4 text-gray-600 dark:text-gray-300">
                            {{ number_format($employee['avg_order_value'], 2) }} د.ل
                        </td>
                        <td class="py-4 px-4">
                            <div class="flex items-center">
                                <div class="w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-2 mr-2">
                                    <div class="bg-{{ $employee['completion_rate'] >= 80 ? 'green' : ($employee['completion_rate'] >= 60 ? 'yellow' : 'red') }}-500 h-2 rounded-full" 
                                         style="width: {{ min($employee['completion_rate'], 100) }}%"></div>
                                </div>
                                <span class="text-sm font-medium text-gray-600 dark:text-gray-300">{{ $employee['completion_rate'] }}%</span>
                            </div>
                        </td>
                        <td class="py-4 px-4 text-gray-600 dark:text-gray-300">
                            @if($employee['avg_completion_time'] > 0)
                                {{ $employee['avg_completion_time'] }} دقيقة
                            @else
                                <span class="text-gray-400">-</span>
                            @endif
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="10" class="py-8 text-center text-gray-500 dark:text-gray-400">
                            <i class="fas fa-users text-4xl mb-4"></i>
                            <p>لا توجد بيانات موظفين في هذه الفترة</p>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>

<style>
@media print {
    .no-print { display: none !important; }
    body { font-size: 12px; }
    .container { max-width: none; margin: 0; padding: 0; }
}

.card-hover {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}
</style>

<script>
function exportToExcel() {
    // تصدير البيانات إلى Excel
    const table = document.querySelector('table');
    const wb = XLSX.utils.table_to_book(table, {sheet: "أداء الموظفين"});
    XLSX.writeFile(wb, 'تقرير_أداء_الموظفين_' + new Date().toISOString().split('T')[0] + '.xlsx');
}

// تحديث الصفحة عند تغيير التاريخ
document.addEventListener('DOMContentLoaded', function() {
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(input => {
        input.addEventListener('change', function() {
            // يمكن إضافة تحديث تلقائي هنا إذا رغبت
        });
    });
});
</script>
@endsection
