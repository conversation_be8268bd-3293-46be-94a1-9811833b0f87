# 🍽️ Eat Hub - نظام إدارة المطاعم

## Restaurant Management System

نظام شامل لإدارة المطاعم مبني بـ Laravel وأحدث تقنيات الويب، مصمم خصيصاً للمطاعم العربية.

## ✨ المميزات

-   **إدارة المستخدمين**: أدوار المدير والموظف والعميل
-   **إدارة القائمة**: إنشاء وإدارة عناصر القائمة مع الصور
-   **إدارة الطلبات**: معالجة طلبات العملاء والمدفوعات
-   **نظام الحجوزات**: حجز وإدارة الطاولات
-   **إدارة المخزون**: تتبع المكونات والمخزون
-   **التقارير المالية**: تتبع الإيرادات وإدارة المصروفات
-   **دعم متعدد اللغات**: العربية والإنجليزية
-   **تصميم متجاوب**: يعمل على جميع الأجهزة
-   **تسجيل دخول اجتماعي**: Google, Facebook, Apple
-   **نظام صلاحيات متقدم**: باستخدام Spatie Permissions

## 🚀 البدء السريع

### للمستعجلين (5 دقائق):

```bash
# Windows
setup.bat

# Linux/Mac
chmod +x setup.sh && ./setup.sh
```

### أو اتبع [دليل الإعداد التفصيلي](SETUP_NEW_DEVICE.md)

## 📋 المتطلبات

-   **PHP 8.1+** مع الإضافات: mysql, mbstring, xml, curl, zip, gd
-   **MySQL 5.7+** أو **MariaDB**
-   **Composer** (مدير حزم PHP)
-   **Node.js 16+** و **npm**
-   **خادم ويب** (Apache/Nginx أو XAMPP)

## 🔧 التثبيت اليدوي

### 1. نسخ المشروع

```bash
git clone [repository-url]
cd cs450level10
```

### 2. تثبيت التبعيات

```bash
composer install
npm install
```

### 3. إعداد البيئة

```bash
cp .env.example .env
php artisan key:generate
```

### 4. إعداد قاعدة البيانات

```sql
CREATE DATABASE eat_hub_new CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 5. تعديل ملف .env

```env
DB_DATABASE=eat_hub_new
DB_USERNAME=root
DB_PASSWORD=your_password
```

### 6. تشغيل الهجرات والبذور

```bash
php artisan migrate
php artisan db:seed
```

### 7. إعداد التخزين والأصول

```bash
php artisan storage:link
npm run build
```

### 8. تشغيل الخادم

```bash
php artisan serve
```

## 🔑 بيانات تسجيل الدخول الافتراضية

**المدير الرئيسي:**

-   البريد الإلكتروني: `<EMAIL>`
-   كلمة المرور: `A178a2002`

## 🛠️ أدوات مساعدة

### فحص النظام

```bash
php check_system.php
```

### مسح الذاكرة المؤقتة

```bash
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

### إعادة تعيين قاعدة البيانات

```bash
php artisan migrate:fresh --seed
```

## 📁 هيكل المشروع

```
cs450level10/
├── app/                    # كود التطبيق
├── database/
│   ├── migrations/         # هجرات قاعدة البيانات
│   └── seeders/           # بذور البيانات
├── resources/
│   ├── views/             # قوالب Blade
│   ├── js/                # ملفات JavaScript
│   └── css/               # ملفات CSS
├── public/                # الملفات العامة
├── storage/               # ملفات التخزين
├── setup.bat              # إعداد تلقائي (Windows)
├── setup.sh               # إعداد تلقائي (Linux/Mac)
├── check_system.php       # فحص النظام
└── SETUP_NEW_DEVICE.md    # دليل الإعداد التفصيلي
```

## 🌐 الوصول للنظام

بعد التشغيل، افتح المتصفح على:
**http://localhost:8000**

## 🆘 استكشاف الأخطاء

### مشكلة قاعدة البيانات

```bash
# تحقق من الاتصال
php artisan tinker
DB::connection()->getPdo();
```

### مشكلة الصلاحيات (Linux/Mac)

```bash
chmod -R 775 storage bootstrap/cache
```

### مشكلة مفتاح التطبيق

```bash
php artisan key:generate
```

## 📚 الوثائق الإضافية

-   [دليل الإعداد التفصيلي](SETUP_NEW_DEVICE.md)
-   [البدء السريع](QUICK_START.md)
-   [إعداد الصلاحيات](PERMISSIONS_GUIDE.md)
-   [نظام الاتصال](CONTACT_SYSTEM_README.md)

## 🤝 المساهمة

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل إرسال Pull Request.

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

إذا واجهت أي مشاكل:

1. تحقق من ملفات السجل في `storage/logs/`
2. شغل `php check_system.php` للتحقق من النظام
3. راجع [دليل استكشاف الأخطاء](SETUP_NEW_DEVICE.md#استكشاف-الأخطاء)

---

**تم تطويره بـ ❤️ لخدمة المطاعم العربية**
