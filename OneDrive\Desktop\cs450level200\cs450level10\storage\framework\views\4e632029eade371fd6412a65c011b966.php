<?php $__env->startSection('title', 'لوحة تحكم الموظف'); ?>

<?php $__env->startSection('content'); ?>
<div class="h-full bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 relative overflow-y-auto">
    <!-- عناصر زخرفية متحركة -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-indigo-600/20 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-purple-400/20 to-pink-600/20 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
    </div>

    <div class="container mx-auto px-4 py-6 relative z-10">
        <!-- ترحيب مبدع -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full shadow-xl mb-4 animate-pulse">
                <i class="fas fa-user-tie text-2xl text-white"></i>
            </div>
            <h1 class="text-3xl md:text-4xl font-bold text-gray-800 dark:text-white mb-3">
                مرحباً <?php echo e(auth()->user()->first_name); ?> 👋
            </h1>
            <p class="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                أهلاً بك في لوحة تحكم الموظف - مركز إدارة عملياتك اليومية
            </p>
            <div class="w-20 h-1 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full mx-auto mt-3"></div>
        </div>

        <!-- بطاقات الإحصائيات -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <!-- إجمالي الطلبات -->
            <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-5 shadow-lg border border-white/20 dark:border-gray-700/20 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                <div class="flex items-center justify-between mb-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-md">
                        <i class="fas fa-shopping-cart text-white text-lg"></i>
                    </div>
                    <span class="text-2xl font-bold text-blue-600 dark:text-blue-400"><?php echo e($todayStats['totalOrders'] ?? 0); ?></span>
                </div>
                <h3 class="text-base font-semibold text-gray-800 dark:text-white mb-1">طلبات اليوم</h3>
                <p class="text-xs text-gray-600 dark:text-gray-400">طلب جديد</p>
            </div>

            <!-- إجمالي الحجوزات -->
            <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-5 shadow-lg border border-white/20 dark:border-gray-700/20 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                <div class="flex items-center justify-between mb-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center shadow-md">
                        <i class="fas fa-calendar-check text-white text-lg"></i>
                    </div>
                    <span class="text-2xl font-bold text-purple-600 dark:text-purple-400"><?php echo e($todayStats['totalReservations'] ?? 0); ?></span>
                </div>
                <h3 class="text-base font-semibold text-gray-800 dark:text-white mb-1">حجوزات اليوم</h3>
                <p class="text-xs text-gray-600 dark:text-gray-400">حجز مؤكد</p>
            </div>

            <!-- إجمالي المبيعات -->
            <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-5 shadow-lg border border-white/20 dark:border-gray-700/20 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                <div class="flex items-center justify-between mb-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-md">
                        <i class="fas fa-dollar-sign text-white text-lg"></i>
                    </div>
                    <span class="text-2xl font-bold text-green-600 dark:text-green-400"><?php echo e(number_format($todayStats['totalSales'] ?? 0, 0)); ?></span>
                </div>
                <h3 class="text-base font-semibold text-gray-800 dark:text-white mb-1">مبيعات اليوم</h3>
                <p class="text-xs text-gray-600 dark:text-gray-400">ريال سعودي</p>
            </div>

            <!-- الطاولات المتاحة -->
            <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-5 shadow-lg border border-white/20 dark:border-gray-700/20 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                <div class="flex items-center justify-between mb-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-600 rounded-xl flex items-center justify-center shadow-md">
                        <i class="fas fa-chair text-white text-lg"></i>
                    </div>
                    <span class="text-2xl font-bold text-orange-600 dark:text-orange-400"><?php echo e($todayStats['availableTables'] ?? 0); ?></span>
                </div>
                <h3 class="text-base font-semibold text-gray-800 dark:text-white mb-1">طاولات متاحة</h3>
                <p class="text-xs text-gray-600 dark:text-gray-400">من إجمالي <?php echo e($todayStats['totalTables'] ?? 0); ?></p>
            </div>
        </div>

        <!-- الوصول السريع -->
        <div class="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20 dark:border-gray-700/20 mb-6">
            <div class="text-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-2">🚀 الوصول السريع</h2>
                <p class="text-sm text-gray-600 dark:text-gray-400">اختصارات مفيدة لتسهيل عملك اليومي</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- إدارة الطلبات -->
                <a href="<?php echo e(route('employee.orders')); ?>" class="group bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-4 rounded-xl border border-blue-200/50 dark:border-blue-700/50 hover:shadow-md transform hover:scale-105 transition-all duration-300">
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-shopping-cart text-white text-sm"></i>
                        </div>
                        <div class="mr-3">
                            <h3 class="text-base font-bold text-gray-800 dark:text-white">إدارة الطلبات</h3>
                            <p class="text-xs text-gray-600 dark:text-gray-400">عرض ومتابعة الطلبات</p>
                        </div>
                    </div>
                    <div class="flex items-center text-blue-600 dark:text-blue-400 group-hover:text-blue-700 dark:group-hover:text-blue-300">
                        <span class="text-xs font-medium">انتقال سريع</span>
                        <i class="fas fa-arrow-left mr-2 group-hover:translate-x-1 transition-transform duration-300 text-xs"></i>
                    </div>
                </a>

                <!-- إدارة الحجوزات -->
                <a href="<?php echo e(route('employee.reservations')); ?>" class="group bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-6 rounded-2xl border border-purple-200/50 dark:border-purple-700/50 hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-calendar-check text-white"></i>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-bold text-gray-800 dark:text-white">إدارة الحجوزات</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">متابعة الحجوزات</p>
                        </div>
                    </div>
                    <div class="flex items-center text-purple-600 dark:text-purple-400 group-hover:text-purple-700 dark:group-hover:text-purple-300">
                        <span class="text-sm font-medium">انتقال سريع</span>
                        <i class="fas fa-arrow-left mr-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </div>
                </a>

                <!-- قائمة الطعام -->
                <a href="<?php echo e(route('employee.menu')); ?>" class="group bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-6 rounded-2xl border border-green-200/50 dark:border-green-700/50 hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-utensils text-white"></i>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-bold text-gray-800 dark:text-white">قائمة الطعام</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">عرض الأطباق المتاحة</p>
                        </div>
                    </div>
                    <div class="flex items-center text-green-600 dark:text-green-400 group-hover:text-green-700 dark:group-hover:text-green-300">
                        <span class="text-sm font-medium">انتقال سريع</span>
                        <i class="fas fa-arrow-left mr-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </div>
                </a>

                <!-- إدارة الطاولات -->
                <a href="<?php echo e(route('employee.tables')); ?>" class="group bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 p-6 rounded-2xl border border-orange-200/50 dark:border-orange-700/50 hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-chair text-white"></i>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-bold text-gray-800 dark:text-white">إدارة الطاولات</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">متابعة حالة الطاولات</p>
                        </div>
                    </div>
                    <div class="flex items-center text-orange-600 dark:text-orange-400 group-hover:text-orange-700 dark:group-hover:text-orange-300">
                        <span class="text-sm font-medium">انتقال سريع</span>
                        <i class="fas fa-arrow-left mr-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </div>
                </a>

                <!-- التقارير -->
                <a href="<?php echo e(route('employee.reports')); ?>" class="group bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-indigo-900/20 dark:to-blue-900/20 p-6 rounded-2xl border border-indigo-200/50 dark:border-indigo-700/50 hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-indigo-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-chart-bar text-white"></i>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-bold text-gray-800 dark:text-white">التقارير</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">عرض الإحصائيات</p>
                        </div>
                    </div>
                    <div class="flex items-center text-indigo-600 dark:text-indigo-400 group-hover:text-indigo-700 dark:group-hover:text-indigo-300">
                        <span class="text-sm font-medium">انتقال سريع</span>
                        <i class="fas fa-arrow-left mr-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </div>
                </a>

                <!-- الملف الشخصي -->
                <a href="<?php echo e(route('employee.profile')); ?>" class="group bg-gradient-to-r from-gray-50 to-slate-50 dark:from-gray-900/20 dark:to-slate-900/20 p-6 rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-gray-500 to-slate-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-user text-white"></i>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-bold text-gray-800 dark:text-white">الملف الشخصي</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">إدارة الحساب</p>
                        </div>
                    </div>
                    <div class="flex items-center text-gray-600 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300">
                        <span class="text-sm font-medium">انتقال سريع</span>
                        <i class="fas fa-arrow-left mr-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>

<style>
@keyframes blob {
    0% { transform: translate(0px, 0px) scale(1); }
    33% { transform: translate(30px, -50px) scale(1.1); }
    66% { transform: translate(-20px, 20px) scale(0.9); }
    100% { transform: translate(0px, 0px) scale(1); }
}

.animate-blob {
    animation: blob 7s infinite;
}

.animation-delay-2000 {
    animation-delay: 2s;
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('employee.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\cs450level200\cs450level10\resources\views/employee/dashboard.blade.php ENDPATH**/ ?>