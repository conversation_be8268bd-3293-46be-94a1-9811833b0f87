@extends('layouts.admin')

@section('title', 'إضافة منتج جديد - لوحة تحكم Eat Hub')

@section('page-title', 'إضافة منتج جديد')

@section('content')
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
    <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-6">إضافة منتج جديد</h2>
    
    @if ($errors->any())
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
        <div class="flex items-center mb-2">
            <i class="fas fa-exclamation-circle ml-2 text-xl"></i>
            <strong class="font-bold">يرجى تصحيح الأخطاء التالية:</strong>
        </div>
        <ul class="list-disc list-inside">
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
    @endif
    
    <form action="{{ route('admin.menu.store') }}" method="POST" enctype="multipart/form-data">
        @csrf
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">اسم المنتج</label>
                <input type="text" name="name" id="name" value="{{ old('name') }}" class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary" required>
            </div>
            
            <div>
                <label for="price" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">السعر (د.ل)</label>
                <input type="number" name="price" id="price" value="{{ old('price') }}" step="0.01" min="0" class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary" required>
            </div>
            
            <div>
                <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">التصنيف</label>
                <select name="category" id="category" class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary" required>
                    <option value="">اختر التصنيف</option>
                    <option value="main" {{ old('category') == 'main' ? 'selected' : '' }}>الأطباق الرئيسية</option>
                    <option value="appetizer" {{ old('category') == 'appetizer' ? 'selected' : '' }}>المقبلات</option>
                    <option value="dessert" {{ old('category') == 'dessert' ? 'selected' : '' }}>الحلويات</option>
                    <option value="beverage" {{ old('category') == 'beverage' ? 'selected' : '' }}>المشروبات</option>
                </select>
            </div>
            
            <div>
                <label for="image" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">صورة المنتج</label>
                <input type="file" name="image" id="image" accept="image/*" class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary">
                <p class="text-xs text-gray-500 mt-1">الحد الأقصى لحجم الملف: 2 ميجابايت. الصيغ المدعومة: JPG، PNG</p>
            </div>
            
            <div class="md:col-span-2">
                <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">وصف المنتج</label>
                <textarea name="description" id="description" rows="4" class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary">{{ old('description') }}</textarea>
            </div>
            
            <div class="md:col-span-2">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">المكونات</h3>
                <div id="ingredients-container">
                    <div class="ingredient-row grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المكون</label>
                            <select name="ingredients[0][id]" class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary" required>
                                <option value="">اختر المكون</option>
                                @foreach($ingredients as $ingredient)
                                    <option value="{{ $ingredient->ingredient_id }}">{{ $ingredient->name }} ({{ $ingredient->unit }})</option>
                                @endforeach
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الكمية</label>
                            <input type="number" name="ingredients[0][quantity]" step="0.01" min="0.01" class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary" required>
                        </div>
                        <div class="flex items-end">
                            <button type="button" class="remove-ingredient px-3 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 hidden">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <button type="button" id="add-ingredient" class="mt-2 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600">
                    <i class="fas fa-plus ml-2"></i>
                    <span>إضافة مكون</span>
                </button>
            </div>
            
            <div class="md:col-span-2">
                <label class="flex items-center mt-6">
                    <input type="hidden" name="is_available" value="0">
                    <input type="checkbox" name="is_available" id="is_available" value="1" class="form-checkbox h-5 w-5 text-primary" {{ old('is_available', true) ? 'checked' : '' }}>
                    <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">متوفر للطلب</span>
                </label>
            </div>
        </div>
        
        <div class="mt-6 flex justify-end">
            <a href="{{ route('admin.menu') }}" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 ml-2">إلغاء</a>
            <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark">
                <i class="fas fa-save ml-2"></i>
                <span>حفظ المنتج</span>
            </button>
        </div>
    </form>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const ingredientsContainer = document.getElementById('ingredients-container');
        const addIngredientBtn = document.getElementById('add-ingredient');
        let ingredientCount = 1;
        
        // إضافة مكون جديد
        addIngredientBtn.addEventListener('click', function() {
            const ingredientRow = document.createElement('div');
            ingredientRow.className = 'ingredient-row grid grid-cols-1 md:grid-cols-3 gap-4 mb-4';
            
            const ingredientOptions = Array.from(document.querySelector('select[name="ingredients[0][id]"]').options)
                .map(option => `<option value="${option.value}">${option.text}</option>`)
                .join('');
            
            ingredientRow.innerHTML = `
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المكون</label>
                    <select name="ingredients[${ingredientCount}][id]" class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary" required>
                        ${ingredientOptions}
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الكمية</label>
                    <input type="number" name="ingredients[${ingredientCount}][quantity]" step="0.01" min="0.01" class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary" required>
                </div>
                <div class="flex items-end">
                    <button type="button" class="remove-ingredient px-3 py-2 bg-red-500 text-white rounded-md hover:bg-red-600">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
            
            ingredientsContainer.appendChild(ingredientRow);
            ingredientCount++;
            
            // إظهار زر الحذف للصف الأول إذا كان هناك أكثر من صف
            if (ingredientCount > 1) {
                document.querySelector('.remove-ingredient.hidden')?.classList.remove('hidden');
            }
        });
        
        // حذف مكون
        ingredientsContainer.addEventListener('click', function(e) {
            if (e.target.closest('.remove-ingredient')) {
                const row = e.target.closest('.ingredient-row');
                row.remove();
                
                // إعادة ترقيم المكونات
                const rows = document.querySelectorAll('.ingredient-row');
                rows.forEach((row, index) => {
                    const select = row.querySelector('select');
                    const input = row.querySelector('input[type="number"]');
                    
                    select.name = `ingredients[${index}][id]`;
                    input.name = `ingredients[${index}][quantity]`;
                });
                
                ingredientCount = rows.length;
                
                // إخفاء زر الحذف للصف الأول إذا كان هناك صف واحد فقط
                if (ingredientCount === 1) {
                    document.querySelector('.remove-ingredient')?.classList.add('hidden');
                }
            }
        });
    });
</script>
@endsection
