<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    public function run()
    {
        // إنشاء مستخدم مدير جديد
        $admin = User::create([
            'first_name' => 'مدير',
            'last_name' => 'النظام',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'phone' => '0500000000',
            'user_type' => 'admin',
            'is_active' => true
        ]);
    }
}
