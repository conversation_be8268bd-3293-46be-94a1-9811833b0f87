<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;
class NotificationController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    // Admin notifications
    public function adminIndex(Request $request)
    {

        // الأدمن يرى جميع الإشعارات مع معلومات المستخدم
        $query = Notification::with('user');

        // تطبيق الفلتر إذا تم تحديده
        if ($request->has('filter')) {
            if ($request->filter === 'read') {
                $query->where('is_read', true);
            } elseif ($request->filter === 'unread') {
                $query->where('is_read', false);
            }
        }

        $notifications = $query->orderBy('created_at', 'desc')
            ->paginate(20)
            ->withQueryString();

        // إحصائيات الإشعارات
        $stats = [
            'total' => Notification::count(),
            'unread' => Notification::where('is_read', false)->count(),
            'read' => Notification::where('is_read', true)->count(),
            'system' => Notification::where('type', 'system')->count(),
            'order' => Notification::where('type', 'order')->count(),
            'reservation' => Notification::where('type', 'reservation')->count(),
            'inventory' => Notification::where('type', 'inventory')->count(),
            'contact' => Notification::where('type', 'contact')->count(),
        ];

        return view('admin.notifications.index', compact('notifications', 'stats'));
    }

    // Employee notifications
    public function employeeIndex(Request $request)
    {
        $query = Notification::where('user_id', Auth::id());

        // تطبيق الفلتر إذا تم تحديده
        if ($request->has('filter')) {
            if ($request->filter === 'read') {
                $query->where('is_read', true);
            } elseif ($request->filter === 'unread') {
                $query->where('is_read', false);
            }
        }

        $notifications = $query->orderBy('created_at', 'desc')
            ->paginate(20)
            ->withQueryString();

        return view('employee.notifications.index', compact('notifications'));
    }

    // Customer notifications
    public function customerIndex()
    {
        $notifications = Notification::where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('customer.notifications.index', compact('notifications'));
    }

    public function markAsRead($id)
    {
        $user = Auth::user();

        if ($user->user_type === 'admin') {
            // الأدمن يمكنه تحديد أي إشعار كمقروء
            $notification = Notification::findOrFail($id);
        } else {
            // المستخدمون الآخرون يحددون إشعاراتهم فقط
            $notification = Notification::where('user_id', Auth::id())
                ->findOrFail($id);
        }

        $notification->update(['is_read' => true]);

        return redirect()->back()->with('success', 'تم تحديث حالة الإشعار');
    }

    public function markAllAsRead()
    {
        // التحقق من نوع المستخدم
        $user = Auth::user();

        if ($user->user_type === 'admin') {
            // الأدمن يحدد جميع الإشعارات كمقروءة
            Notification::where('is_read', false)
                ->update(['is_read' => true]);
        } else {
            // المستخدمون الآخرون يحددون إشعاراتهم فقط
            Notification::where('user_id', Auth::id())
                ->where('is_read', false)
                ->update(['is_read' => true]);
        }

        return redirect()->back()->with('success', 'تم تحديد جميع الإشعارات كمقروءة');
    }

    public function delete($id)
    {
        $user = Auth::user();

        if ($user->user_type === 'admin') {
            // الأدمن يمكنه حذف أي إشعار
            $notification = Notification::findOrFail($id);
        } else {
            // المستخدمون الآخرون يحذفون إشعاراتهم فقط
            $notification = Notification::where('user_id', Auth::id())
                ->findOrFail($id);
        }

        $notification->delete();

        return redirect()->back()->with('success', 'تم حذف الإشعار');
    }

    public function deleteAll()
    {
        Notification::where('user_id', Auth::id())->delete();

        return redirect()->back()->with('success', 'تم حذف جميع الإشعارات');
    }

    public function show($id)
    {
        $notification = Notification::findOrFail($id);

        // تحديث حالة الإشعار ليكون مقروء
        if (!$notification->is_read) {
            $notification->update(['is_read' => true]);
        }

        return view('admin.notifications.show', compact('notification'));
    }

    // API for live notifications
    public function getUnreadCount()
    {
        $count = Notification::where('user_id', Auth::id())
            ->where('is_read', false)
            ->count();

        return response()->json(['count' => $count]);
    }

    public function getLatestNotifications()
    {
        $notifications = Notification::where('user_id', Auth::id())
            ->where('is_read', false)
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        return response()->json(['notifications' => $notifications]);
    }

    // إرسال إشعار إلى مستخدم
    public function sendNotification(Request $request)
    {
        $this->middleware('admin');

        $request->validate([
            'user_id' => 'required|exists:users,user_id',
            'message' => 'required|string|max:255',
        ]);

        Notification::create([
            'user_id' => $request->user_id,
            'message' => $request->message,
            'is_read' => false,
        ]);

        return redirect()->back()->with('success', 'تم إرسال الإشعار بنجاح');
    }

    // إرسال إشعار إلى فئة من المستخدمين
    public function sendBulkNotification(Request $request)
    {
        $this->middleware('admin');

        $request->validate([
            'user_type' => 'required|in:admin,employee,customer,all',
            'message' => 'required|string|max:255',
        ]);

        $query = DB::table('users')->where('is_active', true);

        if ($request->user_type !== 'all') {
            $query->where('user_type', $request->user_type);
        }

        $users = $query->get();

        foreach ($users as $user) {
            Notification::create([
                'user_id' => $user->user_id,
                'message' => $request->message,
                'is_read' => false,
            ]);
        }

        return redirect()->back()->with('success', 'تم إرسال الإشعار إلى ' . count($users) . ' مستخدم بنجاح');
    }

    // عرض صفحة إنشاء إشعار جديد
    public function create()
    {
        $this->middleware('admin');

        // جلب جميع المستخدمين النشطين
        $users = DB::table('users')
            ->where('is_active', true)
            ->get();

        return view('admin.notifications.create', compact('users'));
    }

    // إرسال إشعار جديد
    public function send(Request $request)
    {
        $this->middleware('admin');

        $validationRules = [
            'recipients' => 'required|string|in:all,admins,employees,staff,customers,specific',
            'title' => 'nullable|string|max:255',
            'message' => 'required|string',
            'type' => 'nullable|string|in:system,order,reservation,inventory,contact',
            'action_url' => 'nullable|string',
            'action_text' => 'nullable|string',
        ];

        // إضافة قاعدة التحقق من user_id فقط إذا كان الاختيار "specific"
        if ($request->recipients === 'specific') {
            $validationRules['user_id'] = 'required|exists:users,user_id';
        }

        $request->validate($validationRules);

        // تحديد المستخدمين المستهدفين
        $query = DB::table('users')->where('is_active', true);

        switch ($request->recipients) {
            case 'all':
                // جميع المستخدمين - لا نحتاج لتصفية إضافية
                break;
            case 'admins':
                $query->where('user_type', 'admin');
                break;
            case 'employees':
                $query->where('user_type', 'employee');
                break;
            case 'staff':
                $query->whereIn('user_type', ['admin', 'employee']);
                break;
            case 'customers':
                $query->where('user_type', 'customer');
                break;
            case 'specific':
                // مستخدم محدد
                $query->where('user_id', $request->user_id);
                break;
        }

        $users = $query->get();
        $count = 0;

        foreach ($users as $user) {
            Notification::create([
                'user_id' => $user->user_id,
                'title' => $request->title,
                'message' => $request->message,
                'type' => $request->type,
                'action_url' => $request->action_url,
                'action_text' => $request->action_text,
                'is_read' => false,
            ]);
            $count++;
        }

        return redirect()->route('admin.notifications')->with('success', 'تم إرسال الإشعار إلى ' . $count . ' مستخدم بنجاح');
    }
}