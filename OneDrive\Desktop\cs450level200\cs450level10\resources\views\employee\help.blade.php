@extends('layouts.admin')

@section('title', 'المساعدة - نظام إدارة المطعم')

@section('page-title', 'مركز المساعدة')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">مركز المساعدة</h1>
        <p class="text-gray-600 dark:text-gray-400">اعثر على إجابات لأسئلتك ودليل استخدام النظام</p>
    </div>

    @if(session('error'))
    <div class="bg-red-100 border-r-4 border-red-500 text-red-700 p-4 mb-6 rounded-md dark:bg-red-900/30 dark:text-red-500 dark:border-red-500">
        <p>{{ session('error') }}</p>
    </div>
    @endif

    <!-- البحث -->
    <div class="mb-8">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-bold text-gray-800 dark:text-white mb-4">كيف يمكننا مساعدتك؟</h2>
            <div class="relative">
                <input type="text" id="help-search" class="w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white" placeholder="ابحث عن موضوع أو سؤال...">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-search text-gray-400"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- قائمة المواضيع -->
        <div class="md:col-span-1">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden sticky top-20">
                <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-bold text-gray-800 dark:text-white">المواضيع</h2>
                </div>
                <div class="p-0">
                    <ul class="help-tabs">
                        @foreach($helpSections as $index => $section)
                        <li class="border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                            <button class="w-full px-4 py-3 text-right text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none {{ $index === 0 ? 'active-tab' : '' }}" data-tab="section-{{ $index }}">
                                <i class="fas {{ $section->icon }} ml-2"></i>{{ $section->title }}
                            </button>
                        </li>
                        @endforeach
                        <li class="border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                            <button class="w-full px-4 py-3 text-right text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none" data-tab="faq">
                                <i class="fas fa-question-circle ml-2"></i>الأسئلة الشائعة
                            </button>
                        </li>
                        <li class="border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                            <button class="w-full px-4 py-3 text-right text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none" data-tab="contact">
                                <i class="fas fa-envelope ml-2"></i>اتصل بالدعم
                            </button>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- محتوى المساعدة -->
        <div class="md:col-span-2">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <!-- أقسام المساعدة -->
                @foreach($helpSections as $index => $section)
                <div id="section-{{ $index }}-content" class="help-content {{ $index === 0 ? 'active' : 'hidden' }}">
                    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                        <h2 class="text-xl font-bold text-gray-800 dark:text-white">{{ $section->title }}</h2>
                    </div>
                    <div class="p-6">
                        <div class="prose dark:prose-invert max-w-none">
                            {!! $section->content !!}
                        </div>

                        @if($section->title == 'إدارة الطلبات')
                        <div class="mt-6 border-t border-gray-200 dark:border-gray-700 pt-6">
                            <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">كيفية إنشاء طلب جديد</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-md">
                                    <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الخطوة 1</div>
                                    <p class="text-gray-600 dark:text-gray-400">انتقل إلى صفحة "الطلبات" واضغط على زر "إنشاء طلب جديد"</p>
                                </div>
                                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-md">
                                    <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الخطوة 2</div>
                                    <p class="text-gray-600 dark:text-gray-400">اختر نوع الطلب (تناول في المطعم أو طلب خارجي)</p>
                                </div>
                                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-md">
                                    <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الخطوة 3</div>
                                    <p class="text-gray-600 dark:text-gray-400">أضف العناصر المطلوبة من قائمة الطعام</p>
                                </div>
                                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-md">
                                    <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الخطوة 4</div>
                                    <p class="text-gray-600 dark:text-gray-400">أدخل معلومات العميل وطريقة الدفع</p>
                                </div>
                            </div>

                            <div class="mt-4">
                                <a href="{{ route('employee.orders.create') }}" class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50">
                                    <i class="fas fa-plus-circle ml-2"></i>إنشاء طلب جديد
                                </a>
                            </div>
                        </div>
                        @endif

                        @if($section->title == 'إدارة الطاولات')
                        <div class="mt-6 border-t border-gray-200 dark:border-gray-700 pt-6">
                            <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">حالات الطاولات</h3>
                            <div class="space-y-3">
                                <div class="flex items-center">
                                    <span class="w-4 h-4 rounded-full bg-green-500 mr-2"></span>
                                    <span class="text-gray-700 dark:text-gray-300">متاحة - الطاولة متاحة للحجز أو الاستخدام</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="w-4 h-4 rounded-full bg-red-500 mr-2"></span>
                                    <span class="text-gray-700 dark:text-gray-300">مشغولة - الطاولة قيد الاستخدام حاليًا</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="w-4 h-4 rounded-full bg-yellow-500 mr-2"></span>
                                    <span class="text-gray-700 dark:text-gray-300">محجوزة - الطاولة محجوزة لوقت لاحق</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="w-4 h-4 rounded-full bg-gray-500 mr-2"></span>
                                    <span class="text-gray-700 dark:text-gray-300">غير متاحة - الطاولة غير متاحة للاستخدام (صيانة أو سبب آخر)</span>
                                </div>
                            </div>

                            <div class="mt-4">
                                <a href="{{ route('employee.tables') }}" class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50">
                                    <i class="fas fa-chair ml-2"></i>إدارة الطاولات
                                </a>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
                @endforeach

                <!-- الأسئلة الشائعة -->
                <div id="faq-content" class="help-content hidden">
                    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                        <h2 class="text-xl font-bold text-gray-800 dark:text-white">الأسئلة الشائعة</h2>
                    </div>
                    <div class="p-6">
                        <div class="space-y-6">
                            @if(count($faqs) > 0)
                                @foreach($faqs as $faq)
                                <div class="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden">
                                    <button class="faq-question w-full px-4 py-3 text-right text-gray-800 dark:text-white bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 focus:outline-none flex justify-between items-center">
                                        <span>{{ $faq->question }}</span>
                                        <i class="fas fa-chevron-down text-gray-500 dark:text-gray-400 transition-transform"></i>
                                    </button>
                                    <div class="faq-answer bg-white dark:bg-gray-800 p-4 hidden">
                                        <p class="text-gray-700 dark:text-gray-300">{{ $faq->answer }}</p>
                                    </div>
                                </div>
                                @endforeach
                            @else
                                <div class="text-center py-8">
                                    <div class="text-5xl text-gray-300 dark:text-gray-600 mb-4">
                                        <i class="fas fa-question-circle"></i>
                                    </div>
                                    <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">لا توجد أسئلة شائعة حاليًا</h3>
                                    <p class="text-gray-500 dark:text-gray-400">سيتم إضافة الأسئلة الشائعة قريبًا</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- اتصل بالدعم -->
                <div id="contact-content" class="help-content hidden">
                    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                        <h2 class="text-xl font-bold text-gray-800 dark:text-white">اتصل بالدعم</h2>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-700 dark:text-gray-300 mb-6">إذا لم تجد إجابة لسؤالك، يمكنك التواصل مع فريق الدعم الفني للمساعدة.</p>

                        <form action="#" method="POST" class="space-y-6">
                            @csrf
                            <div>
                                <label for="subject" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الموضوع</label>
                                <input type="text" id="subject" name="subject" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white" placeholder="أدخل موضوع الرسالة">
                            </div>

                            <div>
                                <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الرسالة</label>
                                <textarea id="message" name="message" rows="5" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white" placeholder="اكتب رسالتك هنا..."></textarea>
                            </div>

                            <div>
                                <button type="submit" class="w-full px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50">
                                    <i class="fas fa-paper-plane ml-2"></i>إرسال الرسالة
                                </button>
                            </div>
                        </form>

                        <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">طرق التواصل الأخرى</h3>

                            <div class="space-y-4">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-500 dark:text-blue-400">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    <div class="mr-4">
                                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">البريد الإلكتروني</h4>
                                        <p class="text-gray-600 dark:text-gray-400"><EMAIL></p>
                                    </div>
                                </div>

                                <div class="flex items-start">
                                    <div class="flex-shrink-0 w-10 h-10 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center text-green-500 dark:text-green-400">
                                        <i class="fas fa-phone-alt"></i>
                                    </div>
                                    <div class="mr-4">
                                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">الهاتف</h4>
                                        <p class="text-gray-600 dark:text-gray-400">+218 91-234-5678</p>
                                    </div>
                                </div>

                                <div class="flex items-start">
                                    <div class="flex-shrink-0 w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center text-purple-500 dark:text-purple-400">
                                        <i class="fas fa-headset"></i>
                                    </div>
                                    <div class="mr-4">
                                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">الدعم المباشر</h4>
                                        <p class="text-gray-600 dark:text-gray-400">متاح من 9 صباحًا حتى 5 مساءً</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // التبديل بين علامات التبويب
        const tabs = document.querySelectorAll('.help-tabs button');
        const contents = document.querySelectorAll('.help-content');

        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                const tabId = this.getAttribute('data-tab');

                // تحديث حالة التبويبات
                tabs.forEach(t => t.classList.remove('active-tab', 'bg-gray-100', 'dark:bg-gray-700', 'text-primary'));
                this.classList.add('active-tab', 'bg-gray-100', 'dark:bg-gray-700', 'text-primary');

                // تحديث المحتوى
                contents.forEach(content => {
                    if (content.id === tabId + '-content') {
                        content.classList.remove('hidden');
                        content.classList.add('active');
                    } else {
                        content.classList.add('hidden');
                        content.classList.remove('active');
                    }
                });
            });
        });

        // تبديل الأسئلة الشائعة
        const faqQuestions = document.querySelectorAll('.faq-question');

        faqQuestions.forEach(question => {
            question.addEventListener('click', function() {
                const answer = this.nextElementSibling;
                const icon = this.querySelector('i');

                if (answer.classList.contains('hidden')) {
                    answer.classList.remove('hidden');
                    icon.classList.add('transform', 'rotate-180');
                } else {
                    answer.classList.add('hidden');
                    icon.classList.remove('transform', 'rotate-180');
                }
            });
        });

        // البحث في المساعدة
        const searchInput = document.getElementById('help-search');

        if (searchInput) {
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();

                // يمكن إضافة منطق البحث هنا
                console.log('Searching for:', searchTerm);
            });
        }
    });
</script>
@endsection
