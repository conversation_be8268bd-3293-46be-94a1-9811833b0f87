@extends('customer.layouts.app')

@section('title', 'تفاصيل العرض')

@section('content')
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="mb-8">
        <nav class="text-sm text-gray-500 dark:text-gray-400 mb-4">
            <a href="{{ route('customer.dashboard') }}" class="hover:text-primary">الرئيسية</a>
            <span class="mx-2">/</span>
            <span>تفاصيل العرض</span>
        </nav>
        <h1 class="text-3xl font-bold text-darkText dark:text-white">{{ $offer['title'] }}</h1>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- صورة العرض -->
        <div class="lg:col-span-2">
            <div class="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-lg">
                <img src="{{ $offer['image_url'] ?? 'https://images.unsplash.com/photo-1555939594-58d7cb561ad1' }}" alt="{{ $offer['title'] }}" class="w-full h-96 object-cover">

                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <span class="bg-primary text-white px-3 py-1 rounded-full text-sm">{{ $offer['type'] }}</span>
                        @if($offer['discount'])
                            <span class="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold">خصم {{ $offer['discount'] }}%</span>
                        @endif
                    </div>

                    <h2 class="text-2xl font-bold text-darkText dark:text-white mb-4">{{ $offer['title'] }}</h2>

                    <div class="prose dark:prose-invert max-w-none">
                        <p class="text-gray-600 dark:text-gray-300 leading-relaxed">{{ $offer['description'] }}</p>

                        @if(isset($offer['features']) && $offer['features'])
                            <h3 class="text-lg font-semibold text-darkText dark:text-white mt-6 mb-3">مميزات العرض:</h3>
                            <ul class="list-disc list-inside space-y-2 text-gray-600 dark:text-gray-300">
                                @foreach($offer['features'] as $feature)
                                    <li>{{ $feature }}</li>
                                @endforeach
                            </ul>
                        @endif

                        @if(isset($offer['conditions']) && $offer['conditions'])
                            <h3 class="text-lg font-semibold text-darkText dark:text-white mt-6 mb-3">شروط العرض:</h3>
                            <ul class="list-disc list-inside space-y-2 text-gray-600 dark:text-gray-300">
                                @foreach($offer['conditions'] as $condition)
                                    <li>{{ $condition }}</li>
                                @endforeach
                            </ul>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات العرض والحجز -->
        <div class="lg:col-span-1">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 sticky top-8">
                <div class="mb-6">
                    <h3 class="text-xl font-bold text-darkText dark:text-white mb-4">معلومات العرض</h3>

                    <div class="space-y-3">
                        @if(isset($offer['start_date']) && $offer['start_date'])
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-300">تاريخ البداية:</span>
                            <span class="font-semibold text-darkText dark:text-white">{{ \Carbon\Carbon::parse($offer['start_date'])->format('Y/m/d') }}</span>
                        </div>
                        @endif

                        @if(isset($offer['end_date']) && $offer['end_date'])
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-300">تاريخ الانتهاء:</span>
                            <span class="font-semibold text-darkText dark:text-white">{{ \Carbon\Carbon::parse($offer['end_date'])->format('Y/m/d') }}</span>
                        </div>
                        @else
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-300">المدة:</span>
                            <span class="font-semibold text-green-600">عرض مفتوح</span>
                        </div>
                        @endif

                        @if(isset($offer['original_price']) && $offer['original_price'])
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-300">السعر الأصلي:</span>
                                <span class="line-through text-gray-500">{{ $offer['original_price'] }} د.ل</span>
                            </div>
                        @endif

                        @if(isset($offer['discounted_price']) && $offer['discounted_price'])
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-300">السعر بعد الخصم:</span>
                                <span class="font-bold text-primary text-lg">{{ $offer['discounted_price'] }} د.ل</span>
                            </div>
                        @endif

                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-300">الحالة:</span>
                            <span class="font-semibold {{ $offer['is_active'] ? 'text-green-600' : 'text-red-600' }}">
                                {{ $offer['is_active'] ? 'متاح' : 'منتهي' }}
                            </span>
                        </div>
                    </div>
                </div>

                @if($offer['is_active'])
                    <div class="space-y-3">
                        @if($offer['type'] === 'حجز' || $offer['slug'] === 'family-discount' || $offer['slug'] === 'music-nights')
                            <button onclick="openReservationModal()"
                                    class="w-full bg-primary hover:bg-primary/90 text-white text-center py-3 px-4 rounded-lg transition font-semibold">
                                احجز الآن
                            </button>
                        @endif

                        @if($offer['type'] === 'طعام' || $offer['slug'] === 'new-dishes')
                            <a href="{{ route('customer.menu') }}"
                               class="w-full bg-accent hover:bg-accent/90 text-darkText text-center py-3 px-4 rounded-lg transition font-semibold block">
                                اطلب الآن
                            </a>
                        @endif

                        <button onclick="shareOffer()"
                                class="w-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-darkText dark:text-white text-center py-3 px-4 rounded-lg transition font-semibold">
                            مشاركة العرض
                        </button>
                    </div>
                @else
                    <div class="text-center py-4">
                        <p class="text-red-600 font-semibold">هذا العرض غير متاح حالياً</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- عروض مشابهة -->
    <div class="mt-12">
        <h2 class="text-2xl font-bold text-darkText dark:text-white mb-6">عروض أخرى قد تهمك</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($relatedOffers as $relatedOffer)
                <div class="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-md hover:shadow-lg transition">
                    <img src="{{ $relatedOffer['image_url'] ?? 'https://images.unsplash.com/photo-1555939594-58d7cb561ad1' }}" alt="{{ $relatedOffer['title'] }}" class="w-full h-48 object-cover">
                    <div class="p-4">
                        <h3 class="font-bold text-darkText dark:text-white mb-2">{{ $relatedOffer['title'] }}</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm mb-3">{{ Str::limit($relatedOffer['description'], 100) }}</p>
                        <a href="{{ route('customer.offers.show', $relatedOffer['slug']) }}"
                           class="text-primary hover:text-primary/80 font-semibold text-sm">
                            عرض التفاصيل ←
                        </a>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</div>

<!-- نموذج الحجز المنبثق -->
<div id="reservationModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div class="p-6">
            <!-- رأس النموذج -->
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-bold text-darkText dark:text-white">حجز العرض: {{ $offer['title'] }}</h3>
                <button onclick="closeReservationModal()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- نموذج الحجز -->
            <form id="offerReservationForm" action="{{ route('customer.reservations.store') }}" method="POST" class="space-y-4">
                @csrf
                <input type="hidden" name="offer_slug" value="{{ $offer['slug'] }}">
                <input type="hidden" name="offer_title" value="{{ $offer['title'] }}">

                <!-- تاريخ الحجز -->
                <div>
                    <label for="reservation_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تاريخ الحجز</label>
                    <input type="date" id="reservation_date" name="reservation_date" required
                           min="{{ date('Y-m-d') }}"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                </div>

                <!-- وقت الحجز -->
                <div>
                    <label for="reservation_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">وقت الحجز</label>
                    <select id="reservation_time" name="reservation_time" required
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                        <option value="">اختر الوقت</option>
                        <option value="12:00">12:00 ظهراً</option>
                        <option value="12:30">12:30 ظهراً</option>
                        <option value="13:00">1:00 ظهراً</option>
                        <option value="13:30">1:30 ظهراً</option>
                        <option value="14:00">2:00 ظهراً</option>
                        <option value="14:30">2:30 ظهراً</option>
                        <option value="15:00">3:00 عصراً</option>
                        <option value="15:30">3:30 عصراً</option>
                        <option value="16:00">4:00 عصراً</option>
                        <option value="16:30">4:30 عصراً</option>
                        <option value="17:00">5:00 مساءً</option>
                        <option value="17:30">5:30 مساءً</option>
                        <option value="18:00">6:00 مساءً</option>
                        <option value="18:30">6:30 مساءً</option>
                        <option value="19:00">7:00 مساءً</option>
                        <option value="19:30">7:30 مساءً</option>
                        <option value="20:00">8:00 مساءً</option>
                        <option value="20:30">8:30 مساءً</option>
                        <option value="21:00">9:00 مساءً</option>
                        <option value="21:30">9:30 مساءً</option>
                        <option value="22:00">10:00 مساءً</option>
                    </select>
                </div>

                <!-- عدد الأشخاص -->
                <div>
                    <label for="party_size" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">عدد الأشخاص</label>
                    <select id="party_size" name="party_size" required
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                        <option value="">اختر عدد الأشخاص</option>
                        @for($i = 1; $i <= 10; $i++)
                            <option value="{{ $i }}">{{ $i }} {{ $i == 1 ? 'شخص' : 'أشخاص' }}</option>
                        @endfor
                    </select>
                </div>

                <!-- معلومات الاتصال -->
                <div>
                    <label for="contact_phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الهاتف</label>
                    <input type="tel" id="contact_phone" name="contact_phone" required
                           value="{{ auth()->user()->phone ?? '' }}"
                           placeholder="مثال: +218 91 234 5678"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                </div>

                <!-- طلبات خاصة -->
                <div>
                    <label for="special_requests" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">طلبات خاصة (اختياري)</label>
                    <textarea id="special_requests" name="special_requests" rows="3"
                              placeholder="أي طلبات خاصة للحجز..."
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"></textarea>
                </div>

                <!-- معلومات العرض -->
                @if(isset($offer['discount']) && $offer['discount'])
                    <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
                        <div class="flex items-center">
                            <i class="fas fa-tag text-green-600 ml-2"></i>
                            <span class="text-green-800 dark:text-green-200 font-semibold">
                                سيتم تطبيق خصم {{ $offer['discount'] }}% على هذا الحجز
                            </span>
                        </div>
                    </div>
                @endif

                <!-- أزرار الإجراءات -->
                <div class="flex gap-3 pt-4">
                    <button type="button" onclick="closeReservationModal()"
                            class="flex-1 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition">
                        إلغاء
                    </button>
                    <button type="submit" id="submitReservationBtn"
                            class="flex-1 bg-primary hover:bg-primary/90 text-white py-2 px-4 rounded-lg transition font-semibold">
                        <span id="submitBtnText">تأكيد الحجز</span>
                        <i id="submitBtnLoader" class="fas fa-spinner fa-spin hidden mr-2"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openReservationModal() {
    @auth
        document.getElementById('reservationModal').classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    @else
        alert('يجب تسجيل الدخول أولاً للحجز');
        window.location.href = '{{ route("login") }}';
    @endauth
}

function closeReservationModal() {
    document.getElementById('reservationModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
}

function shareOffer() {
    if (navigator.share) {
        navigator.share({
            title: '{{ $offer["title"] }}',
            text: '{{ $offer["description"] }}',
            url: window.location.href
        });
    } else {
        // نسخ الرابط للحافظة
        navigator.clipboard.writeText(window.location.href).then(() => {
            alert('تم نسخ رابط العرض!');
        });
    }
}

// إغلاق النموذج عند النقر خارجه
document.getElementById('reservationModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeReservationModal();
    }
});

// إغلاق النموذج بمفتاح Escape
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeReservationModal();
    }
});

// تحسين تجربة إرسال النموذج
document.getElementById('offerReservationForm').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('submitReservationBtn');
    const submitBtnText = document.getElementById('submitBtnText');
    const submitBtnLoader = document.getElementById('submitBtnLoader');

    // تعطيل الزر وإظهار التحميل
    submitBtn.disabled = true;
    submitBtnText.textContent = 'جاري الحجز...';
    submitBtnLoader.classList.remove('hidden');
});
</script>
@endsection
