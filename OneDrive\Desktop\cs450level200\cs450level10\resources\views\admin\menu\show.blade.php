@extends('layouts.admin')

@section('title', 'تفاصيل المنتج - لوحة تحكم Eat Hub')

@section('page-title', 'تفاصيل المنتج')

@section('content')
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
    <div class="flex flex-col md:flex-row">
        <div class="md:w-1/3">
            @if($menuItem->image_path)
            <img src="{{ asset('storage/' . $menuItem->image_path) }}" alt="{{ $menuItem->name }}" class="w-full h-full object-cover">
            @else
            <div class="w-full h-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                <i class="fas fa-image text-gray-400 text-5xl"></i>
            </div>
            @endif
        </div>
        <div class="md:w-2/3 p-6">
            <div class="flex justify-between items-start mb-4">
                <div>
                    <h2 class="text-2xl font-bold text-gray-800 dark:text-white">{{ $menuItem->name }}</h2>
                    <div class="mt-2">
                        @if($menuItem->is_available)
                        <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs">متوفر</span>
                        @else
                        <span class="bg-red-500 text-white px-2 py-1 rounded-full text-xs">غير متوفر</span>
                        @endif
                        
                        @php
                            $categoryLabels = [
                                'main' => 'الأطباق الرئيسية',
                                'appetizer' => 'المقبلات',
                                'dessert' => 'الحلويات',
                                'beverage' => 'المشروبات'
                            ];
                            $categoryColors = [
                                'main' => 'blue',
                                'appetizer' => 'green',
                                'dessert' => 'purple',
                                'beverage' => 'yellow'
                            ];
                            $color = $categoryColors[$menuItem->category] ?? 'gray';
                        @endphp
                        
                        <span class="mr-2 text-xs bg-{{ $color }}-100 dark:bg-{{ $color }}-900/30 text-{{ $color }}-800 dark:text-{{ $color }}-300 px-2 py-1 rounded-full">
                            {{ $categoryLabels[$menuItem->category] ?? $menuItem->category }}
                        </span>
                    </div>
                </div>
                <div class="text-xl font-bold text-primary">{{ number_format($menuItem->price, 2) }} د.ل</div>
            </div>
            
            @if($menuItem->description)
            <div class="mb-6">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-2">الوصف</h3>
                <p class="text-gray-600 dark:text-gray-300">{{ $menuItem->description }}</p>
            </div>
            @endif
            
            <div class="mb-6">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-2">المكونات</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    @foreach($menuItem->recipe as $recipe)
                    <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                        <span class="text-gray-700 dark:text-gray-300">{{ $recipe->ingredient->name }}</span>
                        <span class="text-gray-600 dark:text-gray-400">{{ $recipe->quantity }} {{ $recipe->ingredient->unit }}</span>
                    </div>
                    @endforeach
                </div>
            </div>
            
            <div class="flex justify-end space-x-2 space-x-reverse">
                <a href="{{ route('admin.menu') }}" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600">
                    <i class="fas fa-arrow-right ml-2"></i>
                    <span>العودة للقائمة</span>
                </a>
                <a href="{{ route('admin.menu.edit', $menuItem->item_id) }}" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                    <i class="fas fa-edit ml-2"></i>
                    <span>تعديل</span>
                </a>
                <button onclick="deleteMenuItem({{ $menuItem->item_id }})" class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600">
                    <i class="fas fa-trash ml-2"></i>
                    <span>حذف</span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">إحصائيات المنتج</h3>
        <div class="grid grid-cols-2 gap-4">
            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-md">
                <div class="text-sm text-gray-500 dark:text-gray-400">عدد الطلبات</div>
                <div class="text-2xl font-bold text-gray-800 dark:text-white">{{ $menuItem->orders_count ?? 0 }}</div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-md">
                <div class="text-sm text-gray-500 dark:text-gray-400">الإيرادات</div>
                <div class="text-2xl font-bold text-gray-800 dark:text-white">{{ number_format(($menuItem->orders_count ?? 0) * $menuItem->price, 2) }} د.ل</div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-md">
                <div class="text-sm text-gray-500 dark:text-gray-400">متوسط التقييم</div>
                <div class="text-2xl font-bold text-gray-800 dark:text-white">{{ number_format($menuItem->average_rating ?? 0, 1) }}/5</div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-md">
                <div class="text-sm text-gray-500 dark:text-gray-400">تاريخ الإضافة</div>
                <div class="text-lg font-bold text-gray-800 dark:text-white">{{ $menuItem->created_at->format('Y/m/d') }}</div>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">تكلفة المنتج</h3>
        <div class="space-y-4">
            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-md">
                <div class="flex justify-between items-center mb-2">
                    <div class="text-sm text-gray-500 dark:text-gray-400">تكلفة المكونات</div>
                    <div class="text-lg font-bold text-gray-800 dark:text-white">{{ number_format($menuItem->ingredients_cost ?? 0, 2) }} د.ل</div>
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                    <div class="bg-blue-500 h-2 rounded-full" style="width: {{ min(100, (($menuItem->ingredients_cost ?? 0) / $menuItem->price) * 100) }}%"></div>
                </div>
            </div>
            
            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-md">
                <div class="flex justify-between items-center mb-2">
                    <div class="text-sm text-gray-500 dark:text-gray-400">هامش الربح</div>
                    <div class="text-lg font-bold text-gray-800 dark:text-white">{{ number_format($menuItem->price - ($menuItem->ingredients_cost ?? 0), 2) }} د.ل </div>
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                    <div class="bg-green-500 h-2 rounded-full" style="width: {{ min(100, (1 - (($menuItem->ingredients_cost ?? 0) / $menuItem->price)) * 100) }}%"></div>
                </div>
            </div>
            
            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-md">
                <div class="flex justify-between items-center">
                    <div class="text-sm text-gray-500 dark:text-gray-400">نسبة الربح</div>
                    <div class="text-lg font-bold text-gray-800 dark:text-white">
                        {{ number_format((1 - (($menuItem->ingredients_cost ?? 0) / $menuItem->price)) * 100, 1) }}%
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج تأكيد الحذف -->
<div id="deleteModal" class="fixed inset-0 bg-black/50 z-50 hidden flex items-center justify-center">
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md mx-auto">
        <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-4">تأكيد الحذف</h3>
        <p class="text-gray-600 dark:text-gray-300 mb-6">هل أنت متأكد من رغبتك في حذف هذا المنتج من القائمة؟ هذا الإجراء لا يمكن التراجع عنه.</p>
        <div class="flex justify-end space-x-2 space-x-reverse">
            <button id="cancelDelete" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600">إلغاء</button>
            <form id="deleteForm" method="POST" action="{{ route('admin.menu.delete', $menuItem->item_id) }}">
                @csrf
                @method('DELETE')
                <button type="submit" class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600">حذف</button>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    function deleteMenuItem(id) {
        const deleteModal = document.getElementById('deleteModal');
        const cancelDelete = document.getElementById('cancelDelete');
        
        deleteModal.classList.remove('hidden');
        
        cancelDelete.addEventListener('click', function() {
            deleteModal.classList.add('hidden');
        });
    }
</script>
@endsection
