@extends('layouts.admin')

@php
use Illuminate\Support\Facades\DB;
@endphp

@section('title', 'إدارة المصروفات - لوحة تحكم Eat Hub')

@section('page-title', 'إدارة المصروفات')

@section('content')
<div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
    <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0">إدارة المصروفات</h2>
    <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 sm:space-x-reverse">
        <div class="relative">
            <form action="{{ route('admin.expenses') }}" method="GET" class="flex">
                <input type="text" name="search" value="{{ request('search') }}" placeholder="بحث..." class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary text-base">
                <button type="submit" class="absolute left-3 top-2.5 text-gray-500 dark:text-gray-400">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
        <a href="{{ route('admin.expenses.create') }}" class="bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
            <i class="fas fa-plus ml-2"></i>
            <span>إضافة مصروف</span>
        </a>
    </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-6">
    <!-- إحصائية المصروفات 1 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">إجمالي المصروفات (الشهر الحالي)</p>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">{{ number_format($currentMonthTotal, 2) }} ر.س</h3>
            </div>
            <div class="rounded-full bg-red-100 dark:bg-red-900/30 p-3">
                <i class="fas fa-money-bill-wave text-red-500 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- إحصائية المصروفات حسب الفئة -->
    <div class="lg:col-span-3 bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-start mb-4">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white">المصروفات حسب الفئة (الشهر الحالي)</h3>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
            @foreach($categoryTotals as $category => $data)
            <div class="flex flex-col">
                <span class="text-gray-600 dark:text-gray-300 text-sm mb-1">{{ $data['name'] }}</span>
                <span class="text-xl font-bold text-gray-800 dark:text-white">{{ number_format($data['amount'], 2) }} ر.س</span>
                <div class="mt-2 w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                    <div class="{{ str_replace('text-', 'bg-', explode(' ', $data['color'])[0]) }} h-full rounded-full" style="width: {{ $data['percentage'] }}%;"></div>
                </div>
                <span class="text-gray-500 dark:text-gray-400 text-xs mt-1">{{ $data['percentage'] }}%</span>
            </div>
            @endforeach
        </div>
    </div>
</div>

<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-2 md:mb-0">سجل المصروفات</h3>
            <div class="flex space-x-2 space-x-reverse">
                <div class="relative">
                    <input type="date" name="date" form="filterForm" value="{{ request('date') }}" class="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white border-0 rounded-md p-2 text-sm focus:outline-none">
                </div>
                <div class="relative">
                    <select name="category" form="filterForm" class="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white border-0 rounded-md p-2 text-sm focus:outline-none">
                        <option value="">جميع الفئات</option>
                        @foreach($availableCategories as $category)
                            <option value="{{ $category }}" {{ request('category') == $category ? 'selected' : '' }}>{{ $categoryTranslations[$category] }}</option>
                        @endforeach
                    </select>
                </div>
                <form id="filterForm" action="{{ route('admin.expenses') }}" method="GET">
                    @if(request('search'))
                        <input type="hidden" name="search" value="{{ request('search') }}">
                    @endif
                    <button type="submit" class="bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
                        <i class="fas fa-filter ml-2"></i>
                        <span>تصفية</span>
                    </button>
                </form>
            </div>
        </div>
    </div>

    @if($expenses->isEmpty())
        <div class="p-6 text-center">
            <p class="text-gray-500 dark:text-gray-400">لا توجد مصروفات مسجلة في النظام.</p>
            <a href="{{ route('admin.expenses.create') }}" class="mt-4 inline-block bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md transition-all">
                <i class="fas fa-plus ml-2"></i>
                <span>إضافة مصروف جديد</span>
            </a>
        </div>
    @else
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead>
                    <tr class="bg-gray-50 dark:bg-gray-700">
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            الوصف
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            الفئة
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            المبلغ
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            طريقة الدفع
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            تاريخ المصروف
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            سجل بواسطة
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($expenses as $expense)
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                            {{ $expense->description }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 py-1 text-xs rounded-full {{ $categoryColors[$expense->category] ?? 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300' }}">
                                {{ $categoryTranslations[$expense->category] ?? $expense->category }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                            {{ number_format($expense->amount, 2) }} ر.س
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            @php
                                $paymentMethodTranslations = [
                                    'cash' => 'نقدي',
                                    'bank_transfer' => 'تحويل بنكي',
                                    'cheque' => 'شيك',
                                ];
                            @endphp
                            {{ $paymentMethodTranslations[$expense->payment_method] ?? $expense->payment_method }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            {{ $expense->expense_date }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            {{ $expense->recorder ? $expense->recorder->first_name . ' ' . $expense->recorder->last_name : 'غير معروف' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <a href="{{ route('admin.expenses.edit', $expense->expense_id) }}" class="text-blue-500 hover:text-blue-700 transition-all">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button onclick="deleteExpense({{ $expense->expense_id }})" class="text-red-500 hover:text-red-700 transition-all">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            {{ $expenses->links() }}
        </div>
    @endif
</div>

<!-- نموذج تأكيد الحذف -->
<div id="deleteModal" class="fixed inset-0 bg-black/50 z-50 hidden items-center justify-center">
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md mx-auto">
        <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-4">تأكيد الحذف</h3>
        <p class="text-gray-600 dark:text-gray-300 mb-6">هل أنت متأكد من رغبتك في حذف هذا المصروف؟ هذا الإجراء لا يمكن التراجع عنه.</p>
        <div class="flex justify-end space-x-2 space-x-reverse">
            <button id="cancelDelete" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600">إلغاء</button>
            <form id="deleteForm" method="POST" action="">
                @csrf
                @method('DELETE')
                <button type="submit" class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600">حذف</button>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    function deleteExpense(id) {
        const deleteModal = document.getElementById('deleteModal');
        const deleteForm = document.getElementById('deleteForm');
        const cancelDelete = document.getElementById('cancelDelete');

        // تعيين مسار الحذف
        deleteForm.action = "{{ url('admin/expenses') }}/" + id;

        // إظهار النموذج
        deleteModal.classList.remove('hidden');
        deleteModal.classList.add('flex');

        // إضافة مستمع حدث لزر الإلغاء
        cancelDelete.onclick = function() {
            deleteModal.classList.add('hidden');
            deleteModal.classList.remove('flex');
        };

        // منع انتشار النقرات خارج النموذج
        deleteModal.onclick = function(e) {
            if (e.target === deleteModal) {
                deleteModal.classList.add('hidden');
                deleteModal.classList.remove('flex');
            }
        };
    }
</script>
@endsection
