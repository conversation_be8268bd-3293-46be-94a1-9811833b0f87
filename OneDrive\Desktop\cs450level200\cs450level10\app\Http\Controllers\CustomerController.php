<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Order;
use App\Models\Reservation;
use App\Models\Notification;
use App\Models\MenuItem;
use App\Models\Review;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Routing\Controller;

class CustomerController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth', ['except' => ['index']]);
    }

    public function index()
    {
        try {
            // استرجاع العناصر المميزة من قاعدة البيانات
            $featuredItems = MenuItem::where('is_available', true)
                ->orderBy('created_at', 'desc')
                ->take(6)
                ->get();

            // استرجاع آخر التقييمات من قاعدة البيانات
            $latestReviews = Review::with('user')
                ->orderBy('created_at', 'desc')
                ->take(6)
                ->get();

            // إنشاء عروض افتراضية
            $specialOffers = collect([
                (object)[
                    'id' => 1,
                    'title' => 'خصم 20% على طلبات الوجبات العائلية',
                    'description' => 'استمتع بخصم 20% على جميع الوجبات العائلية خلال عطلة نهاية الأسبوع',
                    'image_url' => 'https://images.unsplash.com/photo-1544148103-0773bf10d330',
                    'start_date' => now(),
                    'end_date' => now()->addDays(7),
                    'is_active' => true
                ],
                (object)[
                    'id' => 2,
                    'title' => 'أطباق جديدة في قائمتنا',
                    'description' => 'تعرف على أطباقنا الجديدة المستوحاة من المطبخ الآسيوي بلمسة عربية مميزة',
                    'image_url' => 'https://images.unsplash.com/photo-1476224203421-9ac39bcb3327',
                    'start_date' => now()->subDays(5),
                    'end_date' => now()->addDays(10),
                    'is_active' => true
                ],
                (object)[
                    'id' => 3,
                    'title' => 'ليالي الموسيقى الحية',
                    'description' => 'انضم إلينا كل خميس لليالي الموسيقى الحية مع تشكيلة من أشهى المأكولات',
                    'image_url' => 'https://images.unsplash.com/photo-1519690889869-e705e59f72e1',
                    'start_date' => now()->subDays(2),
                    'end_date' => now()->addDays(30),
                    'is_active' => true
                ]
            ]);

        } catch (\Exception $e) {
            // في حالة وجود خطأ، استخدام بيانات افتراضية
            Log::error('Error in customer index: ' . $e->getMessage());

            $featuredItems = collect();
            $latestReviews = collect();
            $specialOffers = collect([
                (object)[
                    'id' => 1,
                    'title' => 'خصم 20% على طلبات الوجبات العائلية',
                    'description' => 'استمتع بخصم 20% على جميع الوجبات العائلية خلال عطلة نهاية الأسبوع',
                    'image_url' => 'https://images.unsplash.com/photo-1544148103-0773bf10d330',
                    'start_date' => now(),
                    'end_date' => now()->addDays(7),
                    'is_active' => true
                ],
                (object)[
                    'id' => 2,
                    'title' => 'أطباق جديدة في قائمتنا',
                    'description' => 'تعرف على أطباقنا الجديدة المستوحاة من المطبخ الآسيوي بلمسة عربية مميزة',
                    'image_url' => 'https://images.unsplash.com/photo-1476224203421-9ac39bcb3327',
                    'start_date' => now()->subDays(5),
                    'end_date' => now()->addDays(10),
                    'is_active' => true
                ],
                (object)[
                    'id' => 3,
                    'title' => 'ليالي الموسيقى الحية',
                    'description' => 'انضم إلينا كل خميس لليالي الموسيقى الحية مع تشكيلة من أشهى المأكولات',
                    'image_url' => 'https://images.unsplash.com/photo-1519690889869-e705e59f72e1',
                    'start_date' => now()->subDays(2),
                    'end_date' => now()->addDays(30),
                    'is_active' => true
                ]
            ]);
        }

        return view('customer.index', compact('featuredItems', 'latestReviews', 'specialOffers'));
    }

    public function dashboard()
    {
        $user = Auth::user();

        // Get recent orders from database
        try {
            $recentOrders = Order::with(['items.menuItem'])
                ->where('user_id', $user->user_id)
                ->orderBy('created_at', 'desc')
                ->take(5)
                ->get()
                ->map(function($order) {
                    $order->order_number = 'ORD-' . str_pad($order->order_id, 4, '0', STR_PAD_LEFT);
                    return $order;
                });

            Log::info('Dashboard recent orders retrieved:', [
                'user_id' => $user->user_id,
                'orders_count' => $recentOrders->count()
            ]);
        } catch (\Exception $e) {
            Log::error('Error retrieving recent orders for dashboard:', [
                'user_id' => $user->user_id,
                'error' => $e->getMessage()
            ]);
            // في حالة عدم وجود الجدول، استخدم مجموعة افتراضية
            $recentOrders = collect([
                (object)[
                    'order_id' => 1,
                    'user_id' => $user->user_id,
                    'total_amount' => 120.50,
                    'status' => 'completed',
                    'created_at' => now()->subDays(2),
                    'order_number' => 'ORD-1001'
                ],
                (object)[
                    'order_id' => 2,
                    'user_id' => $user->user_id,
                    'total_amount' => 85.75,
                    'status' => 'completed',
                    'created_at' => now()->subDays(5),
                    'order_number' => 'ORD-1002'
                ],
                (object)[
                    'order_id' => 3,
                    'user_id' => $user->user_id,
                    'total_amount' => 150.00,
                    'status' => 'processing',
                    'created_at' => now()->subHours(3),
                    'order_number' => 'ORD-1003'
                ]
            ]);
        }

        // Get upcoming reservations from database
        try {
            $upcomingReservations = Reservation::with('table')
                ->where('user_id', $user->user_id)
                ->where('reservation_time', '>', now())
                ->where('status', 'confirmed')
                ->orderBy('reservation_time', 'asc')
                ->take(3)
                ->get()
                ->map(function($reservation) {
                    $reservation->reservation_number = 'R' . str_pad($reservation->reservation_id, 4, '0', STR_PAD_LEFT);
                    return $reservation;
                });

            Log::info('Dashboard upcoming reservations retrieved:', [
                'user_id' => $user->user_id,
                'reservations_count' => $upcomingReservations->count()
            ]);
        } catch (\Exception $e) {
            Log::error('Error retrieving upcoming reservations for dashboard:', [
                'user_id' => $user->user_id,
                'error' => $e->getMessage()
            ]);
            // في حالة عدم وجود الجدول، استخدم مجموعة افتراضية
            $upcomingReservations = collect([
                (object)[
                    'reservation_id' => 1,
                    'user_id' => $user->user_id,
                    'reservation_time' => now()->addDays(2),
                    'duration' => 120,
                    'status' => 'confirmed',
                    'table' => (object)[
                        'table_id' => 1,
                        'table_number' => 5,
                        'capacity' => 4,
                        'location' => 'داخلي'
                    ]
                ],
                (object)[
                    'reservation_id' => 2,
                    'user_id' => $user->user_id,
                    'reservation_time' => now()->addDays(7),
                    'duration' => 90,
                    'status' => 'confirmed',
                    'table' => (object)[
                        'table_id' => 2,
                        'table_number' => 12,
                        'capacity' => 6,
                        'location' => 'خارجي'
                    ]
                ]
            ]);
        }

        // Get unread notifications from database
        try {
            $notifications = Notification::where('user_id', $user->user_id)
                ->where('is_read', false)
                ->orderBy('created_at', 'desc')
                ->take(5)
                ->get();
        } catch (\Exception $e) {
            // في حالة عدم وجود الجدول، استخدم مجموعة افتراضية
            $notifications = collect([
                (object)[
                    'notification_id' => 1,
                    'user_id' => $user->user_id,
                    'title' => 'تأكيد الطلب',
                    'message' => 'تم تأكيد طلبك رقم ORD-1001 وجاري تجهيزه',
                    'is_read' => false,
                    'created_at' => now()->subHours(2)
                ],
                (object)[
                    'notification_id' => 2,
                    'user_id' => $user->user_id,
                    'title' => 'عرض خاص',
                    'message' => 'استمتع بخصم 15% على طلبك القادم باستخدام كود: SPECIAL15',
                    'is_read' => false,
                    'created_at' => now()->subDays(1)
                ]
            ]);
        }

        // Get order stats from database
        try {
            $totalOrders = Order::where('user_id', $user->user_id)->count();
            $totalSpent = Order::where('user_id', $user->user_id)
                ->where('status', 'completed')
                ->sum('total_amount');

            // Get favorite items
            $favoriteItems = DB::table('order_items')
                ->join('orders', 'order_items.order_id', '=', 'orders.order_id')
                ->join('menu_items', 'order_items.menu_item_id', '=', 'menu_items.item_id')
                ->where('orders.user_id', $user->user_id)
                ->select('menu_items.name', DB::raw('SUM(order_items.quantity) as total'))
                ->groupBy('menu_items.item_id', 'menu_items.name')
                ->orderBy('total', 'desc')
                ->take(3)
                ->get();

            Log::info('Dashboard stats retrieved:', [
                'user_id' => $user->user_id,
                'total_orders' => $totalOrders,
                'total_spent' => $totalSpent,
                'favorite_items_count' => $favoriteItems->count()
            ]);

            $orderStats = [
                'totalOrders' => $totalOrders,
                'totalSpent' => $totalSpent,
                'favoriteItems' => $favoriteItems,
            ];
        } catch (\Exception $e) {
            Log::error('Error retrieving dashboard stats:', [
                'user_id' => $user->user_id,
                'error' => $e->getMessage()
            ]);

            // في حالة عدم وجود الجداول، استخدم بيانات افتراضية
            $orderStats = [
                'totalOrders' => 8,
                'totalSpent' => 750.25,
                'favoriteItems' => collect([
                    (object)[
                        'name' => 'برجر لحم أنجوس',
                        'total' => 5
                    ],
                    (object)[
                        'name' => 'بيتزا سوبريم',
                        'total' => 3
                    ],
                    (object)[
                        'name' => 'سلطة سيزر بالدجاج',
                        'total' => 2
                    ]
                ]),
            ];
        }

        // Get featured menu items
        try {
            $featuredItems = MenuItem::where('is_available', true)
                ->orderBy('created_at', 'desc')
                ->take(3)
                ->get();

            Log::info('Dashboard featured items retrieved:', [
                'user_id' => $user->user_id,
                'featured_items_count' => $featuredItems->count()
            ]);
        } catch (\Exception $e) {
            Log::error('Error retrieving featured items for dashboard:', [
                'user_id' => $user->user_id,
                'error' => $e->getMessage()
            ]);
            // في حالة عدم وجود الجدول، استخدم مجموعة افتراضية
            $featuredItems = collect([
                (object)[
                    'name' => 'برجر لحم أنجوس',
                    'description' => 'برجر لحم بقري فاخر مع صلصة خاصة وجبنة شيدر وخضار طازجة',
                    'price' => 55,
                    'image_url' => 'https://images.unsplash.com/photo-1513104890138-7c749659a591',
                    'is_bestseller' => true
                ],
                (object)[
                    'name' => 'بيتزا سوبريم',
                    'description' => 'بيتزا مع صلصة طماطم، جبنة موزاريلا، فلفل، زيتون، بصل، فطر وبيبروني',
                    'price' => 65,
                    'image_url' => 'https://images.unsplash.com/photo-1565299624946-b28f40a0ae38',
                    'is_bestseller' => false
                ],
                (object)[
                    'name' => 'سلطة سيزر بالدجاج',
                    'description' => 'خس روماني، صدر دجاج مشوي، جبنة بارميزان، خبز محمص مع صلصة سيزر',
                    'price' => 45,
                    'image_url' => 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd',
                    'is_bestseller' => false
                ]
            ]);
        }

        // Get latest reviews
        try {
            $latestReviews = DB::table('reviews')
                ->join('users', 'reviews.user_id', '=', 'users.user_id')
                ->select('reviews.*', 'users.first_name', 'users.last_name')
                ->where('reviews.is_approved', true)
                ->orderBy('reviews.created_at', 'desc')
                ->take(3)
                ->get();
        } catch (\Exception $e) {
            // في حالة عدم وجود الجدول، استخدم مجموعة افتراضية
            $latestReviews = collect();
        }

        // Get special offers
        try {
            $specialOffers = DB::table('special_offers')
                ->where('is_active', true)
                ->where('end_date', '>=', now())
                ->orderBy('start_date', 'asc')
                ->take(3)
                ->get();
        } catch (\Exception $e) {
            $specialOffers = collect([
                (object)[
                    'title' => 'خصم 20% على طلبات الوجبات العائلية',
                    'description' => 'استمتع بخصم 20% على جميع الوجبات العائلية خلال عطلة نهاية الأسبوع',
                    'image_url' => 'https://images.unsplash.com/photo-1544148103-0773bf10d330',
                    'start_date' => now()
                ],
                (object)[
                    'title' => 'أطباق جديدة في قائمتنا',
                    'description' => 'تعرف على أطباقنا الجديدة المستوحاة من المطبخ الآسيوي بلمسة عربية مميزة',
                    'image_url' => 'https://images.unsplash.com/photo-1476224203421-9ac39bcb3327',
                    'start_date' => now()
                ],
                (object)[
                    'title' => 'ليالي الموسيقى الحية',
                    'description' => 'انضم إلينا كل خميس لليالي الموسيقى الحية مع تشكيلة من أشهى المأكولات',
                    'image_url' => 'https://images.unsplash.com/photo-1519690889869-e705e59f72e1',
                    'start_date' => now()
                ]
            ]);
        }

        return view('customer.dashboard', compact(
            'user',
            'recentOrders',
            'upcomingReservations',
            'notifications',
            'orderStats',
            'featuredItems',
            'latestReviews',
            'specialOffers'
        ));
    }

    public function profile()
    {
        $user = Auth::user();

        // التأكد من أن المستخدم مسجل دخول
        if (!$user) {
            return redirect()->route('login')->with('error', 'يجب تسجيل الدخول للوصول إلى الملف الشخصي');
        }

        return view('customer.profile', compact('user'));
    }

    public function updateProfile(Request $request)
    {
        /** @var User $user */
        $user = Auth::user();

        // التحقق من نوع التحديث
        if ($request->has('update_password')) {
            return $this->updatePassword($request);
        }

        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:50',
            'last_name' => 'required|string|max:50',
            'email' => 'required|email|unique:users,email,' . $user->user_id . ',user_id',
            'phone' => 'required|string|max:20',
            'address' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // استخدام update مع where
        User::where('user_id', $user->user_id)->update([
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'email' => $request->email,
            'phone' => $request->phone,
            'address' => $request->address,
        ]);

        return redirect()->route('customer.profile')->with('success', 'تم تحديث بياناتك الشخصية بنجاح');
    }

    private function updatePassword(Request $request)
    {
        /** @var User $user */
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'current_password' => 'required',
            'password' => 'required|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // التحقق من كلمة المرور الحالية
        if (!Hash::check($request->current_password, $user->password)) {
            return redirect()->back()->with('password_error', 'كلمة المرور الحالية غير صحيحة');
        }

        // تحديث كلمة المرور
        User::where('user_id', $user->user_id)->update([
            'password' => Hash::make($request->password),
        ]);

        return redirect()->route('customer.profile')->with('password_success', 'تم تحديث كلمة المرور بنجاح');
    }



    // إعدادات الحساب
    public function accountSettings()
    {
        $user = Auth::user();

        // استرجاع إعدادات المستخدم من قاعدة البيانات أو استخدام القيم الافتراضية
        $userSettings = DB::table('user_settings')
            ->where('user_id', $user->user_id)
            ->first() ?? (object)[
                'language' => 'ar',
                'theme' => 'system',
                'currency' => 'SAR'
            ];

        // استرجاع إعدادات الخصوصية
        $privacySettings = DB::table('privacy_settings')
            ->where('user_id', $user->user_id)
            ->first() ?? (object)[
                'share_reviews' => true,
                'save_search_history' => true,
                'personalized_ads' => true
            ];

        return view('user.settings.account', compact('user', 'userSettings', 'privacySettings'));
    }

    // تحديث إعدادات الحساب
    public function updateAccountSettings(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'language' => 'required|in:ar,en',
            'theme' => 'required|in:light,dark,system',
            'currency' => 'required|in:SAR,USD',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // تحديث أو إنشاء إعدادات المستخدم
        DB::table('user_settings')->updateOrInsert(
            ['user_id' => $user->user_id],
            [
                'language' => $request->language,
                'theme' => $request->theme,
                'currency' => $request->currency,
                'updated_at' => now()
            ]
        );

        return redirect()->route('user.settings.account')->with('success', 'تم تحديث إعدادات الحساب بنجاح');
    }

    // تحديث إعدادات الخصوصية
    public function updatePrivacySettings(Request $request)
    {
        $user = Auth::user();

        // تحديث أو إنشاء إعدادات الخصوصية
        DB::table('privacy_settings')->updateOrInsert(
            ['user_id' => $user->user_id],
            [
                'share_reviews' => $request->has('share_reviews'),
                'save_search_history' => $request->has('save_search_history'),
                'personalized_ads' => $request->has('personalized_ads'),
                'updated_at' => now()
            ]
        );

        return redirect()->route('user.settings.account')->with('success', 'تم تحديث إعدادات الخصوصية بنجاح');
    }

    // إيقاف الحساب
    public function deactivateAccount(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'password' => 'required',
            'deactivation_reason' => 'nullable|string|in:not_satisfied,not_useful,alternative,temporary,privacy,other',
            'other_reason' => 'required_if:deactivation_reason,other',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // التحقق من كلمة المرور
        if (!Hash::check($request->password, $user->password)) {
            return redirect()->back()->with('error', 'كلمة المرور غير صحيحة')->withInput();
        }

        // تسجيل سبب إيقاف الحساب
        DB::table('account_deactivations')->insert([
            'user_id' => $user->user_id,
            'reason' => $request->deactivation_reason,
            'other_reason' => $request->other_reason,
            'created_at' => now()
        ]);

        // تعيين حالة الحساب إلى غير نشط
        User::where('user_id', $user->user_id)->update([
            'is_active' => false,
            'deactivated_at' => now()
        ]);

        // تسجيل الخروج
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('login')->with('success', 'تم إيقاف حسابك بنجاح. يمكنك إعادة تنشيطه في أي وقت خلال 30 يوماً عن طريق تسجيل الدخول مرة أخرى.');
    }

    // إعدادات الدفع
    public function paymentSettings()
    {
        $user = Auth::user();

        // استرجاع طرق الدفع المحفوظة
        $paymentMethods = DB::table('payment_methods')
            ->where('user_id', $user->user_id)
            ->get();

        // استرجاع طريقة الدفع الافتراضية
        $defaultMethod = DB::table('payment_methods')
            ->where('user_id', $user->user_id)
            ->where('is_default', true)
            ->first();

        // استرجاع رصيد المحفظة
        $walletBalance = DB::table('wallets')
            ->where('user_id', $user->user_id)
            ->value('balance') ?? 0;

        return view('user.settings.payment', compact('user', 'paymentMethods', 'defaultMethod', 'walletBalance'));
    }

    // تعيين طريقة الدفع الافتراضية
    public function setDefaultPaymentMethod($method)
    {
        $user = Auth::user();

        // إعادة تعيين جميع طرق الدفع إلى غير افتراضية
        DB::table('payment_methods')
            ->where('user_id', $user->user_id)
            ->update(['is_default' => false]);

        if ($method === 'wallet') {
            // تعيين المحفظة كطريقة دفع افتراضية
            DB::table('user_settings')
                ->updateOrInsert(
                    ['user_id' => $user->user_id],
                    ['default_payment_method' => 'wallet', 'updated_at' => now()]
                );
        } else {
            // تعيين طريقة الدفع المحددة كافتراضية
            DB::table('payment_methods')
                ->where('user_id', $user->user_id)
                ->where('payment_method_id', $method)
                ->update(['is_default' => true]);
        }

        return redirect()->back()->with('success', 'تم تعيين طريقة الدفع الافتراضية بنجاح');
    }

    // إعدادات الإشعارات
    public function notificationSettings()
    {
        $user = Auth::user();

        // استرجاع إعدادات الإشعارات
        $notificationSettings = DB::table('notification_settings')
            ->where('user_id', $user->user_id)
            ->first() ?? (object)[
                'order_updates' => true,
                'reservation_updates' => true,
                'promotions' => true,
                'email_notifications' => true,
                'push_notifications' => true,
                'sms_notifications' => false
            ];

        return view('user.settings.notifications', compact('user', 'notificationSettings'));
    }

    // تحرير الملف الشخصي
    public function editProfile()
    {
        $user = Auth::user();
        return view('user.profile.edit', compact('user'));
    }

    // إعدادات الأمان
    public function securitySettings()
    {
        $user = Auth::user();

        // استرجاع سجل تسجيلات الدخول
        $loginHistory = DB::table('login_history')
            ->where('user_id', $user->user_id)
            ->orderBy('login_time', 'desc')
            ->take(10)
            ->get();

        return view('user.profile.security', compact('user', 'loginHistory'));
    }

    // عرض المعاملات
    public function transactions()
    {
        $user = Auth::user();

        // استرجاع المعاملات
        $transactions = DB::table('transactions')
            ->where('user_id', $user->user_id)
            ->orderBy('transaction_date', 'desc')
            ->paginate(15);

        return view('user.transactions', compact('user', 'transactions'));
    }

    /**
     * عرض صفحة الطلبات
     */
    public function orders(Request $request)
    {
        $user = Auth::user();

        // التأكد من أن المستخدم مسجل دخول
        if (!$user) {
            return redirect()->route('login')->with('error', 'يجب تسجيل الدخول لعرض الطلبات');
        }

        // فلترة الطلبات حسب الحالة
        $status = $request->get('status', 'all');

        try {
            $ordersQuery = Order::with(['items.menuItem'])
                ->where('user_id', $user->user_id)
                ->orderBy('created_at', 'desc');

            if ($status !== 'all') {
                $ordersQuery->where('status', $status);
            }

            $orders = $ordersQuery->paginate(10);

            Log::info('Orders retrieved for user:', [
                'user_id' => $user->user_id,
                'orders_count' => $orders->count(),
                'status_filter' => $status
            ]);

            // إضافة رقم الطلب لكل طلب
            $orders->getCollection()->transform(function($order) {
                $order->order_number = 'ORD-' . str_pad($order->order_id, 4, '0', STR_PAD_LEFT);
                return $order;
            });

        } catch (\Exception $e) {
            Log::error('Error retrieving orders:', [
                'user_id' => $user->user_id,
                'error' => $e->getMessage(),
                'status_filter' => $status
            ]);

            // في حالة عدم وجود الجداول، استخدم بيانات افتراضية
            $orders = collect([
                (object)[
                    'order_id' => 1254,
                    'order_number' => 'ORD-1254',
                    'total_amount' => 85.50,
                    'status' => 'completed',
                    'created_at' => now()->subDays(3),
                    'items' => collect([
                        (object)[
                            'quantity' => 1,
                            'price' => 55.00,
                            'menuItem' => (object)[
                                'name' => 'برجر لحم أنجوس',
                                'image_url' => 'https://images.unsplash.com/photo-1513104890138-7c749659a591'
                            ]
                        ],
                        (object)[
                            'quantity' => 2,
                            'price' => 15.25,
                            'menuItem' => (object)[
                                'name' => 'عصير برتقال طازج',
                                'image_url' => 'https://images.unsplash.com/photo-1461023058943-07fcbe16d735'
                            ]
                        ]
                    ])
                ],
                (object)[
                    'order_id' => 1255,
                    'order_number' => 'ORD-1255',
                    'total_amount' => 120.00,
                    'status' => 'processing',
                    'created_at' => now()->subHours(2),
                    'items' => collect([
                        (object)[
                            'quantity' => 1,
                            'price' => 65.00,
                            'menuItem' => (object)[
                                'name' => 'بيتزا سوبريم',
                                'image_url' => 'https://images.unsplash.com/photo-1565299624946-b28f40a0ae38'
                            ]
                        ],
                        (object)[
                            'quantity' => 1,
                            'price' => 45.00,
                            'menuItem' => (object)[
                                'name' => 'سلطة سيزر',
                                'image_url' => 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd'
                            ]
                        ]
                    ])
                ]
            ]);
        }

        return view('customer.orders', compact('user', 'orders', 'status'));
    }

    /**
     * عرض صفحة الحجوزات
     */
    public function reservations(Request $request)
    {
        $user = Auth::user();

        // التأكد من أن المستخدم مسجل دخول
        if (!$user) {
            return redirect()->route('login')->with('error', 'يجب تسجيل الدخول لعرض الحجوزات');
        }

        // فلترة الحجوزات حسب الحالة
        $status = $request->get('status', 'all');

        try {
            $reservationsQuery = Reservation::with('table')
                ->where('user_id', $user->user_id)
                ->orderBy('reservation_time', 'desc');

            if ($status !== 'all') {
                if ($status === 'upcoming') {
                    $reservationsQuery->where('reservation_time', '>', now())
                        ->where('status', 'confirmed');
                } elseif ($status === 'completed') {
                    $reservationsQuery->where('reservation_time', '<', now())
                        ->where('status', 'completed');
                } else {
                    $reservationsQuery->where('status', $status);
                }
            }

            $reservations = $reservationsQuery->paginate(10);

            // إضافة رقم الحجز لكل حجز
            $reservations->getCollection()->transform(function($reservation) {
                $reservation->reservation_number = 'R' . str_pad($reservation->reservation_id, 4, '0', STR_PAD_LEFT);
                return $reservation;
            });

        } catch (\Exception $e) {
            // في حالة عدم وجود الجداول، استخدم بيانات افتراضية
            $reservations = collect([
                (object)[
                    'reservation_id' => 1001,
                    'reservation_number' => 'R1001',
                    'reservation_time' => now()->addDays(1)->setHour(19)->setMinute(0),
                    'duration' => 120,
                    'status' => 'confirmed',
                    'special_requests' => 'طاولة بجانب النافذة، احتفال بعيد ميلاد',
                    'created_at' => now()->subDays(2),
                    'table' => (object)[
                        'table_number' => 8,
                        'capacity' => 4,
                        'location' => 'منطقة النافذة'
                    ]
                ],
                (object)[
                    'reservation_id' => 1000,
                    'reservation_number' => 'R1000',
                    'reservation_time' => now()->subDays(3)->setHour(18)->setMinute(30),
                    'duration' => 120,
                    'status' => 'completed',
                    'special_requests' => null,
                    'created_at' => now()->subDays(8),
                    'table' => (object)[
                        'table_number' => 5,
                        'capacity' => 2,
                        'location' => 'منطقة الحديقة'
                    ]
                ]
            ]);
        }

        return view('customer.reservations', compact('user', 'reservations', 'status'));
    }
}
