@extends('employee.layouts.app')

@section('title', 'إدارة العملاء')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-gray-50 via-purple-50 to-pink-100 dark:from-gray-900 dark:via-purple-900 dark:to-pink-900 py-8">
    <!-- عناصر زخرفية متحركة -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-purple-400/20 to-pink-600/20 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-indigo-600/20 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
        <div class="absolute top-40 left-40 w-80 h-80 bg-gradient-to-r from-rose-400/20 to-red-600/20 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
    </div>

    <div class="container mx-auto px-4 relative z-10">
        <!-- العنوان الرئيسي -->
        <div class="text-center mb-12">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full shadow-2xl mb-6">
                <i class="fas fa-users text-3xl text-white"></i>
            </div>
            <h1 class="text-4xl md:text-5xl font-bold text-gray-800 dark:text-white mb-4">
                👥 إدارة العملاء
            </h1>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                عرض وإدارة معلومات العملاء وتتبع نشاطهم
            </p>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl p-6 shadow-xl border border-white/20 dark:border-gray-700/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-users text-white text-xl"></i>
                    </div>
                    <span class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ $customers->total() }}</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">إجمالي العملاء</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">عميل مسجل</p>
            </div>

            <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl p-6 shadow-xl border border-white/20 dark:border-gray-700/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-user-check text-white text-xl"></i>
                    </div>
                    <span class="text-3xl font-bold text-green-600 dark:text-green-400">{{ $customers->where('is_active', true)->count() }}</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">عملاء نشطون</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">حاليًا</p>
            </div>

            <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl p-6 shadow-xl border border-white/20 dark:border-gray-700/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-shopping-cart text-white text-xl"></i>
                    </div>
                    <span class="text-3xl font-bold text-purple-600 dark:text-purple-400">{{ $customers->sum('orders_count') }}</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">إجمالي الطلبات</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">من جميع العملاء</p>
            </div>

            <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl p-6 shadow-xl border border-white/20 dark:border-gray-700/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-calendar-check text-white text-xl"></i>
                    </div>
                    <span class="text-3xl font-bold text-orange-600 dark:text-orange-400">{{ $customers->sum('reservations_count') }}</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">إجمالي الحجوزات</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">من جميع العملاء</p>
            </div>
        </div>

        <!-- شريط البحث والفلترة -->
        <div class="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-3xl p-6 shadow-xl border border-white/20 dark:border-gray-700/20 mb-8">
            <div class="flex flex-col md:flex-row gap-4">
                <div class="flex-1">
                    <div class="relative">
                        <input type="text" id="searchCustomers" placeholder="البحث عن عميل..." 
                               class="w-full px-4 py-3 pr-12 border-2 border-gray-200 dark:border-gray-600 rounded-2xl shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white transition-all duration-300">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>
                </div>
                <div class="flex gap-2">
                    <select class="px-4 py-3 border-2 border-gray-200 dark:border-gray-600 rounded-2xl shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white">
                        <option value="">جميع العملاء</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                    </select>
                    <button class="px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-2xl hover:from-purple-600 hover:to-pink-700 transition-all duration-300 transform hover:scale-105 shadow-lg">
                        <i class="fas fa-filter"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- قائمة العملاء -->
        <div class="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/20 dark:border-gray-700/20 overflow-hidden">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-2xl font-bold text-gray-800 dark:text-white flex items-center">
                    <i class="fas fa-list ml-3 text-purple-600"></i>
                    قائمة العملاء
                </h2>
            </div>

            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20">
                        <tr>
                            <th class="px-6 py-4 text-right text-sm font-semibold text-gray-700 dark:text-gray-300">العميل</th>
                            <th class="px-6 py-4 text-right text-sm font-semibold text-gray-700 dark:text-gray-300">معلومات الاتصال</th>
                            <th class="px-6 py-4 text-right text-sm font-semibold text-gray-700 dark:text-gray-300">الطلبات</th>
                            <th class="px-6 py-4 text-right text-sm font-semibold text-gray-700 dark:text-gray-300">الحجوزات</th>
                            <th class="px-6 py-4 text-right text-sm font-semibold text-gray-700 dark:text-gray-300">الحالة</th>
                            <th class="px-6 py-4 text-right text-sm font-semibold text-gray-700 dark:text-gray-300">تاريخ التسجيل</th>
                            <th class="px-6 py-4 text-right text-sm font-semibold text-gray-700 dark:text-gray-300">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse($customers as $customer)
                        <tr class="hover:bg-purple-50/50 dark:hover:bg-purple-900/10 transition-colors duration-200">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center shadow-lg mr-4 overflow-hidden">
                                        @if($customer->profile_image)
                                        <img src="{{ asset('storage/' . $customer->profile_image) }}" alt="{{ $customer->first_name }}" class="w-full h-full object-cover">
                                        @else
                                        <i class="fas fa-user text-white"></i>
                                        @endif
                                    </div>
                                    <div>
                                        <div class="text-sm font-semibold text-gray-800 dark:text-white">
                                            {{ $customer->first_name }} {{ $customer->last_name }}
                                        </div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400">
                                            ID: {{ $customer->id }}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-800 dark:text-white">
                                    <div class="flex items-center mb-1">
                                        <i class="fas fa-envelope text-purple-500 ml-2"></i>
                                        {{ $customer->email }}
                                    </div>
                                    @if($customer->phone)
                                    <div class="flex items-center">
                                        <i class="fas fa-phone text-green-500 ml-2"></i>
                                        {{ $customer->phone }}
                                    </div>
                                    @endif
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mr-2">
                                        <i class="fas fa-shopping-cart text-blue-600 dark:text-blue-400 text-sm"></i>
                                    </div>
                                    <span class="text-lg font-bold text-blue-600 dark:text-blue-400">{{ $customer->orders_count }}</span>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center mr-2">
                                        <i class="fas fa-calendar-check text-orange-600 dark:text-orange-400 text-sm"></i>
                                    </div>
                                    <span class="text-lg font-bold text-orange-600 dark:text-orange-400">{{ $customer->reservations_count }}</span>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                @if($customer->is_active)
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                    <div class="w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse"></div>
                                    نشط
                                </span>
                                @else
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                                    <div class="w-2 h-2 bg-red-500 rounded-full mr-1"></div>
                                    غير نشط
                                </span>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-600 dark:text-gray-400">
                                    {{ $customer->created_at->format('Y-m-d') }}
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-500">
                                    {{ $customer->created_at->diffForHumans() }}
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    <a href="{{ route('employee.customers.show', $customer->id) }}" 
                                       class="p-2 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-xl hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors duration-200"
                                       title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button class="p-2 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-xl hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors duration-200"
                                            title="إرسال رسالة">
                                        <i class="fas fa-envelope"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="flex flex-col items-center">
                                    <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
                                        <i class="fas fa-users text-2xl text-gray-400"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-gray-600 dark:text-gray-400 mb-2">لا توجد عملاء</h3>
                                    <p class="text-gray-500 dark:text-gray-500">لم يتم العثور على أي عملاء مسجلين</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- التصفح -->
            @if($customers->hasPages())
            <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                {{ $customers->links() }}
            </div>
            @endif
        </div>
    </div>
</div>

<style>
@keyframes blob {
    0% { transform: translate(0px, 0px) scale(1); }
    33% { transform: translate(30px, -50px) scale(1.1); }
    66% { transform: translate(-20px, 20px) scale(0.9); }
    100% { transform: translate(0px, 0px) scale(1); }
}

.animate-blob {
    animation: blob 7s infinite;
}

.animation-delay-2000 {
    animation-delay: 2s;
}

.animation-delay-4000 {
    animation-delay: 4s;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // البحث في الوقت الفعلي
    const searchInput = document.getElementById('searchCustomers');
    const tableRows = document.querySelectorAll('tbody tr');

    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        
        tableRows.forEach(row => {
            const customerName = row.querySelector('td:first-child').textContent.toLowerCase();
            const customerEmail = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
            
            if (customerName.includes(searchTerm) || customerEmail.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });
});
</script>
@endsection
