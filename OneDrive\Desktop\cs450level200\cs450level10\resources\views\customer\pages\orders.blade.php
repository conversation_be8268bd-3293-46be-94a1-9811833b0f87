<!-- صفحة طلباتي -->
@auth
<div id="orders-page" class="page hidden">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <!-- عنوان الصفحة -->
            <div class="flex justify-between items-center mb-8">
                <div>
                    <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-2">طلباتي</h1>
                    <p class="text-gray-600 dark:text-gray-400">تتبع جميع طلباتك السابقة والحالية</p>
                </div>
                <button data-page="menu" class="bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg transition">
                    <i class="fas fa-plus ml-2"></i>
                    طلب جديد
                </button>
            </div>

            <!-- فلاتر الطلبات -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
                <div class="flex flex-wrap gap-4 items-center">
                    <div class="flex space-x-2 space-x-reverse">
                        <button class="order-filter-btn active px-4 py-2 rounded-full bg-primary text-white text-sm" data-status="all">
                            جميع الطلبات
                        </button>
                        <button class="order-filter-btn px-4 py-2 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white transition text-sm" data-status="pending">
                            قيد التجهيز
                        </button>
                        <button class="order-filter-btn px-4 py-2 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white transition text-sm" data-status="completed">
                            مكتملة
                        </button>
                        <button class="order-filter-btn px-4 py-2 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white transition text-sm" data-status="cancelled">
                            ملغية
                        </button>
                    </div>
                    <div class="flex items-center space-x-4 space-x-reverse mr-auto">
                        <input type="date" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white text-sm">
                        <select class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white text-sm">
                            <option>آخر 30 يوم</option>
                            <option>آخر 3 أشهر</option>
                            <option>آخر سنة</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- قائمة الطلبات -->
            <div class="space-y-6" id="orders-container">
                <!-- طلب 1 -->
                <div class="order-card bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden" data-status="completed">
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <div class="flex items-center space-x-4 space-x-reverse mb-2">
                                    <h3 class="text-lg font-bold text-gray-800 dark:text-white">طلب #1254</h3>
                                    <span class="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-full text-sm">
                                        مكتمل
                                    </span>
                                </div>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">تم الطلب في 15 ديسمبر 2024 - 2:30 م</p>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">تم التسليم في 15 ديسمبر 2024 - 3:15 م</p>
                            </div>
                            <div class="text-right">
                                <p class="text-2xl font-bold text-gray-800 dark:text-white">85.50 ر.س</p>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">3 عناصر</p>
                            </div>
                        </div>

                        <!-- عناصر الطلب -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                            <div class="space-y-3">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <img src="https://images.unsplash.com/photo-1513104890138-7c749659a591?ixlib=rb-1.2.1&auto=format&fit=crop&w=60&q=80" alt="برجر لحم" class="w-12 h-12 object-cover rounded-md ml-3">
                                        <div>
                                            <p class="font-medium text-gray-800 dark:text-white">برجر لحم أنجوس</p>
                                            <p class="text-gray-600 dark:text-gray-400 text-sm">الكمية: 1</p>
                                        </div>
                                    </div>
                                    <span class="font-medium text-gray-800 dark:text-white">55.00 ر.س</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <img src="https://images.unsplash.com/photo-1461023058943-07fcbe16d735?ixlib=rb-1.2.1&auto=format&fit=crop&w=60&q=80" alt="عصير برتقال" class="w-12 h-12 object-cover rounded-md ml-3">
                                        <div>
                                            <p class="font-medium text-gray-800 dark:text-white">عصير برتقال طازج</p>
                                            <p class="text-gray-600 dark:text-gray-400 text-sm">الكمية: 2</p>
                                        </div>
                                    </div>
                                    <span class="font-medium text-gray-800 dark:text-white">30.50 ر.س</span>
                                </div>
                            </div>
                        </div>

                        <!-- إجراءات الطلب -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
                            <div class="flex justify-between items-center">
                                <div class="flex space-x-3 space-x-reverse">
                                    <button class="text-primary hover:text-primary/80 text-sm">
                                        <i class="fas fa-eye ml-1"></i>عرض التفاصيل
                                    </button>
                                    <button class="text-primary hover:text-primary/80 text-sm">
                                        <i class="fas fa-redo ml-1"></i>إعادة الطلب
                                    </button>
                                    <button class="text-primary hover:text-primary/80 text-sm">
                                        <i class="fas fa-download ml-1"></i>تحميل الفاتورة
                                    </button>
                                </div>
                                <div class="flex items-center text-yellow-400">
                                    <span class="text-gray-600 dark:text-gray-400 text-sm ml-2">تقييم الطلب:</span>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- طلب 2 -->
                <div class="order-card bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden" data-status="pending">
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <div class="flex items-center space-x-4 space-x-reverse mb-2">
                                    <h3 class="text-lg font-bold text-gray-800 dark:text-white">طلب #1255</h3>
                                    <span class="px-3 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 rounded-full text-sm">
                                        قيد التجهيز
                                    </span>
                                </div>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">تم الطلب في 18 ديسمبر 2024 - 7:45 م</p>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">الوقت المتوقع للتسليم: 8:30 م</p>
                            </div>
                            <div class="text-right">
                                <p class="text-2xl font-bold text-gray-800 dark:text-white">120.00 ر.س</p>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">4 عناصر</p>
                            </div>
                        </div>

                        <!-- شريط التقدم -->
                        <div class="mb-4">
                            <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
                                <span>تم استلام الطلب</span>
                                <span>قيد التجهيز</span>
                                <span>جاهز للتسليم</span>
                                <span>تم التسليم</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="bg-primary h-2 rounded-full" style="width: 50%"></div>
                            </div>
                        </div>

                        <!-- عناصر الطلب -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                            <div class="space-y-3">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <img src="https://images.unsplash.com/photo-1565299624946-b28f40a0ae38?ixlib=rb-1.2.1&auto=format&fit=crop&w=60&q=80" alt="بيتزا" class="w-12 h-12 object-cover rounded-md ml-3">
                                        <div>
                                            <p class="font-medium text-gray-800 dark:text-white">بيتزا سوبريم</p>
                                            <p class="text-gray-600 dark:text-gray-400 text-sm">الكمية: 1</p>
                                        </div>
                                    </div>
                                    <span class="font-medium text-gray-800 dark:text-white">65.00 ر.س</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <img src="https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-1.2.1&auto=format&fit=crop&w=60&q=80" alt="سلطة" class="w-12 h-12 object-cover rounded-md ml-3">
                                        <div>
                                            <p class="font-medium text-gray-800 dark:text-white">سلطة سيزر</p>
                                            <p class="text-gray-600 dark:text-gray-400 text-sm">الكمية: 1</p>
                                        </div>
                                    </div>
                                    <span class="font-medium text-gray-800 dark:text-white">45.00 ر.س</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <img src="https://images.unsplash.com/photo-1509042239860-f550ce710b93?ixlib=rb-1.2.1&auto=format&fit=crop&w=60&q=80" alt="قهوة" class="w-12 h-12 object-cover rounded-md ml-3">
                                        <div>
                                            <p class="font-medium text-gray-800 dark:text-white">قهوة عربية</p>
                                            <p class="text-gray-600 dark:text-gray-400 text-sm">الكمية: 2</p>
                                        </div>
                                    </div>
                                    <span class="font-medium text-gray-800 dark:text-white">10.00 ر.س</span>
                                </div>
                            </div>
                        </div>

                        <!-- إجراءات الطلب -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
                            <div class="flex justify-between items-center">
                                <div class="flex space-x-3 space-x-reverse">
                                    <button class="text-primary hover:text-primary/80 text-sm">
                                        <i class="fas fa-eye ml-1"></i>تتبع الطلب
                                    </button>
                                    <button class="text-red-600 hover:text-red-700 text-sm">
                                        <i class="fas fa-times ml-1"></i>إلغاء الطلب
                                    </button>
                                </div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">
                                    <i class="fas fa-clock ml-1"></i>
                                    الوقت المتبقي: 25 دقيقة
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- طلب 3 -->
                <div class="order-card bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden" data-status="cancelled">
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <div class="flex items-center space-x-4 space-x-reverse mb-2">
                                    <h3 class="text-lg font-bold text-gray-800 dark:text-white">طلب #1253</h3>
                                    <span class="px-3 py-1 bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded-full text-sm">
                                        ملغي
                                    </span>
                                </div>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">تم الطلب في 10 ديسمبر 2024 - 6:20 م</p>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">تم الإلغاء في 10 ديسمبر 2024 - 6:25 م</p>
                            </div>
                            <div class="text-right">
                                <p class="text-2xl font-bold text-gray-500 line-through">75.00 ر.س</p>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">2 عناصر</p>
                            </div>
                        </div>

                        <!-- سبب الإلغاء -->
                        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3 mb-4">
                            <p class="text-red-600 dark:text-red-400 text-sm">
                                <i class="fas fa-info-circle ml-1"></i>
                                تم إلغاء الطلب بناءً على طلب العميل
                            </p>
                        </div>

                        <!-- إجراءات الطلب -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                            <div class="flex space-x-3 space-x-reverse">
                                <button class="text-primary hover:text-primary/80 text-sm">
                                    <i class="fas fa-redo ml-1"></i>إعادة الطلب
                                </button>
                                <button class="text-primary hover:text-primary/80 text-sm">
                                    <i class="fas fa-eye ml-1"></i>عرض التفاصيل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- رسالة عدم وجود طلبات -->
            <div id="no-orders" class="text-center py-12 hidden">
                <div class="w-24 h-24 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-shopping-bag text-gray-400 text-3xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-2">لا توجد طلبات</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-6">لم تقم بأي طلبات بعد</p>
                <button data-page="menu" class="bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg transition">
                    <i class="fas fa-plus ml-2"></i>
                    اطلب الآن
                </button>
            </div>
        </div>
    </div>
</div>
@endauth

@guest
<!-- صفحة تسجيل الدخول للمستخدمين غير المسجلين -->
<div id="orders-page" class="page hidden">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-md mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center">
            <div class="w-20 h-20 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-user-lock text-primary text-3xl"></i>
            </div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">تسجيل الدخول مطلوب</h2>
            <p class="text-gray-600 dark:text-gray-400 mb-6">
                يجب تسجيل الدخول لعرض طلباتك
            </p>
            <div class="space-y-4">
                <button id="loginBtn" class="w-full bg-primary hover:bg-primary/90 text-white font-bold py-3 px-6 rounded-lg transition">
                    <i class="fas fa-sign-in-alt ml-2"></i>
                    تسجيل الدخول
                </button>
                <button data-page="home" class="w-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-white font-bold py-3 px-6 rounded-lg transition">
                    <i class="fas fa-home ml-2"></i>
                    العودة للرئيسية
                </button>
            </div>
        </div>
    </div>
</div>
@endguest

<script>
// فلترة الطلبات
document.addEventListener('DOMContentLoaded', function() {
    const filterButtons = document.querySelectorAll('.order-filter-btn');
    const orderCards = document.querySelectorAll('.order-card');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const status = this.getAttribute('data-status');
            
            // تحديث حالة الأزرار
            filterButtons.forEach(btn => {
                btn.classList.remove('active', 'bg-primary', 'text-white');
                btn.classList.add('bg-gray-200', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');
            });
            
            this.classList.add('active', 'bg-primary', 'text-white');
            this.classList.remove('bg-gray-200', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');
            
            // فلترة الطلبات
            let visibleCount = 0;
            orderCards.forEach(card => {
                if (status === 'all' || card.getAttribute('data-status') === status) {
                    card.style.display = 'block';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });
            
            // إظهار رسالة عدم وجود طلبات
            const noOrdersMessage = document.getElementById('no-orders');
            if (visibleCount === 0) {
                noOrdersMessage.classList.remove('hidden');
            } else {
                noOrdersMessage.classList.add('hidden');
            }
        });
    });
});
</script>
