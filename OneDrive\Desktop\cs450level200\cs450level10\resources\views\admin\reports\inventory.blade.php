@extends('layouts.admin')

@section('title', 'تقرير المخزون')

@section('content')
<div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
    <div>
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white">تقرير المخزون</h2>
        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">عرض تفاصيل المخزون وإحصائياته</p>
    </div>
    <div class="mt-4 md:mt-0">
        <a href="{{ route('admin.reports') }}" class="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
            <i class="fas fa-arrow-right ml-2"></i>
            <span>العودة للتقارير</span>
        </a>
    </div>
</div>

<!-- ملخص المخزون -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="rounded-full bg-blue-100 dark:bg-blue-900/30 p-3 ml-4">
                <i class="fas fa-boxes text-blue-500 text-xl"></i>
            </div>
            <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">إجمالي المكونات</p>
                <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ $totalIngredients }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="rounded-full bg-yellow-100 dark:bg-yellow-900/30 p-3 ml-4">
                <i class="fas fa-exclamation-triangle text-yellow-500 text-xl"></i>
            </div>
            <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">المكونات منخفضة المخزون</p>
                <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ $lowStockCount }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="rounded-full bg-red-100 dark:bg-red-900/30 p-3 ml-4">
                <i class="fas fa-times-circle text-red-500 text-xl"></i>
            </div>
            <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">المكونات غير المتوفرة</p>
                <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ $outOfStockCount }}</p>
            </div>
        </div>
    </div>
</div>

<!-- قيمة المخزون -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
    <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">قيمة المخزون</h3>
    <div class="flex items-center">
        <div class="rounded-full bg-green-100 dark:bg-green-900/30 p-3 ml-4">
            <i class="fas fa-money-bill-wave text-green-500 text-xl"></i>
        </div>
        <div>
            <p class="text-3xl font-bold text-gray-800 dark:text-white">{{ number_format($totalInventoryValue, 2) }} د.ل</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">إجمالي قيمة المخزون الحالي</p>
        </div>
    </div>
</div>

<!-- حالة المخزون -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
    <!-- رسم بياني لحالة المخزون -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">حالة المخزون</h3>
        <div class="h-64">
            <canvas id="inventoryStatusChart"></canvas>
        </div>
    </div>

    <!-- أعلى 5 مكونات من حيث القيمة -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">أعلى 5 مكونات من حيث القيمة</h3>
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المكون</th>
                        <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الكمية</th>
                        <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">وحدة القياس</th>
                        <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">القيمة</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($topValueIngredients as $ingredient)
                        <tr>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-800 dark:text-white">{{ $ingredient->name }}</td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-800 dark:text-white">{{ $ingredient->quantity }}</td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-800 dark:text-white">{{ $ingredient->unit }}</td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-800 dark:text-white">{{ number_format($ingredient->value, 2) }} د.ل</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- قائمة المخزون -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
    <div class="p-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white">قائمة المخزون</h3>

        <!-- فلتر البحث -->
        <div class="flex items-center">
            <form action="{{ route('admin.reports.inventory') }}" method="GET" class="flex">
                <input type="text" name="search" value="{{ request('search') }}" placeholder="بحث..." class="px-4 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                <button type="submit" class="px-4 py-2 bg-primary text-white rounded-l-md hover:bg-primary/90 transition-all">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="w-full">
            <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المكون</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الكمية</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">وحدة القياس</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">سعر الوحدة</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">القيمة الإجمالية</th>

                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الحالة</th>
                </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                @foreach($inventory as $item)
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-white">{{ $item->name }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-white">{{ $item->quantity }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-white">{{ $item->unit }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-white">{{ number_format($item->unit_price, 2) }} د.ل</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-white">{{ number_format($item->quantity * $item->unit_price, 2) }} د.ل</td>

                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            @if($item->quantity <= 0)
                                <span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400">غير متوفر</span>
                            @elseif($item->quantity < 10)
                                <span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400">منخفض</span>
                            @else
                                <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">متوفر</span>
                            @endif
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="p-6">
        {{ $inventory->links() }}
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // رسم بياني لحالة المخزون
        const statusCtx = document.getElementById('inventoryStatusChart').getContext('2d');
        const statusData = {
            labels: ['متوفر', 'منخفض', 'غير متوفر'],
            datasets: [{
                data: [{{ $inStockCount }}, {{ $lowStockCount }}, {{ $outOfStockCount }}],
                backgroundColor: [
                    'rgba(72, 187, 120, 0.7)',
                    'rgba(237, 137, 54, 0.7)',
                    'rgba(229, 62, 62, 0.7)'
                ],
                borderColor: [
                    'rgba(72, 187, 120, 1)',
                    'rgba(237, 137, 54, 1)',
                    'rgba(229, 62, 62, 1)'
                ],
                borderWidth: 1
            }]
        };

        new Chart(statusCtx, {
            type: 'pie',
            data: statusData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                family: 'Tajawal'
                            }
                        }
                    }
                }
            }
        });
    });
</script>
@endpush
@endsection
