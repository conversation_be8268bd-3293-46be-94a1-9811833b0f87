<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Reservation;
use App\Models\Notification;
use App\Models\Table;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Routing\Controller;

class EmployeeController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('employee');
    }

    public function dashboard()
    {
        try {
            // Get recent orders
            $pendingOrders = Order::with(['user', 'table'])
                ->where('status', 'pending')
                ->orderBy('created_at', 'desc')
                ->take(5)
                ->get();

            // Get today's reservations
            $todayReservations = Reservation::with(['user', 'table'])
                ->where('reservation_time', '>=', now()->startOfDay())
                ->where('reservation_time', '<=', now()->endOfDay())
                ->where('status', 'confirmed')
                ->orderBy('reservation_time')
                ->get();

            // Get table status
            $tables = Table::all();

            // Count tables by status
            $tableStats = [
                'available' => $tables->where('status', 'available')->count(),
                'occupied' => $tables->where('status', 'occupied')->count(),
                'reserved' => $tables->where('status', 'reserved')->count(),
                'total' => $tables->count()
            ];

            // Get low stock ingredients
            $lowStock = DB::table('inventory')
                ->join('ingredients', 'inventory.ingredient_id', '=', 'ingredients.ingredient_id')
                ->select('ingredients.name', 'inventory.quantity', 'ingredients.unit')
                ->where('inventory.quantity', '<', 10)
                ->get();

            // Get unread notifications
            $notifications = Notification::where('user_id', Auth::id())
                ->where('is_read', false)
                ->orderBy('created_at', 'desc')
                ->take(5)
                ->get();

            // Get today's stats
            $todayStats = [
                'ordersCount' => Order::whereDate('created_at', today())->count(),
                'reservationsCount' => Reservation::whereDate('reservation_time', today())->count(),
                'totalSales' => Order::whereDate('created_at', today())->sum('total_amount'),
                'pendingOrdersCount' => Order::where('status', 'pending')->count()
            ];

            return view('employee.dashboard', compact(
                'pendingOrders',
                'todayReservations',
                'tables',
                'tableStats',
                'lowStock',
                'notifications',
                'todayStats'
            ));
        } catch (\Exception $e) {
            // في حالة وجود خطأ، عرض الصفحة بدون بيانات
            \Log::error('Error in employee dashboard: ' . $e->getMessage());
            return view('employee.dashboard');
        }
    }

    // عرض صفحة الإشعارات
    public function notifications(Request $request)
    {
        try {
            $query = Notification::where('user_id', Auth::id());

            // تطبيق الفلتر إذا تم تحديده
            if ($request->has('filter')) {
                if ($request->filter === 'read') {
                    $query->where('is_read', true);
                } elseif ($request->filter === 'unread') {
                    $query->where('is_read', false);
                }
            }

            $notifications = $query->orderBy('created_at', 'desc')
                ->paginate(10)
                ->withQueryString();

            return view('employee.notifications.index', compact('notifications'));
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error in employee notifications: ' . $e->getMessage());
            return view('employee.notifications.index')->with('error', 'حدث خطأ أثناء تحميل الإشعارات');
        }
    }

    // عرض تفاصيل الإشعار
    public function showNotification($id)
    {
        try {
            $notification = Notification::where('user_id', Auth::id())
                ->where('notification_id', $id)
                ->firstOrFail();

            // تحديث حالة الإشعار إلى مقروء عند عرضه
            if (!$notification->is_read) {
                $notification->is_read = true;
                $notification->save();
            }

            return view('employee.notifications.show', compact('notification'));
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error showing notification: ' . $e->getMessage());
            return redirect()->route('employee.notifications')->with('error', 'الإشعار غير موجود');
        }
    }

    // تحديد إشعار كمقروء
    public function markNotificationAsRead($id)
    {
        try {
            $notification = Notification::where('user_id', Auth::id())
                ->where('notification_id', $id)
                ->firstOrFail();

            $notification->is_read = true;
            $notification->save();

            // إذا كان الطلب AJAX، أرجع JSON response
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم تحديد الإشعار كمقروء'
                ]);
            }

            return redirect()->back()->with('success', 'تم تحديد الإشعار كمقروء');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error marking notification as read: ' . $e->getMessage());

            if (request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'حدث خطأ أثناء تحديد الإشعار كمقروء'
                ], 500);
            }

            return redirect()->back()->with('error', 'حدث خطأ أثناء تحديد الإشعار كمقروء');
        }
    }

    // تحديد جميع الإشعارات كمقروءة
    public function markAllNotificationsAsRead()
    {
        try {
            $updatedCount = Notification::where('user_id', Auth::id())
                ->where('is_read', false)
                ->update(['is_read' => true]);

            // إذا كان الطلب AJAX، أرجع JSON response
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم تحديد جميع الإشعارات كمقروءة',
                    'updated_count' => $updatedCount
                ]);
            }

            return redirect()->back()->with('success', 'تم تحديد جميع الإشعارات كمقروءة');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error marking all notifications as read: ' . $e->getMessage());

            if (request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'حدث خطأ أثناء تحديد جميع الإشعارات كمقروءة'
                ], 500);
            }

            return redirect()->back()->with('error', 'حدث خطأ أثناء تحديد جميع الإشعارات كمقروءة');
        }
    }

    // حذف إشعار
    public function deleteNotification($id)
    {
        try {
            $notification = Notification::where('user_id', Auth::id())
                ->where('notification_id', $id)
                ->firstOrFail();

            $notification->delete();

            return redirect()->route('employee.notifications')->with('success', 'تم حذف الإشعار بنجاح');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error deleting notification: ' . $e->getMessage());
            return redirect()->back()->with('error', 'حدث خطأ أثناء حذف الإشعار');
        }
    }

    // عرض صفحة الطاولات
    public function tables()
    {
        try {
            $tables = Table::orderBy('table_number')->paginate(10);

            // Count tables by status
            $availableTables = Table::where('status', 'available')->count();
            $occupiedTables = Table::where('status', 'occupied')->count();
            $reservedTables = Table::where('status', 'reserved')->count();

            return view('employee.tables', compact('tables', 'availableTables', 'occupiedTables', 'reservedTables'));
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error in employee tables: ' . $e->getMessage());
            return view('employee.tables')->with('error', 'حدث خطأ أثناء تحميل الصفحة');
        }
    }

    // عرض نموذج إضافة طاولة جديدة
    public function createTable()
    {
        return view('employee.tables.create');
    }

    // حفظ طاولة جديدة
    public function storeTable(Request $request)
    {
        $request->validate([
            'table_number' => 'required|integer|unique:tables,table_number',
            'capacity' => 'required|integer|min:1',
            'status' => 'required|in:available,occupied,reserved',
            'location' => 'required|string',
        ], [
            'table_number.required' => 'رقم الطاولة مطلوب',
            'table_number.integer' => 'رقم الطاولة يجب أن يكون رقماً',
            'table_number.unique' => 'رقم الطاولة مستخدم بالفعل',
            'capacity.required' => 'سعة الطاولة مطلوبة',
            'capacity.integer' => 'سعة الطاولة يجب أن تكون رقماً',
            'capacity.min' => 'سعة الطاولة يجب أن تكون على الأقل 1',
            'status.required' => 'حالة الطاولة مطلوبة',
            'status.in' => 'حالة الطاولة غير صالحة',
            'location.required' => 'موقع الطاولة مطلوب',
        ]);

        try {
            $table = new Table();
            $table->table_number = $request->table_number;
            $table->capacity = $request->capacity;
            $table->status = $request->status;
            $table->location = $request->location;
            $table->save();

            return redirect()->route('employee.tables')->with('success', 'تمت إضافة الطاولة بنجاح');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error creating table: ' . $e->getMessage());
            return redirect()->back()->with('error', 'حدث خطأ أثناء إضافة الطاولة')->withInput();
        }
    }

    // عرض تفاصيل الطاولة
    public function showTable($id)
    {
        try {
            $table = Table::findOrFail($id);

            // Get current order for this table if it's occupied
            $currentOrder = null;
            if ($table->status == 'occupied') {
                $currentOrder = Order::where('table_id', $id)
                    ->where('status', 'pending')
                    ->orWhere('status', 'preparing')
                    ->with('user')
                    ->latest()
                    ->first();
            }

            // Get recent orders for this table
            $recentOrders = Order::where('table_id', $id)
                ->with('user')
                ->latest()
                ->take(5)
                ->get();

            // Get stats
            $ordersCount = Order::where('table_id', $id)->count();
            $totalSales = Order::where('table_id', $id)->sum('total_amount');
            $lastOrder = Order::where('table_id', $id)->latest()->first();

            return view('employee.tables.show', compact('table', 'currentOrder', 'recentOrders', 'ordersCount', 'totalSales', 'lastOrder'));
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error showing table: ' . $e->getMessage());
            return redirect()->route('employee.tables')->with('error', 'الطاولة غير موجودة');
        }
    }

    // عرض نموذج تعديل الطاولة
    public function editTable($id)
    {
        try {
            $table = Table::findOrFail($id);
            return view('employee.tables.edit', compact('table'));
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error editing table: ' . $e->getMessage());
            return redirect()->route('employee.tables')->with('error', 'الطاولة غير موجودة');
        }
    }

    // تحديث بيانات الطاولة
    public function updateTable(Request $request, $id)
    {
        $request->validate([
            'table_number' => 'required|integer|unique:tables,table_number,' . $id . ',table_id',
            'capacity' => 'required|integer|min:1',
            'status' => 'required|in:available,occupied,reserved',
            'location' => 'required|string',
        ], [
            'table_number.required' => 'رقم الطاولة مطلوب',
            'table_number.integer' => 'رقم الطاولة يجب أن يكون رقماً',
            'table_number.unique' => 'رقم الطاولة مستخدم بالفعل',
            'capacity.required' => 'سعة الطاولة مطلوبة',
            'capacity.integer' => 'سعة الطاولة يجب أن تكون رقماً',
            'capacity.min' => 'سعة الطاولة يجب أن تكون على الأقل 1',
            'status.required' => 'حالة الطاولة مطلوبة',
            'status.in' => 'حالة الطاولة غير صالحة',
            'location.required' => 'موقع الطاولة مطلوب',
        ]);

        try {
            $table = Table::findOrFail($id);
            $table->table_number = $request->table_number;
            $table->capacity = $request->capacity;
            $table->status = $request->status;
            $table->location = $request->location;
            $table->save();

            return redirect()->route('employee.tables')->with('success', 'تم تحديث الطاولة بنجاح');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error updating table: ' . $e->getMessage());
            return redirect()->back()->with('error', 'حدث خطأ أثناء تحديث الطاولة')->withInput();
        }
    }

    // حذف الطاولة
    public function destroyTable($id)
    {
        try {
            $table = Table::findOrFail($id);

            // Check if table has active orders
            $hasActiveOrders = Order::where('table_id', $id)
                ->whereIn('status', ['pending', 'preparing'])
                ->exists();

            if ($hasActiveOrders) {
                return redirect()->route('employee.tables')->with('error', 'لا يمكن حذف الطاولة لأنها تحتوي على طلبات نشطة');
            }

            $table->delete();
            return redirect()->route('employee.tables')->with('success', 'تم حذف الطاولة بنجاح');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error deleting table: ' . $e->getMessage());
            return redirect()->route('employee.tables')->with('error', 'حدث خطأ أثناء حذف الطاولة');
        }
    }

    // تحديث حالة الطاولة
    public function updateTableStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:available,occupied,reserved',
        ], [
            'status.required' => 'حالة الطاولة مطلوبة',
            'status.in' => 'حالة الطاولة غير صالحة',
        ]);

        try {
            $table = Table::findOrFail($id);

            // Check if table has active orders when trying to mark as available
            if ($request->status == 'available' && $table->status == 'occupied') {
                $hasActiveOrders = Order::where('table_id', $id)
                    ->whereIn('status', ['pending', 'preparing'])
                    ->exists();

                if ($hasActiveOrders) {
                    return redirect()->back()->with('error', 'لا يمكن تغيير حالة الطاولة إلى متاح لأنها تحتوي على طلبات نشطة');
                }
            }

            $table->status = $request->status;
            $table->save();

            return redirect()->back()->with('success', 'تم تحديث حالة الطاولة بنجاح');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error updating table status: ' . $e->getMessage());
            return redirect()->back()->with('error', 'حدث خطأ أثناء تحديث حالة الطاولة');
        }
    }

    // عرض صفحة قائمة الطعام
    public function menu()
    {
        try {
            $menuItems = \App\Models\MenuItem::where('is_available', true)
                ->orderBy('category')
                ->orderBy('name')
                ->get();

            $categories = $menuItems->pluck('category')->unique();

            return view('employee.menu', compact('menuItems', 'categories'));
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error in employee menu: ' . $e->getMessage());
            return view('employee.menu');
        }
    }

    // عرض صفحة الملف الشخصي
    public function profile()
    {
        $user = Auth::user();
        return view('employee.profile', compact('user'));
    }

    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:50',
            'last_name' => 'required|string|max:50',
            'email' => 'required|email|unique:users,email,' . $user->user_id . ',user_id',
            'phone' => 'required|string|max:20',
            'current_password' => 'nullable|required_with:password',
            'password' => 'nullable|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // التحقق من كلمة المرور الحالية إذا تم تقديمها
        if ($request->filled('current_password') && !Hash::check($request->current_password, $user->password)) {
            return redirect()->back()->withErrors(['current_password' => 'كلمة المرور الحالية غير صحيحة'])->withInput();
        }

        $data = [
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'email' => $request->email,
            'phone' => $request->phone,
        ];

        if ($request->filled('password')) {
            $data['password'] = Hash::make($request->password);
        }

        User::where('user_id', $user->user_id)->update($data);

        return redirect()->route('employee.profile')->with('success', 'تم تحديث الملف الشخصي بنجاح');
    }

    // عرض صفحة الطلبات
    public function orders()
    {
        try {
            $orders = Order::with(['user', 'table'])
                ->orderBy('created_at', 'desc')
                ->paginate(10);
            return view('employee.orders.index', compact('orders'));
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error loading orders: ' . $e->getMessage());
            return view('employee.orders.index', ['orders' => collect()]);
        }
    }

    // عرض صفحة الحجوزات
    public function reservations()
    {
        try {
            $reservations = Reservation::with(['user', 'table'])
                ->orderBy('reservation_time', 'desc')
                ->paginate(10);
            return view('employee.reservations.index', compact('reservations'));
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error loading reservations: ' . $e->getMessage());
            return view('employee.reservations.index', ['reservations' => collect()]);
        }
    }

    // عرض صفحة المدفوعات - تم نقلها إلى PaymentController
    // public function payments()
    // {
    //     try {
    //         return view('employee.payments');
    //     } catch (\Exception $e) {
    //         return view('employee.payments');
    //     }
    // }

    // عرض صفحة الإعدادات
    public function settings()
    {
        try {
            $user = Auth::user();
            $settings = DB::table('settings')->first();

            return view('employee.settings', compact('user', 'settings'));
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error in employee settings: ' . $e->getMessage());
            return view('employee.settings')->with('error', 'حدث خطأ أثناء تحميل الإعدادات');
        }
    }

    // تحديث إعدادات الموظف
    public function updateSettings(Request $request)
    {
        try {
            $user = Auth::user();

            // تحديث إعدادات المستخدم
            $validator = Validator::make($request->all(), [
                'language' => 'required|in:ar,en',
                'theme' => 'required|in:light,dark,system',
                'notifications_enabled' => 'boolean',
                'email_notifications' => 'boolean',
            ]);

            if ($validator->fails()) {
                return redirect()->back()->withErrors($validator)->withInput();
            }

            // تحديث إعدادات المستخدم في قاعدة البيانات
            $userSettings = [
                'language' => $request->language,
                'theme' => $request->theme,
                'notifications_enabled' => $request->has('notifications_enabled') ? 1 : 0,
                'email_notifications' => $request->has('email_notifications') ? 1 : 0,
            ];

            // تحديث الإعدادات في جدول المستخدمين أو جدول منفصل للإعدادات
            User::where('user_id', $user->user_id)->update([
                'settings' => json_encode($userSettings)
            ]);

            return redirect()->route('employee.settings')->with('success', 'تم تحديث الإعدادات بنجاح');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error updating employee settings: ' . $e->getMessage());
            return redirect()->back()->with('error', 'حدث خطأ أثناء تحديث الإعدادات')->withInput();
        }
    }

    // عرض صفحة المساعدة
    public function help()
    {
        try {
            // يمكن استرجاع أقسام المساعدة من قاعدة البيانات إذا كانت متوفرة
            $helpSections = DB::table('help_sections')
                ->where('user_type', 'employee')
                ->orWhere('user_type', 'all')
                ->orderBy('order')
                ->get();

            // إذا لم تكن هناك أقسام مساعدة في قاعدة البيانات، استخدم بيانات افتراضية
            if ($helpSections->isEmpty()) {
                $helpSections = $this->getDefaultHelpSections();
            }

            $faqs = DB::table('faqs')
                ->where('user_type', 'employee')
                ->orWhere('user_type', 'all')
                ->orderBy('order')
                ->get();

            return view('employee.help', compact('helpSections', 'faqs'));
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error in employee help page: ' . $e->getMessage());
            return view('employee.help')->with('error', 'حدث خطأ أثناء تحميل صفحة المساعدة');
        }
    }

    // الحصول على أقسام المساعدة الافتراضية
    private function getDefaultHelpSections()
    {
        return collect([
            (object)[
                'title' => 'لوحة التحكم',
                'icon' => 'fa-tachometer-alt',
                'content' => 'تعرض لوحة التحكم ملخصًا للمعلومات المهمة مثل الطلبات الحالية، والطاولات المتاحة، والإشعارات الأخيرة. يمكنك الوصول إلى جميع الوظائف الرئيسية من هنا.',
                'order' => 1
            ],
            (object)[
                'title' => 'إدارة الطلبات',
                'icon' => 'fa-shopping-cart',
                'content' => 'يمكنك إنشاء طلبات جديدة، وعرض الطلبات الحالية، وتحديث حالة الطلبات، وطباعة الإيصالات. انقر على "الطلبات" في القائمة الجانبية للوصول إلى هذه الوظائف.',
                'order' => 2
            ],
            (object)[
                'title' => 'إدارة الطاولات',
                'icon' => 'fa-chair',
                'content' => 'يمكنك عرض حالة الطاولات، وتحديث حالتها، وإدارة الحجوزات. انقر على "الطاولات" في القائمة الجانبية للوصول إلى هذه الوظائف.',
                'order' => 3
            ],
            (object)[
                'title' => 'إدارة المخزون',
                'icon' => 'fa-boxes',
                'content' => 'يمكنك عرض المخزون الحالي، وإضافة عناصر جديدة، وتحديث الكميات. انقر على "المخزون" في القائمة الجانبية للوصول إلى هذه الوظائف.',
                'order' => 4
            ],
            (object)[
                'title' => 'الإشعارات',
                'icon' => 'fa-bell',
                'content' => 'ستتلقى إشعارات عن الطلبات الجديدة، والتغييرات في حالة الطلبات، والتذكيرات بالحجوزات. يمكنك الوصول إلى الإشعارات من خلال أيقونة الجرس في الشريط العلوي.',
                'order' => 5
            ],
            (object)[
                'title' => 'الملف الشخصي والإعدادات',
                'icon' => 'fa-user-cog',
                'content' => 'يمكنك تحديث معلومات ملفك الشخصي وتغيير كلمة المرور من خلال صفحة "الملف الشخصي". يمكنك أيضًا تخصيص إعدادات التطبيق من خلال صفحة "الإعدادات".',
                'order' => 6
            ]
        ]);
    }

    // عرض طلب الطاولة
    public function viewTableOrder(Request $request)
    {
        try {
            $tableId = $request->query('table_id');

            if (!$tableId) {
                return redirect()->route('employee.tables')->with('error', 'معرف الطاولة مطلوب');
            }

            $table = Table::findOrFail($tableId);

            // التحقق من أن الطاولة مشغولة
            if ($table->status != 'occupied') {
                return redirect()->route('employee.tables')->with('error', 'الطاولة غير مشغولة حالياً');
            }

            // البحث عن الطلب النشط للطاولة
            $order = Order::where('table_id', $tableId)
                ->whereIn('status', ['pending', 'preparing'])
                ->with(['user', 'orderItems.menuItem'])
                ->latest()
                ->first();

            if (!$order) {
                return redirect()->route('employee.tables')->with('error', 'لا يوجد طلب نشط لهذه الطاولة');
            }

            return view('employee.tables.view-order', compact('table', 'order'));
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error viewing table order: ' . $e->getMessage());
            return redirect()->route('employee.tables')->with('error', 'حدث خطأ أثناء عرض طلب الطاولة');
        }
    }

    // تحرير الطاولة (تغيير حالتها إلى متاحة)
    public function freeTable(Request $request)
    {
        try {
            $tableId = $request->query('table_id');

            if (!$tableId) {
                return redirect()->route('employee.tables')->with('error', 'معرف الطاولة مطلوب');
            }

            $table = Table::findOrFail($tableId);

            // التحقق من أن الطاولة مشغولة
            if ($table->status != 'occupied') {
                return redirect()->route('employee.tables')->with('error', 'الطاولة غير مشغولة حالياً');
            }

            // التحقق من وجود طلبات نشطة
            $hasActiveOrders = Order::where('table_id', $tableId)
                ->whereIn('status', ['pending', 'preparing'])
                ->exists();

            if ($hasActiveOrders) {
                return redirect()->route('employee.tables')->with('error', 'لا يمكن تحرير الطاولة لأنها تحتوي على طلبات نشطة');
            }

            // تغيير حالة الطاولة إلى متاحة
            $table->status = 'available';
            $table->save();

            return redirect()->route('employee.tables')->with('success', 'تم تحرير الطاولة بنجاح');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error freeing table: ' . $e->getMessage());
            return redirect()->route('employee.tables')->with('error', 'حدث خطأ أثناء تحرير الطاولة');
        }
    }

    // إلغاء حجز الطاولة
    public function cancelTableReservation(Request $request)
    {
        try {
            $tableId = $request->query('table_id');

            if (!$tableId) {
                return redirect()->route('employee.tables')->with('error', 'معرف الطاولة مطلوب');
            }

            $table = Table::findOrFail($tableId);

            // التحقق من أن الطاولة محجوزة
            if ($table->status != 'reserved') {
                return redirect()->route('employee.tables')->with('error', 'الطاولة غير محجوزة حالياً');
            }

            // البحث عن الحجز النشط للطاولة
            $reservation = Reservation::where('table_id', $tableId)
                ->where('status', 'confirmed')
                ->where('reservation_time', '>=', now())
                ->first();

            if ($reservation) {
                // تغيير حالة الحجز إلى ملغي
                $reservation->status = 'cancelled';
                $reservation->save();
            }

            // تغيير حالة الطاولة إلى متاحة
            $table->status = 'available';
            $table->save();

            return redirect()->route('employee.tables')->with('success', 'تم إلغاء حجز الطاولة بنجاح');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error cancelling table reservation: ' . $e->getMessage());
            return redirect()->route('employee.tables')->with('error', 'حدث خطأ أثناء إلغاء حجز الطاولة');
        }
    }

    // التقارير
    public function reports()
    {
        try {
            // إحصائيات عامة
            $totalOrders = Order::count();
            $totalReservations = Reservation::count();
            $totalRevenue = Order::where('status', 'completed')->sum('total_amount');

            // إحصائيات اليوم
            $todayOrders = Order::whereDate('created_at', today())->count();
            $todayRevenue = Order::whereDate('created_at', today())
                ->where('status', 'completed')
                ->sum('total_amount');

            // إحصائيات الأسبوع
            $weekOrders = Order::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count();
            $weekRevenue = Order::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])
                ->where('status', 'completed')
                ->sum('total_amount');

            // إحصائيات الشهر
            $monthOrders = Order::whereMonth('created_at', now()->month)->count();
            $monthRevenue = Order::whereMonth('created_at', now()->month)
                ->where('status', 'completed')
                ->sum('total_amount');

            return view('employee.reports.index', compact(
                'totalOrders', 'totalReservations', 'totalRevenue',
                'todayOrders', 'todayRevenue',
                'weekOrders', 'weekRevenue',
                'monthOrders', 'monthRevenue'
            ));
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error loading reports: ' . $e->getMessage());
            return redirect()->route('employee.dashboard')->with('error', 'حدث خطأ أثناء تحميل التقارير');
        }
    }

    public function dailyReport()
    {
        try {
            $date = request('date', today());

            $orders = Order::with(['user', 'table'])
                ->whereDate('created_at', $date)
                ->orderBy('created_at', 'desc')
                ->get();

            $reservations = Reservation::with(['user', 'table'])
                ->whereDate('reservation_time', $date)
                ->orderBy('reservation_time')
                ->get();

            $revenue = Order::whereDate('created_at', $date)
                ->where('status', 'completed')
                ->sum('total_amount');

            return view('employee.reports.daily', compact('orders', 'reservations', 'revenue', 'date'));
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error loading daily report: ' . $e->getMessage());
            return redirect()->route('employee.reports')->with('error', 'حدث خطأ أثناء تحميل التقرير اليومي');
        }
    }

    public function weeklyReport()
    {
        try {
            $startDate = request('start_date', now()->startOfWeek());
            $endDate = request('end_date', now()->endOfWeek());

            $orders = Order::with(['user', 'table'])
                ->whereBetween('created_at', [$startDate, $endDate])
                ->orderBy('created_at', 'desc')
                ->get();

            $revenue = Order::whereBetween('created_at', [$startDate, $endDate])
                ->where('status', 'completed')
                ->sum('total_amount');

            return view('employee.reports.weekly', compact('orders', 'revenue', 'startDate', 'endDate'));
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error loading weekly report: ' . $e->getMessage());
            return redirect()->route('employee.reports')->with('error', 'حدث خطأ أثناء تحميل التقرير الأسبوعي');
        }
    }

    public function monthlyReport()
    {
        try {
            $month = request('month', now()->month);
            $year = request('year', now()->year);

            $orders = Order::with(['user', 'table'])
                ->whereMonth('created_at', $month)
                ->whereYear('created_at', $year)
                ->orderBy('created_at', 'desc')
                ->get();

            $revenue = Order::whereMonth('created_at', $month)
                ->whereYear('created_at', $year)
                ->where('status', 'completed')
                ->sum('total_amount');

            return view('employee.reports.monthly', compact('orders', 'revenue', 'month', 'year'));
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error loading monthly report: ' . $e->getMessage());
            return redirect()->route('employee.reports')->with('error', 'حدث خطأ أثناء تحميل التقرير الشهري');
        }
    }

    // العملاء
    public function customers()
    {
        try {
            $customers = User::where('user_type', 'customer')
                ->withCount(['orders', 'reservations'])
                ->orderBy('created_at', 'desc')
                ->paginate(20);

            return view('employee.customers.index', compact('customers'));
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error loading customers: ' . $e->getMessage());
            return redirect()->route('employee.dashboard')->with('error', 'حدث خطأ أثناء تحميل قائمة العملاء');
        }
    }

    public function showCustomer($id)
    {
        try {
            $customer = User::where('user_type', 'customer')
                ->with(['orders.table', 'reservations.table'])
                ->findOrFail($id);

            return view('employee.customers.show', compact('customer'));
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error loading customer details: ' . $e->getMessage());
            return redirect()->route('employee.customers')->with('error', 'حدث خطأ أثناء تحميل تفاصيل العميل');
        }
    }
}