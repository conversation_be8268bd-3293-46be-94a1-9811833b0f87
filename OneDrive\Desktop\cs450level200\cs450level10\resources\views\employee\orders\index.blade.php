@extends('employee.layouts.app')

@section('title', 'إدارة الطلبات')

@section('content')
<div id="orders-page" class="page fade-in">
    <!-- هيدر الصفحة المبدع -->
    <div class="mb-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h2 class="text-3xl font-bold text-gradient-ocean mb-2">إدارة الطلبات</h2>
                <p class="text-blue-600 dark:text-blue-400 flex items-center">
                    <i class="fas fa-shopping-cart mr-2"></i>
                    إدارة وتتبع جميع طلبات المطعم
                </p>
                <div class="w-20 h-1 bg-gradient-ocean rounded-full mt-2"></div>
            </div>
            <div class="mt-4 md:mt-0">
                <div class="p-4 rounded-xl bg-gradient-ocean text-white shadow-ocean">
                    <i class="fas fa-shopping-cart text-3xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- شريط البحث والإجراءات المبدع -->
    <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 sm:space-x-reverse flex-1">
            <form action="{{ route('employee.orders') }}" method="GET" class="relative flex-1">
                @if(request('status'))
                    <input type="hidden" name="status" value="{{ request('status') }}">
                @endif
                <div class="relative">
                    <input type="text" name="search" placeholder="بحث برقم الطلب أو اسم العميل..." value="{{ request('search') }}" class="w-full px-4 py-3 pr-12 rounded-xl border-2 border-blue-200 dark:border-blue-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base transition-all duration-300 shadow-sm focus:shadow-lg">
                    <button type="submit" class="absolute left-3 top-1/2 transform -translate-y-1/2 p-2 rounded-lg text-blue-500 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-all duration-300">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
            <a href="{{ route('employee.orders.create') }}" class="btn-magical inline-flex items-center px-6 py-3 bg-gradient-ocean text-white font-bold rounded-xl shadow-ocean hover:shadow-xl transition-all duration-300 group">
                <i class="fas fa-plus mr-2 group-hover:rotate-90 transition-transform duration-300"></i>
                <span>إنشاء طلب</span>
            </a>
        </div>
    </div>

    <!-- رسائل النجاح والخطأ المبدعة -->
    @if(session('success'))
    <div class="mb-6 p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-2 border-green-200 dark:border-green-800 rounded-xl shadow-lg" role="alert">
        <div class="flex items-center">
            <div class="p-2 rounded-lg bg-gradient-forest text-white mr-3">
                <i class="fas fa-check-circle"></i>
            </div>
            <div>
                <h4 class="font-bold text-green-800 dark:text-green-400">نجح!</h4>
                <span class="text-green-700 dark:text-green-300">{{ session('success') }}</span>
            </div>
        </div>
    </div>
    @endif

    @if(session('error'))
    <div class="mb-6 p-4 bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 border-2 border-red-200 dark:border-red-800 rounded-xl shadow-lg" role="alert">
        <div class="flex items-center">
            <div class="p-2 rounded-lg bg-gradient-to-r from-red-500 to-pink-500 text-white mr-3">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div>
                <h4 class="font-bold text-red-800 dark:text-red-400">خطأ!</h4>
                <span class="text-red-700 dark:text-red-300">{{ session('error') }}</span>
            </div>
        </div>
    </div>
    @endif

    <!-- بطاقات الإحصائيات المبدعة -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- إجمالي الطلبات -->
        <div class="relative bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700 card-float stat-card overflow-hidden group">
            <div class="absolute inset-0 gradient-ocean opacity-5 group-hover:opacity-10 transition-opacity duration-300"></div>
            <div class="absolute top-0 left-0 right-0 h-1 gradient-ocean"></div>
            <div class="relative z-10 flex justify-between items-center">
                <div>
                    <h5 class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">إجمالي الطلبات</h5>
                    <p class="text-3xl font-bold text-gradient-ocean">{{ $orderStats['total'] }}</p>
                    <div class="w-12 h-1 gradient-ocean rounded-full mt-2"></div>
                </div>
                <div class="p-4 rounded-2xl bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 shadow-ocean group-hover:shadow-xl transition-all duration-300">
                    <i class="fas fa-shopping-cart text-blue-600 dark:text-blue-400 text-2xl icon-bounce"></i>
                </div>
            </div>
        </div>

        <!-- طلبات قيد الانتظار -->
        <div class="relative bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700 card-float stat-card overflow-hidden group">
            <div class="absolute inset-0 gradient-sunset opacity-5 group-hover:opacity-10 transition-opacity duration-300"></div>
            <div class="absolute top-0 left-0 right-0 h-1 gradient-sunset"></div>
            <div class="relative z-10 flex justify-between items-center">
                <div>
                    <h5 class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">قيد الانتظار</h5>
                    <p class="text-3xl font-bold text-gradient-luxury">{{ $orderStats['pending'] }}</p>
                    <div class="w-12 h-1 gradient-sunset rounded-full mt-2"></div>
                </div>
                <div class="p-4 rounded-2xl bg-gradient-to-br from-yellow-100 to-orange-100 dark:from-yellow-900/30 dark:to-orange-900/30 shadow-lg group-hover:shadow-xl transition-all duration-300">
                    <i class="fas fa-clock text-yellow-600 dark:text-yellow-400 text-2xl icon-rotate"></i>
                </div>
            </div>
        </div>

        <!-- طلبات قيد التحضير -->
        <div class="relative bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700 card-float stat-card overflow-hidden group">
            <div class="absolute inset-0 gradient-luxury opacity-5 group-hover:opacity-10 transition-opacity duration-300"></div>
            <div class="absolute top-0 left-0 right-0 h-1 gradient-luxury"></div>
            <div class="relative z-10 flex justify-between items-center">
                <div>
                    <h5 class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">قيد التحضير</h5>
                    <p class="text-3xl font-bold text-gradient-primary">{{ $orderStats['preparing'] }}</p>
                    <div class="w-12 h-1 gradient-luxury rounded-full mt-2"></div>
                </div>
                <div class="p-4 rounded-2xl bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/30 dark:to-pink-900/30 shadow-luxury group-hover:shadow-xl transition-all duration-300">
                    <i class="fas fa-utensils text-purple-600 dark:text-purple-400 text-2xl icon-pulse"></i>
                </div>
            </div>
        </div>

        <!-- طلبات مكتملة -->
        <div class="relative bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700 card-float stat-card overflow-hidden group">
            <div class="absolute inset-0 gradient-forest opacity-5 group-hover:opacity-10 transition-opacity duration-300"></div>
            <div class="absolute top-0 left-0 right-0 h-1 gradient-forest"></div>
            <div class="relative z-10 flex justify-between items-center">
                <div>
                    <h5 class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">مكتملة</h5>
                    <p class="text-3xl font-bold text-gradient-ocean">{{ $orderStats['completed'] }}</p>
                    <div class="w-12 h-1 gradient-forest rounded-full mt-2"></div>
                </div>
                <div class="p-4 rounded-2xl bg-gradient-to-br from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 shadow-secondary group-hover:shadow-xl transition-all duration-300">
                    <i class="fas fa-check-circle text-green-600 dark:text-green-400 text-2xl icon-bounce"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول الطلبات المبدع -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl overflow-hidden border border-gray-200 dark:border-gray-700">
        <!-- أزرار الفلترة المبدعة -->
        <div class="flex flex-wrap bg-gradient-to-r from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 border-b border-gray-200 dark:border-gray-700">
            <a href="{{ route('employee.orders') }}" class="group relative px-6 py-4 {{ !request('status') ? 'text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400' }} font-medium transition-all duration-300">
                <div class="flex items-center">
                    <div class="p-1 rounded-lg {{ !request('status') ? 'bg-blue-100 dark:bg-blue-900/30' : 'group-hover:bg-blue-50 dark:group-hover:bg-blue-900/20' }} transition-all duration-300">
                        <i class="fas fa-list-ul text-sm"></i>
                    </div>
                    <span class="mr-2">جميع الطلبات</span>
                </div>
                @if(!request('status'))
                    <div class="absolute bottom-0 left-0 right-0 h-1 bg-gradient-ocean"></div>
                @endif
            </a>

            <a href="{{ route('employee.orders', ['status' => 'pending']) }}" class="group relative px-6 py-4 {{ request('status') == 'pending' ? 'text-yellow-600 dark:text-yellow-400' : 'text-gray-600 dark:text-gray-300 hover:text-yellow-600 dark:hover:text-yellow-400' }} font-medium transition-all duration-300">
                <div class="flex items-center">
                    <div class="p-1 rounded-lg {{ request('status') == 'pending' ? 'bg-yellow-100 dark:bg-yellow-900/30' : 'group-hover:bg-yellow-50 dark:group-hover:bg-yellow-900/20' }} transition-all duration-300">
                        <i class="fas fa-clock text-sm"></i>
                    </div>
                    <span class="mr-2">قيد الانتظار</span>
                </div>
                @if(request('status') == 'pending')
                    <div class="absolute bottom-0 left-0 right-0 h-1 bg-gradient-sunset"></div>
                @endif
            </a>

            <a href="{{ route('employee.orders', ['status' => 'preparing']) }}" class="group relative px-6 py-4 {{ request('status') == 'preparing' ? 'text-purple-600 dark:text-purple-400' : 'text-gray-600 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400' }} font-medium transition-all duration-300">
                <div class="flex items-center">
                    <div class="p-1 rounded-lg {{ request('status') == 'preparing' ? 'bg-purple-100 dark:bg-purple-900/30' : 'group-hover:bg-purple-50 dark:group-hover:bg-purple-900/20' }} transition-all duration-300">
                        <i class="fas fa-utensils text-sm"></i>
                    </div>
                    <span class="mr-2">قيد التحضير</span>
                </div>
                @if(request('status') == 'preparing')
                    <div class="absolute bottom-0 left-0 right-0 h-1 bg-gradient-luxury"></div>
                @endif
            </a>

            <a href="{{ route('employee.orders', ['status' => 'completed']) }}" class="group relative px-6 py-4 {{ request('status') == 'completed' ? 'text-green-600 dark:text-green-400' : 'text-gray-600 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400' }} font-medium transition-all duration-300">
                <div class="flex items-center">
                    <div class="p-1 rounded-lg {{ request('status') == 'completed' ? 'bg-green-100 dark:bg-green-900/30' : 'group-hover:bg-green-50 dark:group-hover:bg-green-900/20' }} transition-all duration-300">
                        <i class="fas fa-check-circle text-sm"></i>
                    </div>
                    <span class="mr-2">مكتمل</span>
                </div>
                @if(request('status') == 'completed')
                    <div class="absolute bottom-0 left-0 right-0 h-1 bg-gradient-forest"></div>
                @endif
            </a>

            <a href="{{ route('employee.orders', ['status' => 'canceled']) }}" class="group relative px-6 py-4 {{ request('status') == 'canceled' ? 'text-red-600 dark:text-red-400' : 'text-gray-600 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400' }} font-medium transition-all duration-300">
                <div class="flex items-center">
                    <div class="p-1 rounded-lg {{ request('status') == 'canceled' ? 'bg-red-100 dark:bg-red-900/30' : 'group-hover:bg-red-50 dark:group-hover:bg-red-900/20' }} transition-all duration-300">
                        <i class="fas fa-times-circle text-sm"></i>
                    </div>
                    <span class="mr-2">ملغي</span>
                </div>
                @if(request('status') == 'canceled')
                    <div class="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-red-500 to-pink-500"></div>
                @endif
            </a>
        </div>

        <!-- محتوى الجدول -->
        <div class="p-6">
            <!-- هيدر الجدول مع الفلاتر -->
            <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h3 class="text-xl font-bold text-gradient-ocean mb-1">سجل الطلبات</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">إدارة ومتابعة جميع الطلبات</p>
                </div>

                <!-- فلاتر البحث المبدعة -->
                <div class="mt-4 md:mt-0">
                    <form action="{{ route('employee.orders') }}" method="GET" class="flex flex-wrap gap-3">
                        @if(request('status'))
                            <input type="hidden" name="status" value="{{ request('status') }}">
                        @endif
                        @if(request('search'))
                            <input type="hidden" name="search" value="{{ request('search') }}">
                        @endif

                        <!-- فلتر التاريخ -->
                        <div class="relative">
                            <input type="date" name="date" value="{{ request('date') }}" class="px-4 py-2 rounded-xl border-2 border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm transition-all duration-300 shadow-sm focus:shadow-lg">
                        </div>

                        <!-- فلتر طريقة الدفع -->
                        <div class="relative">
                            <select name="payment_method" class="px-4 py-2 rounded-xl border-2 border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm transition-all duration-300 shadow-sm focus:shadow-lg">
                                <option value="">جميع طرق الدفع</option>
                                <option value="cash" {{ request('payment_method') == 'cash' ? 'selected' : '' }}>نقدي</option>
                                <option value="credit_card" {{ request('payment_method') == 'credit_card' ? 'selected' : '' }}>بطاقة ائتمان</option>
                                <option value="wallet" {{ request('payment_method') == 'wallet' ? 'selected' : '' }}>محفظة رقمية</option>
                            </select>
                        </div>

                        <!-- زر الفلترة -->
                        <button type="submit" class="btn-magical px-4 py-2 bg-gradient-ocean text-white rounded-xl shadow-ocean hover:shadow-xl transition-all duration-300 group">
                            <i class="fas fa-filter group-hover:rotate-12 transition-transform duration-300"></i>
                        </button>
                    </form>
                </div>
            </div>

            <!-- الجدول المبدع -->
            <div class="overflow-x-auto rounded-xl border border-gray-200 dark:border-gray-700">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
                        <tr>
                            <th scope="col" class="px-6 py-4 text-right text-sm font-bold text-blue-800 dark:text-blue-300">
                                <div class="flex items-center">
                                    <i class="fas fa-hashtag mr-2 text-blue-600 dark:text-blue-400"></i>
                                    رقم الطلب
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-4 text-right text-sm font-bold text-blue-800 dark:text-blue-300">
                                <div class="flex items-center">
                                    <i class="fas fa-user mr-2 text-blue-600 dark:text-blue-400"></i>
                                    العميل
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-4 text-right text-sm font-bold text-blue-800 dark:text-blue-300">
                                <div class="flex items-center">
                                    <i class="fas fa-utensils mr-2 text-blue-600 dark:text-blue-400"></i>
                                    المنتجات
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-4 text-right text-sm font-bold text-blue-800 dark:text-blue-300">
                                <div class="flex items-center">
                                    <i class="fas fa-money-bill-wave mr-2 text-blue-600 dark:text-blue-400"></i>
                                    المبلغ
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-4 text-right text-sm font-bold text-blue-800 dark:text-blue-300">
                                <div class="flex items-center">
                                    <i class="fas fa-credit-card mr-2 text-blue-600 dark:text-blue-400"></i>
                                    طريقة الدفع
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-4 text-right text-sm font-bold text-blue-800 dark:text-blue-300">
                                <div class="flex items-center">
                                    <i class="fas fa-info-circle mr-2 text-blue-600 dark:text-blue-400"></i>
                                    الحالة
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-4 text-right text-sm font-bold text-blue-800 dark:text-blue-300">
                                <div class="flex items-center">
                                    <i class="fas fa-calendar mr-2 text-blue-600 dark:text-blue-400"></i>
                                    تاريخ الطلب
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-4 text-right text-sm font-bold text-blue-800 dark:text-blue-300">
                                <div class="flex items-center">
                                    <i class="fas fa-cogs mr-2 text-blue-600 dark:text-blue-400"></i>
                                    الإجراءات
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse($orders as $order)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                                <span class="bg-primary/10 text-primary px-2 py-1 rounded-md font-bold">#{{ $order->order_id }}</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center text-primary font-bold ml-2">
                                        {{ substr($order->user->first_name, 0, 1) }}
                                    </div>
                                    <span>{{ $order->user->first_name }} {{ $order->user->last_name }}</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                                @if($order->items->count() > 0)
                                    <div class="flex flex-col">
                                        @foreach($order->items->take(2) as $item)
                                            <span class="inline-flex items-center">
                                                <i class="fas fa-utensils text-xs text-primary ml-1"></i>
                                                {{ $item->menuItem->name ?? 'غير معروف' }}
                                                <span class="text-xs text-gray-500 dark:text-gray-400 mr-1">({{ $item->quantity }})</span>
                                            </span>
                                        @endforeach
                                        @if($order->items->count() > 2)
                                            <span class="text-xs text-primary mt-1">+ {{ $order->items->count() - 2 }} عناصر أخرى</span>
                                        @endif
                                    </div>
                                @else
                                    <span class="text-gray-400 dark:text-gray-500">لا توجد عناصر</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                                <span class="text-primary font-bold">{{ number_format($order->total_amount, 2) }}</span> <span class="text-xs text-gray-500 dark:text-gray-400">د.ل</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                                @if(isset($order->payments) && $order->payments->count() > 0)
                                    @if($order->payments->first()->payment_method == 'cash')
                                        <span class="inline-flex items-center">
                                            <i class="fas fa-money-bill-wave text-green-500 ml-1"></i>
                                            <span>نقدي</span>
                                        </span>
                                    @else
                                        <span class="inline-flex items-center">
                                            <i class="fas fa-credit-card text-blue-500 ml-1"></i>
                                            <span>بطاقة ائتمان</span>
                                        </span>
                                    @endif
                                @else
                                    <span class="inline-flex items-center text-gray-400 dark:text-gray-500">
                                        <i class="fas fa-clock ml-1"></i>
                                        <span>غير مدفوع</span>
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                @if($order->status == 'pending')
                                    <span class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">
                                        <i class="fas fa-clock ml-1"></i>
                                        <span>قيد الانتظار</span>
                                    </span>
                                @elseif($order->status == 'preparing')
                                    <span class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                                        <i class="fas fa-utensils ml-1"></i>
                                        <span>قيد التحضير</span>
                                    </span>
                                @elseif($order->status == 'completed')
                                    <span class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                        <i class="fas fa-check-circle ml-1"></i>
                                        <span>مكتمل</span>
                                    </span>
                                @elseif($order->status == 'canceled')
                                    <span class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-full bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                                        <i class="fas fa-times-circle ml-1"></i>
                                        <span>ملغي</span>
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                                {{ $order->created_at->format('Y-m-d H:i') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    @if($order->status == 'pending')
                                        <form action="{{ route('employee.orders.update-status', $order->order_id) }}" method="POST" class="inline">
                                            @csrf
                                            @method('PUT')
                                            <input type="hidden" name="status" value="preparing">
                                            <button type="submit" class="p-1 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-900/50">
                                                <i class="fas fa-utensils"></i>
                                            </button>
                                        </form>
                                    @elseif($order->status == 'preparing')
                                        <form action="{{ route('employee.orders.update-status', $order->order_id) }}" method="POST" class="inline">
                                            @csrf
                                            @method('PUT')
                                            <input type="hidden" name="status" value="completed">
                                            <button type="submit" class="p-1 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 hover:bg-green-200 dark:hover:bg-green-900/50">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </form>
                                    @endif
                                    <a href="{{ route('employee.orders.show', $order->order_id) }}" class="p-1 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-900/50">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    @if($order->status == 'pending' || $order->status == 'preparing')
                                    <a href="{{ route('employee.orders.edit', $order->order_id) }}" class="p-1 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 hover:bg-yellow-200 dark:hover:bg-yellow-900/50">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                                لا توجد طلبات متاحة
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-700 dark:text-gray-300">
                    @if($orders->total() > 0)
                        عرض <span class="font-medium">{{ $orders->firstItem() }}</span> إلى <span class="font-medium">{{ $orders->lastItem() }}</span> من <span class="font-medium">{{ $orders->total() }}</span> طلب
                    @else
                        لا توجد طلبات
                    @endif
                </div>
                <div class="flex items-center space-x-2 space-x-reverse">
                    {{ $orders->links() }}
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
