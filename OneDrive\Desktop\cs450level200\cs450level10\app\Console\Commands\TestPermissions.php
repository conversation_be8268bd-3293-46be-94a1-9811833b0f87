<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;

class TestPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permissions:test {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'اختبار صلاحيات مستخدم معين';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $this->error("المستخدم بالإيميل {$email} غير موجود");
            return 1;
        }

        $this->info("اختبار صلاحيات المستخدم: {$user->first_name} {$user->last_name}");
        $this->info("النوع: {$user->user_type}");
        $this->line("الإيميل: {$user->email}");
        $this->line("الحالة: " . ($user->is_active ? 'نشط' : 'غير نشط'));
        
        $this->newLine();
        
        // عرض الأدوار
        $roles = $user->getRoleNames();
        if ($roles->count() > 0) {
            $this->info("الأدوار:");
            foreach ($roles as $role) {
                $this->line("  - {$role}");
            }
        } else {
            $this->warn("لا توجد أدوار مُعيَّنة");
        }
        
        $this->newLine();
        
        // عرض الصلاحيات
        $permissions = $user->getAllPermissions();
        if ($permissions->count() > 0) {
            $this->info("الصلاحيات ({$permissions->count()}):");
            
            // تجميع الصلاحيات حسب النوع
            $groupedPermissions = $permissions->groupBy(function($permission) {
                return explode('.', $permission->name)[0];
            });
            
            foreach ($groupedPermissions as $group => $groupPermissions) {
                $groupName = $this->getGroupName($group);
                $this->line("  📁 {$groupName}:");
                
                foreach ($groupPermissions as $permission) {
                    $parts = explode('.', $permission->name);
                    $action = isset($parts[1]) ? $this->getActionName($parts[1]) : $parts[1] ?? '';
                    $this->line("    ✓ {$action}");
                }
                $this->newLine();
            }
        } else {
            $this->warn("لا توجد صلاحيات");
        }
        
        // اختبار صلاحيات محددة
        $this->newLine();
        $this->info("اختبار الوصول للأقسام:");
        
        $testPermissions = [
            'dashboard.admin' => 'لوحة تحكم المدير',
            'users.view' => 'عرض المستخدمين',
            'menu.view' => 'عرض القائمة',
            'orders.view' => 'عرض الطلبات',
            'reports.view' => 'عرض التقارير',
            'reports.sales' => 'تقارير المبيعات',
            'reports.financial' => 'التقارير المالية',
            'inventory.view' => 'عرض المخزون',
            'expenses.view' => 'عرض المصروفات'
        ];
        
        foreach ($testPermissions as $permission => $description) {
            $hasPermission = $user->can($permission);
            $status = $hasPermission ? '✅' : '❌';
            $this->line("  {$status} {$description}");
        }
        
        return 0;
    }
    
    private function getGroupName($group)
    {
        $names = [
            'users' => 'إدارة المستخدمين',
            'menu' => 'إدارة القائمة',
            'orders' => 'إدارة الطلبات',
            'reservations' => 'إدارة الحجوزات',
            'inventory' => 'إدارة المخزون',
            'ingredients' => 'إدارة المكونات',
            'expenses' => 'إدارة المصروفات',
            'reports' => 'التقارير',
            'tables' => 'إدارة الطاولات',
            'payments' => 'إدارة المدفوعات',
            'notifications' => 'إدارة الإشعارات',
            'settings' => 'الإعدادات',
            'dashboard' => 'لوحة التحكم'
        ];
        
        return $names[$group] ?? $group;
    }
    
    private function getActionName($action)
    {
        $names = [
            'view' => 'عرض',
            'create' => 'إضافة',
            'edit' => 'تعديل',
            'delete' => 'حذف',
            'status' => 'تغيير الحالة',
            'export' => 'تصدير',
            'import' => 'استيراد',
            'send' => 'إرسال',
            'permissions' => 'إدارة الصلاحيات',
            'admin' => 'المدير',
            'employee' => 'الموظف',
            'financial' => 'مالية',
            'sales' => 'مبيعات',
            'inventory' => 'مخزون',
            'details' => 'التفاصيل',
            'all' => 'الكل',
            'price' => 'السعر',
            'quantity' => 'الكمية',
            'availability' => 'التوفر',
            'items' => 'العناصر',
            'customer' => 'العميل',
            'cancel' => 'إلغاء',
            'payment' => 'الدفع',
            'print' => 'طباعة',
            'time' => 'الوقت',
            'table' => 'الطاولة',
            'confirm' => 'تأكيد',
            'transactions' => 'المعاملات',
            'transfer' => 'نقل',
            'adjust' => 'تسوية',
            'recipe' => 'الوصفة',
            'amount' => 'المبلغ',
            'category' => 'الفئة',
            'approve' => 'موافقة',
            'detailed' => 'مفصل',
            'profit' => 'ربح',
            'daily' => 'يومي',
            'monthly' => 'شهري',
            'low' => 'منخفض',
            'customers' => 'عملاء',
            'employees' => 'موظفين',
            'capacity' => 'السعة',
            'location' => 'الموقع',
            'reserve' => 'حجز',
            'free' => 'تحرير',
            'method' => 'الطريقة',
            'refund' => 'استرداد',
            'targeted' => 'مستهدف',
            'schedule' => 'جدولة',
            'system' => 'النظام',
            'security' => 'الأمان',
            'appearance' => 'المظهر',
            'backup' => 'نسخ احتياطي',
            'restore' => 'استعادة',
            'analytics' => 'تحليلات',
            'statistics' => 'إحصائيات'
        ];
        
        return $names[$action] ?? $action;
    }
}
