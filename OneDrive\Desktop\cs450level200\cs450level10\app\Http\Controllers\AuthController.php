<?php
namespace App\Http\Controllers;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Traits\HasRoles;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Validator;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Support\Str;
use Illuminate\Routing\Controller;
class AuthController extends Controller
{
    /**
     * عرض نموذج تسجيل الدخول
     */
    public function showLoginForm()
    {
        if (Auth::check()) {
            return $this->redirectToDashboard();
        }
        return view('auth.login');
    }

    /**
     * معالجة تسجيل الدخول
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // محاولة تسجيل الدخول
        $credentials = $request->only('email', 'password');
        $remember = $request->filled('remember');

        if (Auth::attempt($credentials, $remember)) {
            $request->session()->regenerate();

            $user = Auth::user();

            // التحقق من أن الحساب مفعل (فقط للموظفين والعملاء)
            if (!$user->is_active && $user->user_type !== 'admin') {
                Auth::logout();
                return redirect()->route('login')->with('error', 'حسابك غير مفعل');
            }

            // التحقق من وجود معلمة redirect في الرابط
            if ($request->has('redirect')) {
                $redirect = $request->query('redirect');

                // التحقق من نوع المستخدم وتوجيهه للصفحة المناسبة
                if ($redirect === 'reservations') {
                    return redirect()->route('customer.reservations.create');
                }
            }

            // توجيه المستخدم حسب نوعه إذا لم يكن هناك معلمة redirect
            if ($user->user_type === 'admin') {
                return redirect()->route('admin.dashboard');
            } elseif ($user->user_type === 'employee') {
                return redirect()->route('employee.dashboard');
            } else {
                return redirect()->route('customer.index');
            }
        }

        // في حالة فشل تسجيل الدخول
        return back()->with('error', 'بيانات الدخول غير صحيحة')->withInput($request->except('password'));
    }

    /**
     * عرض نموذج التسجيل
     */
    public function showRegistrationForm()
    {
        if (Auth::check()) {
            return $this->redirectToDashboard();
        }
        return view('auth.register');
    }

    /**
     * معالجة التسجيل
     */


    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:50',
            'last_name' => 'required|string|max:50',
            'email' => 'required|email|unique:users',
            'phone' => 'required|string|max:20',
            'password' => 'required|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $user = User::create([
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'email' => $request->email,
            'phone' => $request->phone,
            'password' => Hash::make($request->password),
            'user_type' => 'customer',
            'is_active' => true
        ]);

        // تم تعيين نوع المستخدم كعميل في الإنشاء

        Auth::login($user);

        // التحقق من وجود معلمة redirect في الرابط
        if ($request->has('redirect')) {
            $redirect = $request->query('redirect');

            // التحقق من نوع التوجيه
            if ($redirect === 'reservations') {
                return redirect()->route('customer.reservations.create')->with('success', 'تم التسجيل بنجاح');
            }
        }

        return redirect()->route('customer.dashboard')->with('success', 'تم التسجيل بنجاح');
    }


    /**
     * تسجيل الخروج
     */
    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        return redirect()->route('login')->with('success', 'تم تسجيل الخروج بنجاح');
    }

    /**
     * عرض نموذج نسيت كلمة السر
     */
    public function showForgotPasswordForm()
    {
        return view('auth.forgot-password');
    }

    /**
     * إرسال رابط إعادة تعيين كلمة السر
     */
    public function sendResetLinkEmail(Request $request)
    {
        $request->validate(['email' => 'required|email']);

        $status = Password::sendResetLink(
            $request->only('email')
        );

        return $status === Password::RESET_LINK_SENT
            ? back()->with('status', __($status))
            : back()->withErrors(['email' => __($status)]);
    }

    /**
     * عرض نموذج إعادة تعيين كلمة السر
     */
    public function showResetForm(Request $request, $token = null)
    {
        return view('auth.reset-password')->with(
            ['token' => $token, 'email' => $request->email]
        );
    }

    /**
     * معالجة إعادة تعيين كلمة السر
     */
    public function resetPassword(Request $request)
    {
        $request->validate([
            'token' => 'required',
            'email' => 'required|email',
            'password' => 'required|min:8|confirmed',
        ]);

        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function ($user, $password) {
                User::where('user_id', $user->user_id)->update([
                    'password' => Hash::make($password),
                    'remember_token' => Str::random(60),
                ]);

                event(new PasswordReset($user));
            }
        );

        return $status == Password::PASSWORD_RESET
            ? redirect()->route('login')->with('status', __($status))
            : back()->withErrors(['email' => [__($status)]]);
    }

    /**
     * عرض نموذج تغيير كلمة السر
     */
    public function showChangePasswordForm()
    {
        return view('auth.change-password');
    }

    /**
     * معالجة تغيير كلمة السر
     */
    public function changePassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'current_password' => 'required',
            'new_password' => 'required|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        if (!Hash::check($request->current_password, Auth::user()->password)) {
            return back()->with('error', 'كلمة السر الحالية غير صحيحة');
        }

        User::where('user_id', Auth::id())->update([
            'password' => Hash::make($request->new_password)
        ]);

        return back()->with('success', 'تم تغيير كلمة السر بنجاح');
    }

    /**
     * توجيه المستخدم حسب صلاحياته
     */
    protected function redirectToDashboard()
    {
        $user = Auth::user();

        if ($user->user_type === 'admin'){
            return redirect()->route('admin.dashboard');
        }

        if ($user->user_type === 'employee') {
            return redirect()->route('employee.dashboard');
        }

        return redirect()->route('customer.dashboard');
    }
}