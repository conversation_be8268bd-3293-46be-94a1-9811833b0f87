@extends('layouts.admin')

@section('title', 'عرض طلب الطاولة - نظام إدارة المطعم')

@section('page-title', 'عرض طلب الطاولة #' . $table->table_number)

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="mb-6 flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-800 dark:text-white">طلب الطاولة #{{ $table->table_number }}</h1>
            <p class="text-gray-600 dark:text-gray-400">{{ $table->location }} - سعة: {{ $table->capacity }} أشخاص</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('employee.tables') }}" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600">
                <i class="fas fa-arrow-right ml-1"></i>العودة للطاولات
            </a>
            <a href="{{ route('employee.orders.edit', $order->order_id) }}" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                <i class="fas fa-edit ml-1"></i>تعديل الطلب
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- معلومات الطلب -->
        <div class="md:col-span-2">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold text-gray-800 dark:text-white">تفاصيل الطلب</h2>
                    <span class="px-3 py-1 rounded-full text-sm font-medium
                        @if($order->status == 'pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-500
                        @elseif($order->status == 'preparing') bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-500
                        @elseif($order->status == 'ready') bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-500
                        @elseif($order->status == 'completed') bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-500
                        @elseif($order->status == 'cancelled') bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-500
                        @endif">
                        @if($order->status == 'pending') قيد الانتظار
                        @elseif($order->status == 'preparing') قيد التحضير
                        @elseif($order->status == 'ready') جاهز
                        @elseif($order->status == 'completed') مكتمل
                        @elseif($order->status == 'cancelled') ملغي
                        @endif
                    </span>
                </div>

                <div class="mb-4">
                    <div class="flex justify-between mb-2">
                        <span class="text-gray-600 dark:text-gray-400">رقم الطلب:</span>
                        <span class="font-medium text-gray-800 dark:text-white">#{{ $order->order_id }}</span>
                    </div>
                    <div class="flex justify-between mb-2">
                        <span class="text-gray-600 dark:text-gray-400">تاريخ الطلب:</span>
                        <span class="font-medium text-gray-800 dark:text-white">{{ $order->created_at->format('Y-m-d H:i') }}</span>
                    </div>
                    <div class="flex justify-between mb-2">
                        <span class="text-gray-600 dark:text-gray-400">العميل:</span>
                        <span class="font-medium text-gray-800 dark:text-white">
                            @if($order->user)
                                {{ $order->user->first_name }} {{ $order->user->last_name }}
                            @else
                                عميل غير مسجل
                            @endif
                        </span>
                    </div>
                    <div class="flex justify-between mb-2">
                        <span class="text-gray-600 dark:text-gray-400">نوع الطلب:</span>
                        <span class="font-medium text-gray-800 dark:text-white">{{ $order->order_type == 'dine_in' ? 'تناول في المطعم' : 'طلب خارجي' }}</span>
                    </div>
                    <div class="flex justify-between mb-2">
                        <span class="text-gray-600 dark:text-gray-400">طريقة الدفع:</span>
                        <span class="font-medium text-gray-800 dark:text-white">
                            @if($order->payment_method == 'cash') نقداً
                            @elseif($order->payment_method == 'card') بطاقة ائتمان
                            @elseif($order->payment_method == 'wallet') محفظة إلكترونية
                            @else {{ $order->payment_method }}
                            @endif
                        </span>
                    </div>
                    <div class="flex justify-between mb-2">
                        <span class="text-gray-600 dark:text-gray-400">حالة الدفع:</span>
                        <span class="font-medium {{ $order->payment_status == 'paid' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                            {{ $order->payment_status == 'paid' ? 'مدفوع' : 'غير مدفوع' }}
                        </span>
                    </div>
                </div>

                <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                    <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-3">العناصر المطلوبة</h3>

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead>
                                <tr>
                                    <th class="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">العنصر</th>
                                    <th class="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">السعر</th>
                                    <th class="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الكمية</th>
                                    <th class="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                @foreach($order->orderItems as $item)
                                <tr>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="flex items-center">
                                            @if($item->menuItem && $item->menuItem->image)
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <img class="h-10 w-10 rounded-md object-cover" src="{{ asset('storage/' . $item->menuItem->image) }}" alt="{{ $item->menuItem->name }}">
                                            </div>
                                            @endif
                                            <div class="mr-3">
                                                <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                    {{ $item->menuItem ? $item->menuItem->name : $item->item_name }}
                                                </div>
                                                @if($item->notes)
                                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                                    {{ $item->notes }}
                                                </div>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        {{ number_format($item->unit_price, 2) }} د.ل
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        {{ $item->quantity }}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                        {{ number_format($item->unit_price * $item->quantity, 2) }} د.ل
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4 border-t border-gray-200 dark:border-gray-700 pt-4">
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600 dark:text-gray-400">المجموع الفرعي:</span>
                            <span class="font-medium text-gray-800 dark:text-white">{{ number_format($order->subtotal, 2) }} د.ل</span>
                        </div>
                        @if($order->tax_amount > 0)
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600 dark:text-gray-400">الضريبة ({{ $order->tax_rate }}%):</span>
                            <span class="font-medium text-gray-800 dark:text-white">{{ number_format($order->tax_amount, 2) }} د.ل</span>
                        </div>
                        @endif
                        @if($order->discount_amount > 0)
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600 dark:text-gray-400">الخصم:</span>
                            <span class="font-medium text-green-600 dark:text-green-400">- {{ number_format($order->discount_amount, 2) }} د.ل</span>
                        </div>
                        @endif
                        <div class="flex justify-between font-bold text-lg mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                            <span class="text-gray-800 dark:text-white">الإجمالي:</span>
                            <span class="text-primary">{{ number_format($order->total_amount, 2) }} د.ل</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإجراءات والملاحظات -->
        <div class="md:col-span-1">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-bold text-gray-800 dark:text-white mb-4">الإجراءات</h2>

                <div class="space-y-3">
                    <form action="{{ route('employee.orders.update-status', $order->order_id) }}" method="POST" class="mb-3">
                        @csrf
                        @method('PUT')
                        <div class="mb-3">
                            <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">تغيير حالة الطلب</label>
                            <select id="status" name="status" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white">
                                <option value="pending" {{ $order->status == 'pending' ? 'selected' : '' }}>قيد الانتظار</option>
                                <option value="preparing" {{ $order->status == 'preparing' ? 'selected' : '' }}>قيد التحضير</option>
                                <option value="ready" {{ $order->status == 'ready' ? 'selected' : '' }}>جاهز</option>
                                <option value="completed" {{ $order->status == 'completed' ? 'selected' : '' }}>مكتمل</option>
                                <option value="cancelled" {{ $order->status == 'cancelled' ? 'selected' : '' }}>ملغي</option>
                            </select>
                        </div>
                        <button type="submit" class="w-full px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50">
                            تحديث الحالة
                        </button>
                    </form>

                    <a href="{{ route('employee.orders.edit', $order->order_id) }}" class="block w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-center">
                        <i class="fas fa-edit ml-1"></i>تعديل الطلب
                    </a>

                    @if($order->payment_status != 'paid')
                    <a href="{{ route('employee.orders.show', $order->order_id) }}?payment=true" class="block w-full px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 text-center">
                        <i class="fas fa-money-bill-wave ml-1"></i>تسجيل الدفع
                    </a>
                    @endif

                    <a href="{{ route('employee.tables.free') }}?table_id={{ $table->table_id }}" class="block w-full px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 text-center">
                        <i class="fas fa-check-circle ml-1"></i>تحرير الطاولة
                    </a>

                    <button id="printReceiptBtn" class="w-full px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50">
                        <i class="fas fa-print ml-1"></i>طباعة الإيصال
                    </button>
                </div>
            </div>

            @if($order->notes)
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 class="text-xl font-bold text-gray-800 dark:text-white mb-4">ملاحظات الطلب</h2>
                <p class="text-gray-700 dark:text-gray-300">{{ $order->notes }}</p>
            </div>
            @endif
        </div>
    </div>
</div>

<!-- سكريبت طباعة الإيصال -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const printReceiptBtn = document.getElementById('printReceiptBtn');

        if (printReceiptBtn) {
            printReceiptBtn.addEventListener('click', function() {
                // هنا يمكن إضافة منطق طباعة الإيصال
                // يمكن استخدام نافذة جديدة للطباعة أو استخدام API للطباعة

                const printWindow = window.open('', '_blank');

                printWindow.document.write(`
                    <html dir="rtl">
                    <head>
                        <title>إيصال الطلب #{{ $order->order_id }}</title>
                        <style>
                            body {
                                font-family: 'Arial', sans-serif;
                                margin: 0;
                                padding: 20px;
                                direction: rtl;
                            }
                            .receipt {
                                max-width: 300px;
                                margin: 0 auto;
                                border: 1px solid #ddd;
                                padding: 15px;
                            }
                            .header {
                                text-align: center;
                                margin-bottom: 20px;
                                border-bottom: 1px dashed #ddd;
                                padding-bottom: 10px;
                            }
                            .info {
                                margin-bottom: 15px;
                            }
                            .info div {
                                display: flex;
                                justify-content: space-between;
                                margin-bottom: 5px;
                            }
                            .items {
                                margin-bottom: 15px;
                                border-bottom: 1px dashed #ddd;
                                padding-bottom: 10px;
                            }
                            .item {
                                display: flex;
                                justify-content: space-between;
                                margin-bottom: 5px;
                            }
                            .total {
                                font-weight: bold;
                                display: flex;
                                justify-content: space-between;
                                margin-top: 10px;
                                border-top: 1px dashed #ddd;
                                padding-top: 10px;
                            }
                            .footer {
                                text-align: center;
                                margin-top: 20px;
                                font-size: 12px;
                            }
                            @media print {
                                body {
                                    padding: 0;
                                }
                                .receipt {
                                    border: none;
                                }
                            }
                        </style>
                    </head>
                    <body>
                        <div class="receipt">
                            <div class="header">
                                <h2>Eat Hub</h2>
                                <p>إيصال الطلب</p>
                            </div>

                            <div class="info">
                                <div>
                                    <span>رقم الطلب:</span>
                                    <span>#{{ $order->order_id }}</span>
                                </div>
                                <div>
                                    <span>التاريخ:</span>
                                    <span>{{ $order->created_at->format('Y-m-d H:i') }}</span>
                                </div>
                                <div>
                                    <span>الطاولة:</span>
                                    <span>#{{ $table->table_number }}</span>
                                </div>
                                <div>
                                    <span>العميل:</span>
                                    <span>
                                        @if($order->user)
                                            {{ $order->user->first_name }} {{ $order->user->last_name }}
                                        @else
                                            عميل غير مسجل
                                        @endif
                                    </span>
                                </div>
                            </div>

                            <div class="items">
                                <h3>العناصر</h3>
                                @foreach($order->orderItems as $item)
                                <div class="item">
                                    <span>{{ $item->quantity }}x {{ $item->menuItem ? $item->menuItem->name : $item->item_name }}</span>
                                    <span>{{ number_format($item->unit_price * $item->quantity, 2) }} د.ل</span>
                                </div>
                                @endforeach
                            </div>

                            <div>
                                <div class="item">
                                    <span>المجموع الفرعي:</span>
                                    <span>{{ number_format($order->subtotal, 2) }} د.ل</span>
                                </div>
                                @if($order->tax_amount > 0)
                                <div class="item">
                                    <span>الضريبة ({{ $order->tax_rate }}%):</span>
                                    <span>{{ number_format($order->tax_amount, 2) }} د.ل</span>
                                </div>
                                @endif
                                @if($order->discount_amount > 0)
                                <div class="item">
                                    <span>الخصم:</span>
                                    <span>- {{ number_format($order->discount_amount, 2) }} د.ل</span>
                                </div>
                                @endif

                                <div class="total">
                                    <span>الإجمالي:</span>
                                    <span>{{ number_format($order->total_amount, 2) }} د.ل</span>
                                </div>
                            </div>

                            <div class="footer">
                                <p>شكراً لزيارتكم!</p>
                                <p>Eat Hub - نظام إدارة المطعم</p>
                            </div>
                        </div>

                        <script>
                            window.onload = function() {
                                window.print();
                                setTimeout(function() {
                                    window.close();
                                }, 500);
                            };
                        </script>
                    </body>
                    </html>
                `);

                printWindow.document.close();
            });
        }
    });
</script>
@endsection
