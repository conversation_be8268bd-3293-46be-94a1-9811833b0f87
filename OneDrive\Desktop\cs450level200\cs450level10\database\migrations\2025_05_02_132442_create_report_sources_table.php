<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('report_sources', function (Blueprint $table) {
            $table->id('source_id');
            $table->foreignId('report_id')->constrained('financial_reports')->onDelete('cascade');
            $table->enum('source_type', ['order', 'payment', 'expense', 'invoice']);
            $table->integer('source_ref_id');
            $table->decimal('amount', 15, 2);
        });
    }

    public function down()
    {
        Schema::dropIfExists('report_sources');
    }
};