<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;

class UpdatePasswordHash extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:update-password-hash';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update password_hash field for all users';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $users = User::all();
        $count = 0;

        foreach ($users as $user) {
            if (!empty($user->password) && empty($user->password_hash)) {
                $user->password_hash = $user->password;
                $user->save();
                $count++;
            }
        }

        $this->info("Updated password_hash for {$count} users.");
    }
}
