@extends('layouts.admin')

@section('title', 'إدارة المصروفات - لوحة تحكم Eat Hub')

@section('page-title', 'إدارة المصروفات')

@section('content')
<div class="space-y-4">
    <!-- العنوان الرئيسي -->
    <div class="flex items-center justify-between bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 rounded-lg p-6 shadow-lg border border-red-100 dark:border-red-800">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white flex items-center">
                <i class="fas fa-money-bill-wave text-red-500 ml-3"></i>
                إدارة المصروفات
            </h1>
            <p class="text-gray-600 dark:text-gray-400 mt-2">تتبع وإدارة مصروفات المطعم وصرف المخزون التلقائي</p>
            <div class="flex items-center mt-2 text-sm text-gray-500 dark:text-gray-400">
                <i class="fas fa-info-circle ml-2"></i>
                <span>يتم تسجيل مصروفات المخزون تلقائياً عند استخدام المكونات</span>
            </div>
        </div>
        <div class="flex flex-col space-y-2">
            <a href="{{ route('admin.expenses.create') }}" class="bg-red-500 hover:bg-red-600 text-white font-bold py-3 px-6 rounded-lg flex items-center transition-all shadow-md">
                <i class="fas fa-plus ml-2"></i>
                <span>إضافة مصروف يدوي</span>
            </a>
            <a href="{{ route('admin.inventory') }}" class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-6 rounded-lg flex items-center transition-all text-sm">
                <i class="fas fa-warehouse ml-2"></i>
                <span>إدارة المخزون</span>
            </a>
        </div>
    </div>

    <!-- شريط البحث والفلاتر المحسن -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-md">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4 lg:space-x-reverse">
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 sm:space-x-reverse flex-1">
                <div class="relative flex-1">
                    <input type="text" id="searchInput" placeholder="البحث في الوصف، الفئة، أو المبلغ..."
                           class="w-full px-4 py-3 pr-12 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 text-base">
                    <button class="absolute left-3 top-3.5 text-gray-500 dark:text-gray-400">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                <div class="flex space-x-2 space-x-reverse">
                    <select id="categoryFilter" class="bg-white dark:bg-gray-700 text-gray-800 dark:text-white border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-red-500">
                        <option value="">جميع الفئات</option>
                        <option value="inventory_auto">صرف المخزون (تلقائي)</option>
                        <option value="ingredients">المكونات (يدوي)</option>
                        <option value="utilities">المرافق</option>
                        <option value="salaries">الرواتب</option>
                        <option value="maintenance">الصيانة</option>
                        <option value="other">أخرى</option>
                    </select>
                    <input type="date" id="dateFilter" class="bg-white dark:bg-gray-700 text-gray-800 dark:text-white border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-red-500">
                </div>
            </div>
            <div class="flex space-x-2 space-x-reverse">
                <button onclick="exportExpenses()" class="bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-4 rounded-lg flex items-center transition-all">
                    <i class="fas fa-download ml-2"></i>
                    <span>تصدير</span>
                </button>
                <a href="{{ route('admin.expenses.create') }}" class="bg-red-500 hover:bg-red-600 text-white font-bold py-3 px-4 rounded-lg flex items-center transition-all">
                    <i class="fas fa-plus ml-2"></i>
                    <span>إضافة مصروف</span>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- بطاقات الإحصائيات المحسنة -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
    <!-- إجمالي المصروفات -->
    <div class="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 rounded-xl shadow-lg p-6 border border-red-200 dark:border-red-700">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-red-600 dark:text-red-400 text-sm font-medium mb-2">إجمالي المصروفات</p>
                <h3 class="text-3xl font-bold text-red-800 dark:text-red-200">{{ number_format($currentMonthTotal ?? 0, 2) }}</h3>
                <p class="text-red-500 dark:text-red-400 text-xs mt-1">دينار ليبي - الشهر الحالي</p>
            </div>
            <div class="rounded-full bg-red-200 dark:bg-red-800/50 p-3">
                <i class="fas fa-money-bill-wave text-red-600 dark:text-red-300 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- مصروفات المخزون التلقائية -->
    <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-xl shadow-lg p-6 border border-blue-200 dark:border-blue-700">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-blue-600 dark:text-blue-400 text-sm font-medium mb-2">صرف المخزون التلقائي</p>
                <h3 class="text-3xl font-bold text-blue-800 dark:text-blue-200">{{ number_format($inventoryExpenses ?? 0, 2) }}</h3>
                <p class="text-blue-500 dark:text-blue-400 text-xs mt-1">دينار ليبي - هذا الشهر</p>
            </div>
            <div class="rounded-full bg-blue-200 dark:bg-blue-800/50 p-3">
                <i class="fas fa-warehouse text-blue-600 dark:text-blue-300 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- المصروفات اليدوية -->
    <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-xl shadow-lg p-6 border border-green-200 dark:border-green-700">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-green-600 dark:text-green-400 text-sm font-medium mb-2">المصروفات اليدوية</p>
                <h3 class="text-3xl font-bold text-green-800 dark:text-green-200">{{ number_format($manualExpenses ?? 0, 2) }}</h3>
                <p class="text-green-500 dark:text-green-400 text-xs mt-1">دينار ليبي - هذا الشهر</p>
            </div>
            <div class="rounded-full bg-green-200 dark:bg-green-800/50 p-3">
                <i class="fas fa-hand-holding-usd text-green-600 dark:text-green-300 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- متوسط المصروفات اليومية -->
    <div class="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-xl shadow-lg p-6 border border-purple-200 dark:border-purple-700">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-purple-600 dark:text-purple-400 text-sm font-medium mb-2">متوسط يومي</p>
                <h3 class="text-3xl font-bold text-purple-800 dark:text-purple-200">{{ number_format($dailyAverage ?? 0, 2) }}</h3>
                <p class="text-purple-500 dark:text-purple-400 text-xs mt-1">دينار ليبي - يومياً</p>
            </div>
            <div class="rounded-full bg-purple-200 dark:bg-purple-800/50 p-3">
                <i class="fas fa-chart-line text-purple-600 dark:text-purple-300 text-xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- تفاصيل المصروفات حسب الفئة -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-4 mb-4 border border-gray-200 dark:border-gray-700">
    <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-bold text-gray-800 dark:text-white flex items-center">
            <i class="fas fa-chart-pie text-blue-500 ml-2"></i>
            توزيع المصروفات حسب الفئة
        </h3>
        <div class="text-sm text-gray-500 dark:text-gray-400">
            الشهر الحالي: {{ now()->format('F Y') }}
        </div>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        @php
            $categoryData = [
                'inventory_auto' => ['name' => 'صرف المخزون (تلقائي)', 'color' => 'blue', 'icon' => 'warehouse'],
                'ingredients' => ['name' => 'المكونات (يدوي)', 'color' => 'green', 'icon' => 'leaf'],
                'utilities' => ['name' => 'المرافق', 'color' => 'yellow', 'icon' => 'bolt'],
                'salaries' => ['name' => 'الرواتب', 'color' => 'indigo', 'icon' => 'users'],
                'maintenance' => ['name' => 'الصيانة', 'color' => 'red', 'icon' => 'tools'],
                'other' => ['name' => 'أخرى', 'color' => 'gray', 'icon' => 'ellipsis-h']
            ];
        @endphp

        @forelse($categoryTotals ?? [] as $category)
            @php
                $percentage = $currentMonthTotal > 0 ? ($category->total / $currentMonthTotal) * 100 : 0;
                $categoryInfo = $categoryData[$category->category] ?? $categoryData['other'];
                $color = $categoryInfo['color'];
            @endphp
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                <div class="flex items-center justify-between mb-3">
                    <div class="rounded-full bg-{{ $color }}-100 dark:bg-{{ $color }}-900/30 p-2">
                        <i class="fas fa-{{ $categoryInfo['icon'] }} text-{{ $color }}-600 dark:text-{{ $color }}-400 text-sm"></i>
                    </div>
                    <span class="text-xs font-medium text-{{ $color }}-600 dark:text-{{ $color }}-400">{{ number_format($percentage, 1) }}%</span>
                </div>
                <h4 class="text-sm font-medium text-gray-800 dark:text-white mb-1">{{ $categoryInfo['name'] }}</h4>
                <p class="text-lg font-bold text-gray-900 dark:text-white">{{ number_format($category->total, 2) }}</p>
                <div class="mt-2 w-full h-1.5 bg-gray-200 dark:bg-gray-600 rounded-full overflow-hidden">
                    <div class="bg-{{ $color }}-500 h-full rounded-full transition-all duration-500" style="width: {{ $percentage }}%;"></div>
                </div>
            </div>
        @empty
            <div class="col-span-full text-center py-8">
                <i class="fas fa-chart-pie text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-500 dark:text-gray-400">لا توجد بيانات مصروفات متاحة</p>
                <p class="text-sm text-gray-400 dark:text-gray-500 mt-1">ابدأ بإضافة مصروفات أو استخدام المخزون</p>
            </div>
        @endforelse
    </div>
</div>

<!-- جدول المصروفات المحسن -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden border border-gray-200 dark:border-gray-700">
    <div class="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div>
                <h3 class="text-xl font-bold text-gray-800 dark:text-white flex items-center">
                    <i class="fas fa-list-alt text-red-500 ml-2"></i>
                    سجل المصروفات التفصيلي
                </h3>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    يشمل المصروفات اليدوية وصرف المخزون التلقائي
                </p>
            </div>
            <div class="flex flex-wrap gap-2">
                <div class="flex items-center space-x-2 space-x-reverse">
                    <span class="w-3 h-3 bg-blue-500 rounded-full"></span>
                    <span class="text-xs text-gray-600 dark:text-gray-400">تلقائي</span>
                </div>
                <div class="flex items-center space-x-2 space-x-reverse">
                    <span class="w-3 h-3 bg-green-500 rounded-full"></span>
                    <span class="text-xs text-gray-600 dark:text-gray-400">يدوي</span>
                </div>
            </div>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead>
                <tr class="bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800">
                    <th scope="col" class="px-6 py-4 text-right text-xs font-bold text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                        <i class="fas fa-file-alt ml-1"></i>
                        الوصف
                    </th>
                    <th scope="col" class="px-6 py-4 text-right text-xs font-bold text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                        <i class="fas fa-tag ml-1"></i>
                        النوع
                    </th>
                    <th scope="col" class="px-6 py-4 text-right text-xs font-bold text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                        <i class="fas fa-layer-group ml-1"></i>
                        الفئة
                    </th>
                    <th scope="col" class="px-6 py-4 text-right text-xs font-bold text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                        <i class="fas fa-coins ml-1"></i>
                        المبلغ
                    </th>
                    <th scope="col" class="px-6 py-4 text-right text-xs font-bold text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                        <i class="fas fa-credit-card ml-1"></i>
                        طريقة الدفع
                    </th>
                    <th scope="col" class="px-6 py-4 text-right text-xs font-bold text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                        <i class="fas fa-calendar ml-1"></i>
                        التاريخ
                    </th>
                    <th scope="col" class="px-6 py-4 text-right text-xs font-bold text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                        <i class="fas fa-user ml-1"></i>
                        المسجل
                    </th>
                    <th scope="col" class="px-6 py-4 text-right text-xs font-bold text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                        <i class="fas fa-cogs ml-1"></i>
                        الإجراءات
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                @forelse($expenses ?? [] as $expense)
                @php
                    $isAutomatic = isset($expense->is_automatic) && $expense->is_automatic;
                    $categoryData = [
                        'inventory_auto' => ['name' => 'صرف المخزون (تلقائي)', 'color' => 'blue', 'icon' => 'warehouse'],
                        'ingredients' => ['name' => 'المكونات (يدوي)', 'color' => 'green', 'icon' => 'leaf'],
                        'utilities' => ['name' => 'المرافق', 'color' => 'yellow', 'icon' => 'bolt'],
                        'salaries' => ['name' => 'الرواتب', 'color' => 'indigo', 'icon' => 'users'],
                        'maintenance' => ['name' => 'الصيانة', 'color' => 'red', 'icon' => 'tools'],
                        'other' => ['name' => 'أخرى', 'color' => 'gray', 'icon' => 'ellipsis-h']
                    ];
                    $categoryInfo = $categoryData[$expense->category] ?? $categoryData['other'];
                @endphp
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors duration-200">
                    <!-- الوصف -->
                    <td class="px-6 py-4 text-sm">
                        <div class="flex items-start">
                            <div class="flex-1">
                                <p class="font-medium text-gray-900 dark:text-white">{{ $expense->description }}</p>
                                @if($isAutomatic)
                                <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">
                                    <i class="fas fa-robot ml-1"></i>
                                    تم إنشاؤه تلقائياً من استخدام المخزون
                                </p>
                                @endif
                                @if(isset($expense->order_id))
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                    <i class="fas fa-receipt ml-1"></i>
                                    مرتبط بالطلب #{{ $expense->order_id }}
                                </p>
                                @endif
                            </div>
                        </div>
                    </td>

                    <!-- النوع -->
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        @if($isAutomatic)
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                            <i class="fas fa-cog ml-1"></i>
                            تلقائي
                        </span>
                        @else
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                            <i class="fas fa-hand-paper ml-1"></i>
                            يدوي
                        </span>
                        @endif
                    </td>

                    <!-- الفئة -->
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-{{ $categoryInfo['color'] }}-100 dark:bg-{{ $categoryInfo['color'] }}-900/30 text-{{ $categoryInfo['color'] }}-800 dark:text-{{ $categoryInfo['color'] }}-300">
                            <i class="fas fa-{{ $categoryInfo['icon'] }} ml-1"></i>
                            {{ $categoryInfo['name'] }}
                        </span>
                    </td>

                    <!-- المبلغ -->
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <div class="flex items-center">
                            <span class="text-lg font-bold text-gray-900 dark:text-white">{{ number_format($expense->amount, 2) }}</span>
                            <span class="text-xs text-gray-500 dark:text-gray-400 mr-1">د.ل</span>
                        </div>
                    </td>

                    <!-- طريقة الدفع -->
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                        @if($isAutomatic)
                        <span class="text-blue-600 dark:text-blue-400">
                            <i class="fas fa-warehouse ml-1"></i>
                            صرف مخزون
                        </span>
                        @else
                        <span>{{ $expense->payment_method ?? 'غير محدد' }}</span>
                        @endif
                    </td>

                    <!-- التاريخ -->
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                        <div class="flex flex-col">
                            <span>{{ $expense->expense_date ? $expense->expense_date->format('d/m/Y') : 'غير محدد' }}</span>
                            <span class="text-xs text-gray-400">{{ $expense->expense_date ? $expense->expense_date->format('H:i') : '' }}</span>
                        </div>
                    </td>

                    <!-- المسجل -->
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                        @if($isAutomatic)
                        <span class="text-blue-600 dark:text-blue-400">
                            <i class="fas fa-robot ml-1"></i>
                            النظام
                        </span>
                        @else
                        <span>{{ $expense->recorder ? $expense->recorder->first_name . ' ' . $expense->recorder->last_name : 'غير معروف' }}</span>
                        @endif
                    </td>

                    <!-- الإجراءات -->
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                        <div class="flex items-center justify-end space-x-2 space-x-reverse">
                            @if(!$isAutomatic)
                            <a href="{{ route('admin.expenses.edit', $expense->expense_id) }}"
                               class="text-blue-500 hover:text-blue-700 transition-colors p-1 rounded hover:bg-blue-50 dark:hover:bg-blue-900/20"
                               title="تعديل">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button onclick="deleteExpense('{{ $expense->expense_id }}')"
                                    class="text-red-500 hover:text-red-700 transition-colors p-1 rounded hover:bg-red-50 dark:hover:bg-red-900/20"
                                    title="حذف">
                                <i class="fas fa-trash"></i>
                            @else
                            <span class="text-gray-400 text-xs">
                                <i class="fas fa-lock ml-1"></i>
                                تلقائي
                            </span>
                            @endif
                            <button onclick="viewExpenseDetails('{{ $expense->expense_id }}')"
                                    class="text-gray-500 hover:text-gray-700 transition-colors p-1 rounded hover:bg-gray-50 dark:hover:bg-gray-900/20"
                                    title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="8" class="px-6 py-12 text-center">
                        <div class="flex flex-col items-center">
                            <i class="fas fa-receipt text-gray-400 text-4xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">لا توجد مصروفات مسجلة</h3>
                            <p class="text-gray-500 dark:text-gray-400 mb-4">ابدأ بإضافة مصروفات أو استخدام المخزون لرؤية البيانات هنا</p>
                            <a href="{{ route('admin.expenses.create') }}"
                               class="inline-flex items-center px-4 py-2 bg-red-500 hover:bg-red-600 text-white text-sm font-medium rounded-lg transition-colors">
                                <i class="fas fa-plus ml-2"></i>
                                إضافة أول مصروف
                            </a>
                        </div>
                    </td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <!-- تذييل الجدول مع التصفح -->
    <div class="bg-gray-50 dark:bg-gray-700/50 px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div class="text-sm text-gray-700 dark:text-gray-300 mb-4 sm:mb-0">
                @if(isset($expenses) && $expenses->count() > 0)
                عرض {{ $expenses->firstItem() }} إلى {{ $expenses->lastItem() }} من أصل {{ $expenses->total() }} مصروف
                @endif
            </div>
            @if(isset($expenses))
            {{ $expenses->links('pagination.tailwind') }}
            @endif
        </div>
    </div>
</div>

<!-- نموذج تأكيد الحذف -->
<div id="deleteModal" class="fixed inset-0 bg-black/50 z-50 hidden flex items-center justify-center">
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md mx-auto">
        <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-4">تأكيد الحذف</h3>
        <p class="text-gray-600 dark:text-gray-300 mb-6">هل أنت متأكد من رغبتك في حذف هذا المصروف؟ هذا الإجراء لا يمكن التراجع عنه.</p>
        <div class="flex justify-end space-x-2 space-x-reverse">
            <button id="cancelDelete" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600">إلغاء</button>
            <form id="deleteForm" method="POST" action="">
                @csrf
                @method('DELETE')
                <button type="submit" class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600">حذف</button>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // وظائف إدارة المصروفات المحسنة
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة البحث والفلاتر
        initializeFilters();

        // تحديث الإحصائيات كل 30 ثانية
        setInterval(updateStats, 30000);
    });

    // تهيئة البحث والفلاتر
    function initializeFilters() {
        const searchInput = document.getElementById('searchInput');
        const categoryFilter = document.getElementById('categoryFilter');
        const dateFilter = document.getElementById('dateFilter');

        // البحث المباشر
        if (searchInput) {
            searchInput.addEventListener('input', debounce(function() {
                filterExpenses();
            }, 300));
        }

        // فلتر الفئة
        if (categoryFilter) {
            categoryFilter.addEventListener('change', filterExpenses);
        }

        // فلتر التاريخ
        if (dateFilter) {
            dateFilter.addEventListener('change', filterExpenses);
        }
    }

    // تطبيق الفلاتر
    function filterExpenses() {
        const searchTerm = document.getElementById('searchInput')?.value.toLowerCase() || '';
        const selectedCategory = document.getElementById('categoryFilter')?.value || '';
        const selectedDate = document.getElementById('dateFilter')?.value || '';

        const rows = document.querySelectorAll('tbody tr:not(.empty-state)');
        let visibleCount = 0;

        rows.forEach(row => {
            const description = row.querySelector('td:first-child')?.textContent.toLowerCase() || '';
            const category = row.querySelector('td:nth-child(3) span')?.textContent.toLowerCase() || '';
            const date = row.querySelector('td:nth-child(6)')?.textContent || '';

            let shouldShow = true;

            // فلتر البحث
            if (searchTerm && !description.includes(searchTerm)) {
                shouldShow = false;
            }

            // فلتر الفئة
            if (selectedCategory && !category.includes(selectedCategory)) {
                shouldShow = false;
            }

            // فلتر التاريخ
            if (selectedDate && !date.includes(selectedDate)) {
                shouldShow = false;
            }

            if (shouldShow) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        // إظهار رسالة عدم وجود نتائج
        updateEmptyState(visibleCount === 0);
    }

    // تحديث حالة عدم وجود نتائج
    function updateEmptyState(isEmpty) {
        const emptyState = document.querySelector('.empty-state');
        if (isEmpty && !emptyState) {
            const tbody = document.querySelector('tbody');
            const emptyRow = document.createElement('tr');
            emptyRow.className = 'empty-state';
            emptyRow.innerHTML = `
                <td colspan="8" class="px-6 py-12 text-center">
                    <div class="flex flex-col items-center">
                        <i class="fas fa-search text-gray-400 text-4xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">لا توجد نتائج</h3>
                        <p class="text-gray-500 dark:text-gray-400">جرب تغيير معايير البحث أو الفلاتر</p>
                    </div>
                </td>
            `;
            tbody.appendChild(emptyRow);
        } else if (!isEmpty && emptyState) {
            emptyState.remove();
        }
    }

    // حذف المصروف
    function deleteExpense(id) {
        const deleteModal = document.getElementById('deleteModal');
        const deleteForm = document.getElementById('deleteForm');
        const cancelDelete = document.getElementById('cancelDelete');

        deleteForm.action = "/admin/expenses/" + id;
        deleteModal.classList.remove('hidden');

        cancelDelete.addEventListener('click', function() {
            deleteModal.classList.add('hidden');
        });
    }

    // عرض تفاصيل المصروف
    function viewExpenseDetails(id) {
        // إعادة توجيه لصفحة التفاصيل
        window.location.href = "/admin/expenses/" + id;
    }

    // تصدير المصروفات
    function exportExpenses() {
        const searchParams = new URLSearchParams();

        const searchTerm = document.getElementById('searchInput')?.value;
        const category = document.getElementById('categoryFilter')?.value;
        const date = document.getElementById('dateFilter')?.value;

        if (searchTerm) searchParams.append('search', searchTerm);
        if (category) searchParams.append('category', category);
        if (date) searchParams.append('date', date);

        window.location.href = "/admin/expenses/export?" + searchParams.toString();
    }

    // تحديث الإحصائيات
    function updateStats() {
        fetch("/admin/expenses/stats")
            .then(response => response.json())
            .then(data => {
                // تحديث البطاقات الإحصائية
                updateStatCard('currentMonthTotal', data.currentMonthTotal);
                updateStatCard('inventoryExpenses', data.inventoryExpenses);
                updateStatCard('manualExpenses', data.manualExpenses);
                updateStatCard('dailyAverage', data.dailyAverage);
            })
            .catch(error => console.log('خطأ في تحديث الإحصائيات:', error));
    }

    // تحديث بطاقة إحصائية
    function updateStatCard(cardId, value) {
        const card = document.querySelector(`[data-stat="${cardId}"]`);
        if (card) {
            card.textContent = new Intl.NumberFormat('ar-LY').format(value);
        }
    }

    // دالة التأخير للبحث
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // إضافة تأثيرات بصرية للمصروفات التلقائية
    document.addEventListener('DOMContentLoaded', function() {
        const automaticRows = document.querySelectorAll('tr[data-automatic="true"]');
        automaticRows.forEach(row => {
            row.style.background = 'linear-gradient(90deg, rgba(59, 130, 246, 0.05) 0%, transparent 100%)';
        });
    });

    // إشعارات النجاح
    @if(session('success'))
    document.addEventListener('DOMContentLoaded', function() {
        showNotification('{{ session('success') }}', 'success');
    });
    @endif

    @if(session('error'))
    document.addEventListener('DOMContentLoaded', function() {
        showNotification('{{ session('error') }}', 'error');
    });
    @endif

    function showNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 ${
            type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
        }`;
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-${type === 'success' ? 'check' : 'exclamation-triangle'} ml-2"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="mr-4 text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
</script>
@endsection