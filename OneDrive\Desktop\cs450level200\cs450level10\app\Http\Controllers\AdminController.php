<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Order;
use App\Models\Payment;
use App\Models\Expense;
use App\Models\FinancialReport;
use App\Models\ReportSource;
use App\Models\Setting;
use App\Models\Notification;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Routing\Controller;

class AdminController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'admin']);
    }

    public function dashboard()
    {
        // إحصائيات عامة
        $totalOrders = Order::count();
        $totalCustomers = User::where('user_type', 'customer')->count();
        $totalRevenue = Payment::sum('amount');
        $totalMenuItems = DB::table('menu_items')->count();

        // متوسط التقييم
        $averageRating = DB::table('reviews')->avg('rating') ?? 4.8;

        // مقارنة بالشهر الماضي
        $currentMonth = now();
        $lastMonth = now()->subMonth();

        // التأكد من استخدام نفس السنة والشهر في المقارنة
        $currentMonthOrders = Order::whereYear('created_at', $currentMonth->year)
            ->whereMonth('created_at', $currentMonth->month)
            ->count();

        $lastMonthOrders = Order::whereYear('created_at', $lastMonth->year)
            ->whereMonth('created_at', $lastMonth->month)
            ->count();

        $orderPercentChange = $lastMonthOrders > 0
            ? round((($currentMonthOrders - $lastMonthOrders) / $lastMonthOrders) * 100, 1)
            : ($currentMonthOrders > 0 ? 100 : 0);

        $currentMonthRevenue = Payment::whereYear('created_at', $currentMonth->year)
            ->whereMonth('created_at', $currentMonth->month)
            ->sum('amount');

        $lastMonthRevenue = Payment::whereYear('created_at', $lastMonth->year)
            ->whereMonth('created_at', $lastMonth->month)
            ->sum('amount');

        $revenuePercentChange = $lastMonthRevenue > 0
            ? round((($currentMonthRevenue - $lastMonthRevenue) / $lastMonthRevenue) * 100, 1)
            : ($currentMonthRevenue > 0 ? 100 : 0);

        $currentMonthCustomers = User::where('user_type', 'customer')
            ->whereYear('created_at', $currentMonth->year)
            ->whereMonth('created_at', $currentMonth->month)
            ->count();

        $lastMonthCustomers = User::where('user_type', 'customer')
            ->whereYear('created_at', $lastMonth->year)
            ->whereMonth('created_at', $lastMonth->month)
            ->count();

        $customerPercentChange = $lastMonthCustomers > 0
            ? round((($currentMonthCustomers - $lastMonthCustomers) / $lastMonthCustomers) * 100, 1)
            : ($currentMonthCustomers > 0 ? 100 : 0);

        // الأكثر مبيعاً
        $topSellingItems = DB::table('order_items')
            ->join('menu_items', 'order_items.menu_item_id', '=', 'menu_items.item_id')
            ->select('menu_items.name', DB::raw('SUM(order_items.quantity) as total_ordered'))
            ->groupBy('menu_items.name')
            ->orderBy('total_ordered', 'desc')
            ->limit(5)
            ->get();

        // بيانات المبيعات للرسم البياني (آخر 7 أيام)
        $salesData = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $dayName = now()->subDays($i)->locale('ar')->dayName;

            $amount = Order::whereDate('created_at', $date)
                ->where('status', 'completed')
                ->sum('total_amount');

            $salesData[] = [
                'day' => $dayName,
                'amount' => $amount
            ];
        }

        // أحدث الطلبات
        $recentOrders = Order::with('user')
            ->latest()
            ->take(5)
            ->get();

        // المكونات منخفضة المخزون
        $lowStockItems = DB::table('inventory')
            ->join('ingredients', 'inventory.ingredient_id', '=', 'ingredients.ingredient_id')
            ->select('ingredients.name', 'inventory.quantity', 'ingredients.unit')
            ->where('inventory.quantity', '<', 10)
            ->get();

        // المكونات التي قاربت على انتهاء الصلاحية
        $expiringItems = DB::table('ingredients')
            ->select('name', 'expiry_date', 'unit')
            ->whereNotNull('expiry_date')
            ->where('expiry_date', '>', now())
            ->where('expiry_date', '<=', now()->addDays(7))
            ->orderBy('expiry_date')
            ->get();

        return view('admin.dashboard', compact(
            'totalOrders',
            'totalCustomers',
            'totalRevenue',
            'totalMenuItems',
            'averageRating',
            'orderPercentChange',
            'revenuePercentChange',
            'customerPercentChange',
            'topSellingItems',
            'salesData',
            'recentOrders',
            'lowStockItems',
            'expiringItems'
        ));
    }

    public function users(Request $request)
    {
        // بناء الاستعلام الأساسي
        $query = User::query();

        // تطبيق البحث إذا تم تقديمه
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = '%' . $request->search . '%';
            $query->where(function($q) use ($searchTerm) {
                $q->where('first_name', 'like', $searchTerm)
                  ->orWhere('last_name', 'like', $searchTerm)
                  ->orWhere('email', 'like', $searchTerm)
                  ->orWhere('phone', 'like', $searchTerm);
            });
        }

        // تطبيق التصفية حسب نوع المستخدم إذا تم تقديمها
        if ($request->has('user_type') && !empty($request->user_type)) {
            $query->where('user_type', $request->user_type);
        }

        // ترتيب النتائج حسب تاريخ الإنشاء (الأحدث أولاً)
        $query->orderBy('created_at', 'desc');

        // تقسيم النتائج إلى صفحات
        $users = $query->paginate(15)->appends($request->except('page'));

        // عرض صفحة قائمة المستخدمين
        return view('admin.users.index', compact('users'));
    }

    public function createUser()
    {
        return view('admin.users.create');
    }

    public function storeUser(Request $request)
    {
        // التحقق من صحة البيانات المدخلة (سيتم استخدام ملفات اللغة العربية تلقائيًا)
        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:50',
            'last_name' => 'required|string|max:50',
            'email' => 'required|email|unique:users,email',
            'phone' => 'required|string|max:20',
            'password' => 'required|string|min:8|confirmed',
            'user_type' => 'required|in:admin,employee,customer',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // إنشاء المستخدم الجديد
        User::create([
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'email' => $request->email,
            'phone' => $request->phone,
            'password' => Hash::make($request->password),
            'user_type' => $request->user_type,
            'is_active' => (bool)$request->input('is_active', 0),
        ]);

        // إعادة التوجيه إلى صفحة قائمة المستخدمين مع رسالة نجاح
        return redirect()->route('admin.users')->with('success', 'تم إنشاء المستخدم بنجاح');
    }

    public function showUser($id)
    {
        // استرجاع بيانات المستخدم
        $user = User::findOrFail($id);

        // إذا كان المستخدم عميل، نقوم بإضافة بعض الإحصائيات
        if ($user->user_type == 'customer') {
            // يمكن إضافة عدد الطلبات وإجمالي المشتريات وعدد الحجوزات
            // هذه مجرد أمثلة، يمكن تعديلها حسب هيكل قاعدة البيانات
            $user->orders_count = $user->orders()->count();
            $user->total_spent = $user->orders()->sum('total_amount');
            $user->reservations_count = $user->reservations()->count();
        }

        // إذا كان المستخدم موظف، نقوم بإضافة بعض الإحصائيات
        if ($user->user_type == 'employee') {
            // يمكن إضافة عدد الطلبات المعالجة ومتوسط التقييم
            // هذه مجرد أمثلة، يمكن تعديلها حسب هيكل قاعدة البيانات
            $user->processed_orders_count = $user->processedOrders()->count();
            $user->average_rating = $user->ratings()->avg('rating') ?? 0;
        }

        return view('admin.users.show', compact('user'));
    }

    public function editUser($id)
    {
        // استرجاع المستخدم بغض النظر عن حالته (نشط أو غير نشط)
        $user = User::withoutGlobalScope('active')->findOrFail($id);
        return view('admin.users.edit', compact('user'));
    }

    public function updateUser(Request $request, $id)
    {
        // استرجاع المستخدم بغض النظر عن حالته (نشط أو غير نشط)
        $user = User::withoutGlobalScope('active')->findOrFail($id);

        // التحقق من صحة البيانات المدخلة (سيتم استخدام ملفات اللغة العربية تلقائيًا)
        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:50',
            'last_name' => 'required|string|max:50',
            'email' => 'required|email|unique:users,email,' . $id . ',user_id',
            'phone' => 'required|string|max:20',
            'user_type' => 'required|in:admin,employee,customer',
            'is_active' => 'boolean',
            'password' => 'nullable|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // تحديث بيانات المستخدم
        $data = [
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'email' => $request->email,
            'phone' => $request->phone,
            'user_type' => $request->user_type,
            'is_active' => (bool)$request->input('is_active', 0),
        ];

        // تحديث كلمة المرور إذا تم إدخالها
        if ($request->filled('password')) {
            $data['password'] = Hash::make($request->password);
        }

        // حفظ التغييرات
        User::where('user_id', $user->user_id)->update($data);

        // إعادة التوجيه مع رسالة نجاح
        return redirect()->route('admin.users')->with('success', 'تم تحديث بيانات المستخدم بنجاح');
    }

    public function deleteUser($id)
    {
        $user = User::findOrFail($id);

        // اختياريًا: تأكد من أنك لا تحذف الحساب الذي تستخدمه حاليًا
        if (Auth::id() == $id) {
            return redirect()->back()->with('error', 'لا يمكنك حذف الحساب الذي تستخدمه حاليًا');
        }

        $user->delete();

        return redirect()->route('admin.users')->with('success', 'تم حذف المستخدم بنجاح');
    }

    public function financialReports()
    {
        $reports = FinancialReport::with('user')->orderBy('created_at', 'desc')->paginate(15);
        return view('admin.reports.index', compact('reports'));
    }

    public function createReport()
    {
        return view('admin.reports.create');
    }

    public function storeReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'report_type' => 'required|in:daily,weekly,monthly,annual,custom',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // حساب مجموع الإيرادات
        $totalIncome = Payment::whereHas('order', function($query) use ($request) {
                $query->whereBetween('created_at', [$request->start_date, $request->end_date]);
            })
            ->sum('amount');

        // حساب مجموع المصروفات
        $totalExpenses = Expense::whereBetween('expense_date', [$request->start_date, $request->end_date])
            ->sum('amount');

        // إنشاء التقرير المالي
        $report = FinancialReport::create([
            'report_type' => $request->report_type,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'total_income' => $totalIncome,
            'total_expenses' => $totalExpenses,
            'generated_by' => Auth::id(), // استبدل auth()->id() بـ Auth::id()
        ]);

        // إضافة مصادر الإيرادات إلى التقرير
        $payments = Payment::whereHas('order', function($query) use ($request) {
                $query->whereBetween('created_at', [$request->start_date, $request->end_date]);
            })
            ->get();

        foreach ($payments as $payment) {
            ReportSource::create([
                'report_id' => $report->report_id,
                'source_type' => 'payment',
                'source_ref_id' => $payment->payment_id,
                'amount' => $payment->amount,
            ]);
        }

        // إضافة المصروفات إلى التقرير
        $expenses = Expense::whereBetween('expense_date', [$request->start_date, $request->end_date])
            ->get();

        foreach ($expenses as $expense) {
            ReportSource::create([
                'report_id' => $report->report_id,
                'source_type' => 'expense',
                'source_ref_id' => $expense->expense_id,
                'amount' => -$expense->amount, // قيمة سالبة للمصروفات
            ]);
        }

        return redirect()->route('admin.reports.view', $report->report_id)->with('success', 'تم إنشاء التقرير المالي بنجاح');
    }

    public function viewReport($id)
    {
        $report = FinancialReport::with(['sources.sourceItem', 'user'])->findOrFail($id);

        // تقسيم المصادر إلى إيرادات ومصروفات
        $incomeSources = $report->sources->where('amount', '>', 0);
        $expenseSources = $report->sources->where('amount', '<', 0);

        return view('admin.reports.view', compact('report', 'incomeSources', 'expenseSources'));
    }

    public function settings()
    {
        // استرجاع الإعدادات من قاعدة البيانات
        $settings = Setting::getAllSettings();

        return view('admin.settings', compact('settings'));
    }

    public function updateSettings(Request $request)
    {
        // التحقق من البيانات
        $validator = Validator::make($request->all(), [
            'restaurant_name' => 'nullable|string|max:100',
            'restaurant_phone' => 'nullable|string|max:20',
            'restaurant_email' => 'nullable|email|max:100',
            'restaurant_address' => 'nullable|string|max:255',
            'tax_rate' => 'nullable|numeric|min:0|max:100',
            'service_fee' => 'nullable|numeric|min:0|max:100',
            'include_tax_in_price' => 'nullable',
            'currency' => 'nullable|string|max:10',
            'timezone' => 'nullable|string|max:50',
            'date_format' => 'nullable|string|max:20',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            // حفظ الإعدادات العامة
            Setting::updateOrCreateSetting('restaurant_name', $request->restaurant_name, 'general');
            Setting::updateOrCreateSetting('restaurant_phone', $request->restaurant_phone, 'general');
            Setting::updateOrCreateSetting('restaurant_email', $request->restaurant_email, 'general');
            Setting::updateOrCreateSetting('restaurant_address', $request->restaurant_address, 'general');

            // حفظ إعدادات الضرائب والرسوم
            Setting::updateOrCreateSetting('tax_rate', $request->tax_rate, 'tax');
            Setting::updateOrCreateSetting('service_fee', $request->service_fee, 'tax');
            Setting::updateOrCreateSetting('include_tax_in_price', $request->has('include_tax_in_price') ? 'نعم' : 'لا', 'tax');

            // حفظ إعدادات النظام
            Setting::updateOrCreateSetting('currency', $request->currency, 'system');
            Setting::updateOrCreateSetting('timezone', $request->timezone, 'system');
            Setting::updateOrCreateSetting('date_format', $request->date_format, 'system');

            return redirect()->route('admin.settings')->with('success', 'تم تحديث الإعدادات بنجاح');
        } catch (\Exception $e) {
            return redirect()->route('admin.settings')->with('error', 'حدث خطأ أثناء حفظ الإعدادات: ' . $e->getMessage());
        }
    }

    public function profile()
    {
        $user = Auth::user();
        return view('admin.profile', compact('user'));
    }

    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:50',
            'last_name' => 'required|string|max:50',
            'email' => 'required|email|unique:users,email,' . $user->user_id . ',user_id',
            'phone' => 'required|string|max:20',
            'current_password' => 'nullable|required_with:password',
            'password' => 'nullable|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // التحقق من كلمة المرور الحالية إذا تم تقديمها
        if ($request->filled('current_password') && !Hash::check($request->current_password, $user->password)) {
            return redirect()->back()->withErrors(['current_password' => 'كلمة المرور الحالية غير صحيحة'])->withInput();
        }

        $data = [
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'email' => $request->email,
            'phone' => $request->phone,
        ];

        if ($request->filled('password')) {
            $data['password'] = Hash::make($request->password);
        }

        User::where('user_id', $user->user_id)->update($data);

        return redirect()->route('admin.profile')->with('success', 'تم تحديث الملف الشخصي بنجاح');
    }

    // عرض صفحة الإشعارات
    public function notifications(Request $request)
    {
        try {
            // الأدمن يرى جميع الإشعارات مع معلومات المستخدم
            $query = Notification::with('user');

            // تطبيق الفلتر إذا تم تحديده
            if ($request->has('filter')) {
                if ($request->filter === 'read') {
                    $query->where('is_read', true);
                } elseif ($request->filter === 'unread') {
                    $query->where('is_read', false);
                }
            }

            $notifications = $query->orderBy('created_at', 'desc')
                ->paginate(10)
                ->withQueryString();

            // إحصائيات الإشعارات
            $stats = [
                'total' => Notification::count(),
                'read' => Notification::where('is_read', true)->count(),
                'unread' => Notification::where('is_read', false)->count(),
                'system' => Notification::where('type', 'system')->count(),
                'order' => Notification::where('type', 'order')->count(),
                'reservation' => Notification::where('type', 'reservation')->count(),
                'inventory' => Notification::where('type', 'inventory')->count(),
            ];

            return view('admin.notifications.index', compact('notifications', 'stats'));
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error in admin notifications: ' . $e->getMessage());
            return view('admin.notifications.index')->with('error', 'حدث خطأ أثناء تحميل الإشعارات');
        }
    }

    // عرض صفحة إنشاء إشعار جديد
    public function createNotification()
    {
        try {
            $users = User::orderBy('user_type')
                ->orderBy('first_name')
                ->get();

            return view('admin.notifications.create', compact('users'));
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error in admin create notification: ' . $e->getMessage());
            return redirect()->route('admin.notifications')->with('error', 'حدث خطأ أثناء تحميل صفحة إنشاء الإشعار');
        }
    }

    // إرسال إشعار جديد
    public function sendNotification(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'recipients' => 'required|in:all,admins,employees,staff,customers,specific',
            'user_id' => 'required_if:recipients,specific|exists:users,user_id',
            'type' => 'required|in:system,order,reservation,inventory',
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'action_url' => 'nullable|string|max:255',
            'action_text' => 'nullable|string|max:100',
        ], [
            'recipients.required' => 'يرجى تحديد المستلمين',
            'recipients.in' => 'قيمة المستلمين غير صالحة',
            'user_id.required_if' => 'يرجى تحديد المستخدم',
            'user_id.exists' => 'المستخدم المحدد غير موجود',
            'type.required' => 'يرجى تحديد نوع الإشعار',
            'type.in' => 'نوع الإشعار غير صالح',
            'title.required' => 'عنوان الإشعار مطلوب',
            'title.max' => 'عنوان الإشعار يجب أن لا يتجاوز 255 حرفاً',
            'message.required' => 'نص الإشعار مطلوب',
            'action_url.max' => 'رابط الإجراء يجب أن لا يتجاوز 255 حرفاً',
            'action_text.max' => 'نص الإجراء يجب أن لا يتجاوز 100 حرفاً',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            $notificationService = new NotificationService();
            $title = $request->title;
            $message = $request->message;
            $type = $request->type;
            $actionUrl = $request->action_url;
            $actionText = $request->action_text;

            switch ($request->recipients) {
                case 'all':
                    $notificationService->notifyAll($title, $message, $type, null, $actionUrl, $actionText);
                    $recipientsText = 'جميع المستخدمين';
                    break;
                case 'admins':
                    $notificationService->notifyAdmins($title, $message, $type, null, $actionUrl, $actionText);
                    $recipientsText = 'المسؤولين';
                    break;
                case 'employees':
                    $notificationService->notifyEmployees($title, $message, $type, null, $actionUrl, $actionText);
                    $recipientsText = 'الموظفين';
                    break;
                case 'staff':
                    $notificationService->notifyStaff($title, $message, $type, null, $actionUrl, $actionText);
                    $recipientsText = 'طاقم العمل';
                    break;
                case 'customers':
                    $notificationService->notifyCustomers($title, $message, $type, null, $actionUrl, $actionText);
                    $recipientsText = 'العملاء';
                    break;
                case 'specific':
                    $user = User::findOrFail($request->user_id);
                    $notificationService->notifyUser($user, $title, $message, $type, null, $actionUrl, $actionText);
                    $recipientsText = $user->first_name . ' ' . $user->last_name;
                    break;
            }

            return redirect()->route('admin.notifications')->with('success', 'تم إرسال الإشعار بنجاح إلى ' . $recipientsText);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error sending notification: ' . $e->getMessage());
            return redirect()->back()->with('error', 'حدث خطأ أثناء إرسال الإشعار: ' . $e->getMessage())->withInput();
        }
    }

    // عرض تفاصيل الإشعار
    public function showNotification($id)
    {
        try {
            $notification = Notification::where('user_id', Auth::id())
                ->where('notification_id', $id)
                ->firstOrFail();

            // تحديث حالة الإشعار إلى مقروء عند عرضه
            if (!$notification->is_read) {
                $notification->is_read = true;
                $notification->save();
            }

            return view('admin.notifications.show', compact('notification'));
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error showing notification: ' . $e->getMessage());
            return redirect()->route('admin.notifications')->with('error', 'الإشعار غير موجود');
        }
    }

    // تحديد إشعار كمقروء
    public function markNotificationAsRead($id)
    {
        try {
            $notification = Notification::where('user_id', Auth::id())
                ->where('notification_id', $id)
                ->firstOrFail();

            $notification->is_read = true;
            $notification->save();

            return redirect()->back()->with('success', 'تم تحديد الإشعار كمقروء');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error marking notification as read: ' . $e->getMessage());
            return redirect()->back()->with('error', 'حدث خطأ أثناء تحديد الإشعار كمقروء');
        }
    }

    // تحديد جميع الإشعارات كمقروءة
    public function markAllNotificationsAsRead()
    {
        try {
            // الأدمن يحدد جميع الإشعارات كمقروءة
            Notification::where('is_read', false)
                ->update(['is_read' => true]);

            return redirect()->back()->with('success', 'تم تحديد جميع الإشعارات كمقروءة');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error marking all notifications as read: ' . $e->getMessage());
            return redirect()->back()->with('error', 'حدث خطأ أثناء تحديد جميع الإشعارات كمقروءة');
        }
    }

    // حذف إشعار
    public function deleteNotification($id)
    {
        try {
            $notification = Notification::where('user_id', Auth::id())
                ->where('notification_id', $id)
                ->firstOrFail();

            $notification->delete();

            return redirect()->route('admin.notifications')->with('success', 'تم حذف الإشعار بنجاح');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error deleting notification: ' . $e->getMessage());
            return redirect()->back()->with('error', 'حدث خطأ أثناء حذف الإشعار');
        }
    }

    // حذف الحساب الشخصي
    public function deleteProfile(Request $request)
    {
        try {
            $user = Auth::user();

            // التحقق من أن المستخدم ليس المسؤول الوحيد في النظام
            $adminCount = User::where('user_type', 'admin')->count();
            if ($adminCount <= 1 && $user->user_type == 'admin') {
                return redirect()->route('admin.profile')->with('error', 'لا يمكن حذف الحساب لأنك المسؤول الوحيد في النظام');
            }

            // تسجيل الخروج
            Auth::logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            // حذف الحساب
            User::where('user_id', $user->user_id)->delete();

            return redirect()->route('login')->with('success', 'تم حذف حسابك بنجاح');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error deleting profile: ' . $e->getMessage());
            return redirect()->back()->with('error', 'حدث خطأ أثناء حذف الحساب: ' . $e->getMessage());
        }
    }

    /**
     * جلب بيانات مخطط لوحة التحكم حسب الفترة
     */
    public function getDashboardChartData(Request $request)
    {
        $period = $request->get('period', 7); // عدد الأيام

        $salesData = [];
        $endDate = now();
        $startDate = now()->subDays($period - 1);

        // جلب البيانات حسب الفترة
        for ($date = $startDate->copy(); $date <= $endDate; $date->addDay()) {
            $dayName = $date->locale('ar')->dayName;

            $amount = Order::whereDate('created_at', $date)
                ->where('status', 'completed')
                ->sum('total_amount');

            $salesData[] = [
                'day' => $dayName,
                'amount' => (float) $amount
            ];
        }

        return response()->json([
            'days' => array_column($salesData, 'day'),
            'amounts' => array_column($salesData, 'amount')
        ]);
    }
}