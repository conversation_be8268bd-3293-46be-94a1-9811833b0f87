# أمثلة عملية لاستخدام نظام الصلاحيات

## 1. إنشاء مستخدم جديد وإعطاؤه صلاحيات

### في الكونسول (Tinker)
```bash
php artisan tinker
```

```php
// إنشاء موظف جديد
$employee = App\Models\User::create([
    'first_name' => 'محمد',
    'last_name' => 'أحمد',
    'email' => '<EMAIL>',
    'password' => bcrypt('password123'),
    'phone' => '+218912345678',
    'user_type' => 'employee',
    'is_active' => true
]);

// إعطاؤه دور الموظف
$employee->assignRole('employee');

// إعطاؤه صلاحيات إضافية
$employee->givePermissionTo(['inventory.view', 'reports.sales']);

// التحقق من الصلاحيات
$employee->can('orders.view'); // true (من دور الموظف)
$employee->can('users.delete'); // false
$employee->can('inventory.view'); // true (صلاحية مباشرة)
```

## 2. إنشاء دور مخصص

```php
// إنشاء دور "مدير مبيعات"
$salesManager = Spatie\Permission\Models\Role::create(['name' => 'sales_manager']);

// إعطاؤه صلاحيات محددة
$salesManager->givePermissionTo([
    'orders.view', 'orders.create', 'orders.edit', 'orders.status',
    'menu.view',
    'reports.view', 'reports.sales',
    'customers.view'
]);

// تعيين المستخدم لهذا الدور
$user = App\Models\User::find(1);
$user->assignRole('sales_manager');
```

## 3. استخدام الصلاحيات في الكونترولر

```php
<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class OrderController extends Controller
{
    public function __construct()
    {
        // التحقق من صلاحية عرض الطلبات لجميع الدوال
        $this->middleware('permission:orders.view');
        
        // صلاحيات محددة لدوال معينة
        $this->middleware('permission:orders.create')->only(['create', 'store']);
        $this->middleware('permission:orders.edit')->only(['edit', 'update']);
        $this->middleware('permission:orders.delete')->only(['destroy']);
    }

    public function index()
    {
        // عرض الطلبات حسب صلاحيات المستخدم
        $query = Order::query();
        
        // إذا لم يكن مديراً، عرض طلباته فقط
        if (!auth()->user()->can('orders.view_all')) {
            $query->where('created_by', auth()->id());
        }
        
        $orders = $query->paginate(15);
        return view('admin.orders.index', compact('orders'));
    }

    public function updateStatus(Request $request, $id)
    {
        // التحقق من صلاحية تغيير الحالة
        if (!auth()->user()->can('orders.status')) {
            return response()->json(['error' => 'ليس لديك صلاحية لتغيير حالة الطلب'], 403);
        }

        $order = Order::findOrFail($id);
        $order->update(['status' => $request->status]);
        
        return response()->json(['success' => true]);
    }
}
```

## 4. استخدام الصلاحيات في Blade Templates

```blade
{{-- في صفحة قائمة الطلبات --}}
<div class="actions">
    @can('orders.create')
        <a href="{{ route('admin.orders.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> طلب جديد
        </a>
    @endcan
    
    @can('orders.export')
        <a href="{{ route('admin.orders.export') }}" class="btn btn-secondary">
            <i class="fas fa-download"></i> تصدير
        </a>
    @endcan
</div>

{{-- في جدول الطلبات --}}
<table class="table">
    @foreach($orders as $order)
    <tr>
        <td>{{ $order->id }}</td>
        <td>{{ $order->customer_name }}</td>
        <td>{{ $order->total_amount }}</td>
        <td>
            @can('orders.edit')
                <a href="{{ route('admin.orders.edit', $order) }}" class="btn btn-sm btn-warning">
                    تعديل
                </a>
            @endcan
            
            @can('orders.delete')
                <button onclick="deleteOrder({{ $order->id }})" class="btn btn-sm btn-danger">
                    حذف
                </button>
            @endcan
            
            @can('orders.status')
                <select onchange="updateStatus({{ $order->id }}, this.value)">
                    <option value="pending" {{ $order->status == 'pending' ? 'selected' : '' }}>قيد الانتظار</option>
                    <option value="confirmed" {{ $order->status == 'confirmed' ? 'selected' : '' }}>مؤكد</option>
                    <option value="completed" {{ $order->status == 'completed' ? 'selected' : '' }}>مكتمل</option>
                </select>
            @endcan
        </td>
    </tr>
    @endforeach
</table>

{{-- إخفاء أقسام كاملة حسب الصلاحيات --}}
@can('reports.view')
<div class="reports-section">
    <h3>التقارير</h3>
    
    @can('reports.financial')
        <a href="{{ route('admin.reports.financial') }}">التقارير المالية</a>
    @endcan
    
    @can('reports.sales')
        <a href="{{ route('admin.reports.sales') }}">تقارير المبيعات</a>
    @endcan
</div>
@endcan
```

## 5. استخدام الأدوار

```blade
{{-- التحقق من الدور --}}
@role('admin')
    <div class="admin-panel">
        <h2>لوحة تحكم المدير</h2>
        {{-- محتوى خاص بالمدير --}}
    </div>
@endrole

@role('employee')
    <div class="employee-panel">
        <h2>لوحة تحكم الموظف</h2>
        {{-- محتوى خاص بالموظف --}}
    </div>
@endrole

{{-- التحقق من عدة أدوار --}}
@hasanyrole('admin|manager')
    <div class="management-tools">
        {{-- أدوات الإدارة --}}
    </div>
@endhasanyrole
```

## 6. إنشاء Middleware مخصص

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class RestrictToOwnData
{
    public function handle(Request $request, Closure $next, $model = null)
    {
        $user = auth()->user();
        
        // المديرون يمكنهم الوصول لكل شيء
        if ($user->hasRole('admin')) {
            return $next($request);
        }
        
        // التحقق من ملكية البيانات
        if ($model && $request->route($model)) {
            $modelInstance = $request->route($model);
            
            if ($modelInstance->user_id !== $user->user_id) {
                abort(403, 'ليس لديك صلاحية للوصول لهذه البيانات');
            }
        }
        
        return $next($request);
    }
}
```

## 7. سيناريوهات عملية

### سيناريو 1: موظف استقبال
```php
$receptionist = User::create([...]);
$receptionist->assignRole('employee');
$receptionist->givePermissionTo([
    'reservations.view', 'reservations.create', 'reservations.edit',
    'tables.view', 'tables.status',
    'customers.view'
]);
```

### سيناريو 2: طباخ
```php
$chef = User::create([...]);
$chefRole = Role::create(['name' => 'chef']);
$chefRole->givePermissionTo([
    'orders.view', 'orders.status',
    'menu.view',
    'inventory.view'
]);
$chef->assignRole('chef');
```

### سيناريو 3: محاسب
```php
$accountant = User::create([...]);
$accountantRole = Role::create(['name' => 'accountant']);
$accountantRole->givePermissionTo([
    'orders.view',
    'expenses.view', 'expenses.create', 'expenses.edit',
    'reports.view', 'reports.financial', 'reports.sales',
    'payments.view'
]);
$accountant->assignRole('accountant');
```

## 8. نصائح للاستخدام الأمثل

### تجميع الصلاحيات
```php
// بدلاً من إعطاء صلاحيات فردية
$user->givePermissionTo(['orders.view', 'orders.create', 'orders.edit']);

// استخدم الأدوار
$orderManagerRole = Role::create(['name' => 'order_manager']);
$orderManagerRole->givePermissionTo(['orders.view', 'orders.create', 'orders.edit']);
$user->assignRole('order_manager');
```

### التحقق المتقدم
```php
// في الكونترولر
public function show($id)
{
    $order = Order::findOrFail($id);
    
    // التحقق من الصلاحية والملكية
    if (!auth()->user()->can('orders.view') || 
        (!auth()->user()->hasRole('admin') && $order->user_id !== auth()->id())) {
        abort(403);
    }
    
    return view('orders.show', compact('order'));
}
```

### استخدام Gates مخصصة
```php
// في AuthServiceProvider
Gate::define('manage-order', function ($user, $order) {
    return $user->can('orders.edit') && 
           ($user->hasRole('admin') || $order->created_by === $user->id);
});

// في الكونترولر
if (Gate::denies('manage-order', $order)) {
    abort(403);
}
```
