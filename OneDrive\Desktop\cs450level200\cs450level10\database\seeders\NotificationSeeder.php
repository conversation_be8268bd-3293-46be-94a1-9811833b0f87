<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Notification;
use App\Models\User;

class NotificationSeeder extends Seeder
{
    public function run()
    {
        // التأكد من وجود مستخدمين
        $admin = User::where('user_type', 'admin')->first();
        $employee = User::where('user_type', 'employee')->first();
        $customers = User::where('user_type', 'customer')->take(3)->get();

        if (!$admin) {
            $admin = User::create([
                'first_name' => 'المدير',
                'last_name' => 'العام',
                'email' => '<EMAIL>',
                'phone' => '0911111111',
                'password' => bcrypt('password'),
                'user_type' => 'admin',
                'is_active' => true
            ]);
        }

        if (!$employee) {
            $employee = User::create([
                'first_name' => 'موظف',
                'last_name' => 'الخدمة',
                'email' => '<EMAIL>',
                'phone' => '0922222222',
                'password' => bcrypt('password'),
                'user_type' => 'employee',
                'is_active' => true
            ]);
        }

        // إنشاء إشعارات متنوعة
        $notifications = [
            // إشعارات للأدمن
            [
                'user_id' => $admin->user_id,
                'title' => 'طلب جديد تم استلامه',
                'message' => 'تم استلام طلب جديد رقم #1001 من العميل أحمد محمد',
                'type' => 'order',
                'is_read' => false,
                'created_at' => now()->subHours(1)
            ],
            [
                'user_id' => $admin->user_id,
                'title' => 'حجز طاولة جديد',
                'message' => 'تم حجز طاولة رقم 5 لتاريخ ' . now()->addDays(2)->format('Y-m-d'),
                'type' => 'reservation',
                'is_read' => true,
                'created_at' => now()->subHours(3)
            ],
            [
                'user_id' => $admin->user_id,
                'title' => 'تحديث النظام',
                'message' => 'تم تحديث النظام بنجاح إلى الإصدار 2.1.0',
                'type' => 'system',
                'is_read' => false,
                'created_at' => now()->subHours(5)
            ],
            [
                'user_id' => $admin->user_id,
                'title' => 'نفاد المخزون',
                'message' => 'تحذير: مخزون البرجر الكلاسيكي أقل من 10 قطع',
                'type' => 'inventory',
                'is_read' => false,
                'created_at' => now()->subHours(2)
            ],
            [
                'user_id' => $admin->user_id,
                'title' => 'رسالة تواصل جديدة',
                'message' => 'تم استلام رسالة تواصل جديدة من العميل فاطمة علي',
                'type' => 'contact',
                'is_read' => true,
                'created_at' => now()->subDays(1)
            ],

            // إشعارات للموظف
            [
                'user_id' => $employee->user_id,
                'title' => 'طلب جاهز للتحضير',
                'message' => 'الطلب رقم #1002 جاهز للتحضير - طاولة رقم 3',
                'type' => 'order',
                'is_read' => false,
                'created_at' => now()->subMinutes(30)
            ],
            [
                'user_id' => $employee->user_id,
                'title' => 'تذكير بالحجز',
                'message' => 'تذكير: حجز طاولة رقم 7 في الساعة 7:00 مساءً',
                'type' => 'reservation',
                'is_read' => true,
                'created_at' => now()->subHours(4)
            ],
            [
                'user_id' => $employee->user_id,
                'title' => 'تحديث الوردية',
                'message' => 'تم تحديث جدول الورديات لهذا الأسبوع',
                'type' => 'system',
                'is_read' => false,
                'created_at' => now()->subHours(6)
            ]
        ];

        // إضافة إشعارات للعملاء إذا كانوا موجودين
        if ($customers->count() > 0) {
            foreach ($customers as $index => $customer) {
                $notifications[] = [
                    'user_id' => $customer->user_id,
                    'title' => 'تم تأكيد طلبك',
                    'message' => 'تم تأكيد طلبك رقم #' . (1010 + $index) . ' وسيتم تحضيره قريباً',
                    'type' => 'order',
                    'is_read' => $index % 2 == 0, // بعض الإشعارات مقروءة وبعضها غير مقروء
                    'created_at' => now()->subHours($index + 1)
                ];

                $notifications[] = [
                    'user_id' => $customer->user_id,
                    'title' => 'عرض خاص لك!',
                    'message' => 'احصل على خصم 20% على طلبك القادم باستخدام كود SAVE20',
                    'type' => 'system',
                    'is_read' => false,
                    'created_at' => now()->subDays($index + 1)
                ];
            }
        }

        // إنشاء الإشعارات
        foreach ($notifications as $notificationData) {
            Notification::create($notificationData);
        }

        // إضافة المزيد من الإشعارات للاختبار
        for ($i = 1; $i <= 15; $i++) {
            Notification::create([
                'user_id' => $admin->user_id,
                'title' => 'إشعار تجريبي رقم ' . $i,
                'message' => 'هذا إشعار تجريبي رقم ' . $i . ' لاختبار الترقيم والفلترة',
                'type' => ['order', 'system', 'reservation', 'inventory', 'contact'][rand(0, 4)],
                'is_read' => rand(0, 1) == 1,
                'created_at' => now()->subHours(rand(1, 48))
            ]);
        }

        echo "تم إنشاء " . Notification::count() . " إشعار تجريبي\n";
        echo "الإشعارات غير المقروءة: " . Notification::where('is_read', false)->count() . "\n";
        echo "الإشعارات المقروءة: " . Notification::where('is_read', true)->count() . "\n";
    }
}
