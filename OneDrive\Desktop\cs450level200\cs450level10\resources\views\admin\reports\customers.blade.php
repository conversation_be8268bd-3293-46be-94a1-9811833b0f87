@extends('layouts.admin')

@section('title', 'تقرير العملاء')

@section('content')
<div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
    <div>
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white">تقرير العملاء</h2>
        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">عرض تفاصيل وإحصائيات العملاء</p>
    </div>
    <div class="mt-4 md:mt-0">
        <a href="{{ route('admin.reports') }}" class="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
            <i class="fas fa-arrow-right ml-2"></i>
            <span>العودة للتقارير</span>
        </a>
    </div>
</div>

<!-- ملخص العملاء -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="rounded-full bg-blue-100 dark:bg-blue-900/30 p-3 ml-4">
                <i class="fas fa-users text-blue-500 text-xl"></i>
            </div>
            <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">إجمالي العملاء</p>
                <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ $totalCustomers }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="rounded-full bg-green-100 dark:bg-green-900/30 p-3 ml-4">
                <i class="fas fa-user-plus text-green-500 text-xl"></i>
            </div>
            <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">عملاء جدد (آخر 30 يوم)</p>
                <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ $newCustomers }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="rounded-full bg-purple-100 dark:bg-purple-900/30 p-3 ml-4">
                <i class="fas fa-user-check text-purple-500 text-xl"></i>
            </div>
            <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">عملاء نشطين (آخر 90 يوم)</p>
                <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ $activeCustomers }}</p>
            </div>
        </div>
    </div>
</div>

<!-- متوسط قيمة الطلب لكل عميل -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
    <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">متوسط قيمة الطلب لكل عميل</h3>
    <div class="flex items-center">
        <div class="rounded-full bg-green-100 dark:bg-green-900/30 p-3 ml-4">
            <i class="fas fa-money-bill-wave text-green-500 text-xl"></i>
        </div>
        <div>
            <p class="text-3xl font-bold text-gray-800 dark:text-white">{{ number_format($averageOrderValuePerCustomer, 2) }} د.ل</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">متوسط قيمة الطلب لكل عميل</p>
        </div>
    </div>
</div>

<!-- أفضل 10 عملاء -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white">أفضل 10 عملاء من حيث قيمة الطلبات</h3>
    </div>

    <div class="overflow-x-auto">
        <table class="w-full">
            <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الاسم</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">البريد الإلكتروني</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">رقم الهاتف</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">عدد الطلبات</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">إجمالي المشتريات</th>
                </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                @foreach($topCustomers as $customer)
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-white">{{ $customer->first_name }} {{ $customer->last_name }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-white">{{ $customer->email }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-white">{{ $customer->phone }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-white">{{ $customer->orders_count }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-white">{{ number_format($customer->total_spent, 2) }} د.ل</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>

<!-- رسم بياني لتوزيع العملاء -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">نسبة العملاء النشطين</h3>
        <div class="h-64">
            <canvas id="activeCustomersChart"></canvas>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">نسبة العملاء الجدد</h3>
        <div class="h-64">
            <canvas id="newCustomersChart"></canvas>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // رسم بياني للعملاء النشطين
        const activeCtx = document.getElementById('activeCustomersChart').getContext('2d');
        const activeData = {
            labels: ['نشط', 'غير نشط'],
            datasets: [{
                data: [{{ $activeCustomers }}, {{ $totalCustomers - $activeCustomers }}],
                backgroundColor: [
                    'rgba(72, 187, 120, 0.7)',
                    'rgba(160, 174, 192, 0.7)'
                ],
                borderColor: [
                    'rgba(72, 187, 120, 1)',
                    'rgba(160, 174, 192, 1)'
                ],
                borderWidth: 1
            }]
        };

        new Chart(activeCtx, {
            type: 'pie',
            data: activeData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                family: 'Tajawal'
                            }
                        }
                    }
                }
            }
        });

        // رسم بياني للعملاء الجدد
        const newCtx = document.getElementById('newCustomersChart').getContext('2d');
        const newData = {
            labels: ['جديد', 'قديم'],
            datasets: [{
                data: [{{ $newCustomers }}, {{ $totalCustomers - $newCustomers }}],
                backgroundColor: [
                    'rgba(66, 153, 225, 0.7)',
                    'rgba(160, 174, 192, 0.7)'
                ],
                borderColor: [
                    'rgba(66, 153, 225, 1)',
                    'rgba(160, 174, 192, 1)'
                ],
                borderWidth: 1
            }]
        };

        new Chart(newCtx, {
            type: 'pie',
            data: newData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                family: 'Tajawal'
                            }
                        }
                    }
                }
            }
        });
    });
</script>
@endpush
@endsection
