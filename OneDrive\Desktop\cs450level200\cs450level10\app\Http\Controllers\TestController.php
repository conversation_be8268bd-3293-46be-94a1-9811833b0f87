<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use Illuminate\Routing\Controller;

class TestController extends Controller
{
    public function testLogin()
    {
        // إنشاء مستخدم مدير للاختبار
        $email = '<EMAIL>';
        $password = 'test123';

        // التحقق مما إذا كان المستخدم موجوداً بالفعل
        $user = User::where('email', $email)->first();

        if (!$user) {
            // إنشاء مستخدم جديد
            $user = User::create([
                'first_name' => 'مستخدم',
                'last_name' => 'اختبار',
                'email' => $email,
                'password' => Hash::make($password),
                'phone' => '**********',
                'user_type' => 'admin',
                'is_active' => true
            ]);
        }

        // محاولة تسجيل الدخول
        $credentials = [
            'email' => $email,
            'password' => $password
        ];

        if (Auth::attempt($credentials)) {
            // تم تسجيل الدخول بنجاح
            $loggedInUser = Auth::user();

            return response()->json([
                'success' => true,
                'message' => 'تم تسجيل الدخول بنجاح',
                'user' => [
                    'id' => $loggedInUser->user_id,
                    'name' => $loggedInUser->first_name . ' ' . $loggedInUser->last_name,
                    'email' => $loggedInUser->email,
                    'user_type' => $loggedInUser->user_type,
                    'is_active' => $loggedInUser->is_active
                ]
            ]);
        }

        // فشل تسجيل الدخول
        return response()->json([
            'success' => false,
            'message' => 'فشل تسجيل الدخول',
            'credentials' => $credentials
        ]);
    }
    public function listUsers()
    {
        $users = User::all();

        $formattedUsers = $users->map(function($user) {
            return [
                'id' => $user->user_id,
                'name' => $user->first_name . ' ' . $user->last_name,
                'email' => $user->email,
                'user_type' => $user->user_type,
                'is_active' => $user->is_active
            ];
        });

        return response()->json([
            'count' => $users->count(),
            'users' => $formattedUsers
        ]);
    }

    public function createAdmin()
    {
        // إنشاء مستخدم مدير جديد
        $admin = User::create([
            'first_name' => 'مدير',
            'last_name' => 'النظام',
            'email' => '<EMAIL>',
            'password' => Hash::make('A178a2002'),
            'phone' => '919676123',
            'user_type' => 'admin',
            'is_active' => true
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء مستخدم مدير جديد بنجاح',
            'admin' => [
                'id' => $admin->user_id,
                'name' => $admin->first_name . ' ' . $admin->last_name,
                'email' => $admin->email,
                'user_type' => $admin->user_type,
                'is_active' => $admin->is_active
            ]
        ]);
    }

    public function loginAsAdmin()
    {
        // تسجيل الدخول كمدير
        $email = '<EMAIL>';
        $password = 'admin123';

        // محاولة تسجيل الدخول
        $credentials = [
            'email' => $email,
            'password' => $password
        ];

        if (Auth::attempt($credentials)) {
            // تم تسجيل الدخول بنجاح
            $user = Auth::user();

            // توجيه المستخدم حسب نوعه
            if ($user->user_type === 'admin') {
                return redirect()->route('admin.dashboard');
            } elseif ($user->user_type === 'employee') {
                return redirect()->route('employee.dashboard');
            } else {
                return redirect()->route('customer.dashboard');
            }
        }

        // فشل تسجيل الدخول
        return response()->json([
            'success' => false,
            'message' => 'فشل تسجيل الدخول',
            'credentials' => $credentials
        ]);
    }
}