-- ملف إعداد قاعدة البيانات لمشروع Eat Hub
-- Restaurant Management System Database Setup

-- إنشاء قاعدة البيانات الجديدة
CREATE DATABASE IF NOT EXISTS eat_hub_new 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- استخدام قاعدة البيانات الجديدة
USE eat_hub_new;

-- إنشاء مستخدم مخصص لقاعدة البيانات (اختياري)
-- CREATE USER 'eat_hub_user'@'localhost' IDENTIFIED BY 'secure_password_here';
-- GRANT ALL PRIVILEGES ON eat_hub_new.* TO 'eat_hub_user'@'localhost';
-- FLUSH PRIVILEGES;

-- عرض معلومات قاعدة البيانات
SELECT 
    SCHEMA_NAME as 'Database Name',
    DEFAULT_CHARACTER_SET_NAME as 'Character Set',
    DEFAULT_COLLATION_NAME as 'Collation'
FROM information_schema.SCHEMATA 
WHERE SCHEMA_NAME = 'eat_hub_new';

-- عرض رسالة تأكيد
SELECT 'تم إنشاء قاعدة البيانات بنجاح!' as 'Status';
SELECT 'يمكنك الآن تشغيل: php artisan migrate' as 'Next Step';
