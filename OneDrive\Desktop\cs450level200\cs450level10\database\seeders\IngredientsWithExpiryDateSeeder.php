<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class IngredientsWithExpiryDateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // استيراد النماذج والمكتبات اللازمة
        $ingredient = \App\Models\Ingredient::class;
        $carbon = \Carbon\Carbon::class;

        // إضافة مكون صلصة بيستو مع تاريخ انتهاء صلاحية بعد 3 أيام
        $ingredient::updateOrCreate(
            ['name' => 'صلصة بيستو'],
            [
                'unit' => 'لتر',
                'is_active' => true,
                'expiry_date' => $carbon::now()->addDays(3)
            ]
        );

        // إضافة مكون كريمة طبخ مع تاريخ انتهاء صلاحية بعد 5 أيام
        $ingredient::updateOrCreate(
            ['name' => 'كريمة طبخ'],
            [
                'unit' => 'لتر',
                'is_active' => true,
                'expiry_date' => $carbon::now()->addDays(5)
            ]
        );
    }
}
