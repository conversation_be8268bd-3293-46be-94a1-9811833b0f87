<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Eat Hub - اختبار Laravel</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary: #f59e0b;
            --primary-dark: #d97706;
        }
        .text-primary { color: var(--primary); }
        .bg-primary { background-color: var(--primary); }
        .border-primary { border-color: var(--primary); }
        .hover\:bg-primary\/90:hover { background-color: rgba(245, 158, 11, 0.9); }
        .bg-primary\/10 { background-color: rgba(245, 158, 11, 0.1); }
        .page { display: none; }
        .page.active { display: block; }
        .nav-link.active { color: var(--primary) !important; }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900">

<!-- شريط التنقل -->
<header class="bg-white dark:bg-gray-800 shadow-md sticky top-0 z-50">
    <div class="container mx-auto px-4">
        <div class="flex justify-between items-center h-16">
            <!-- الشعار -->
            <div class="flex items-center">
                <i class="fas fa-utensils text-primary text-2xl ml-3"></i>
                <h1 class="text-xl font-bold text-gray-800 dark:text-white">Eat Hub</h1>
            </div>

            <!-- قائمة التنقل -->
            <nav class="hidden md:flex space-x-8 space-x-reverse">
                <a href="#" data-page="home" class="nav-link text-gray-700 dark:text-gray-300 hover:text-primary transition">الرئيسية</a>
                <a href="#" data-page="menu" class="nav-link text-gray-700 dark:text-gray-300 hover:text-primary transition">القائمة</a>
                <a href="#" data-page="about" class="nav-link text-gray-700 dark:text-gray-300 hover:text-primary transition">من نحن</a>
                <a href="#" data-page="contact" class="nav-link text-gray-700 dark:text-gray-300 hover:text-primary transition">اتصل بنا</a>
            </nav>

            <!-- قائمة المستخدم -->
            <div class="relative">
                <button id="userMenuBtn" class="flex items-center space-x-2 space-x-reverse text-gray-700 dark:text-gray-300 hover:text-primary transition">
                    <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm font-bold">
                        أ
                    </div>
                    <span>أحمد محمد</span>
                    <i class="fas fa-chevron-down text-xs"></i>
                </button>
                
                <!-- قائمة منسدلة -->
                <div id="userMenu" class="absolute left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 hidden">
                    <div class="p-3 border-b border-gray-200 dark:border-gray-700">
                        <p class="text-sm font-medium text-gray-800 dark:text-white">أحمد محمد</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400"><EMAIL></p>
                    </div>
                    <a href="#" data-page="dashboard" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-tachometer-alt ml-2"></i>لوحة التحكم
                    </a>
                    <a href="#" data-page="profile" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-user-circle ml-2"></i>الملف الشخصي
                    </a>
                    <a href="#" data-page="orders" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-clipboard-list ml-2"></i>طلباتي
                    </a>
                    <a href="#" data-page="reservations" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-calendar-check ml-2"></i>حجوزاتي
                    </a>
                    <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
                    <button class="block w-full text-right px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-sign-out-alt ml-2"></i>تسجيل الخروج
                    </button>
                </div>
            </div>
        </div>
    </div>
</header>

<!-- المحتوى الرئيسي -->
<main class="min-h-screen">
    <!-- الصفحة الرئيسية -->
    <div id="home-page" class="page active">
        <div class="container mx-auto px-4 py-8">
            <div class="text-center mb-12">
                <h1 class="text-4xl font-bold text-gray-800 dark:text-white mb-4">مرحباً بكم في Eat Hub</h1>
                <p class="text-xl text-gray-600 dark:text-gray-400">أشهى المأكولات في أجواء مميزة</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center">
                    <i class="fas fa-utensils text-primary text-4xl mb-4"></i>
                    <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-2">أطباق شهية</h3>
                    <p class="text-gray-600 dark:text-gray-400">تشكيلة واسعة من الأطباق المحضرة بعناية</p>
                </div>
                
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center">
                    <i class="fas fa-clock text-primary text-4xl mb-4"></i>
                    <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-2">خدمة سريعة</h3>
                    <p class="text-gray-600 dark:text-gray-400">نقدم خدمة سريعة وفعالة لراحتكم</p>
                </div>
                
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center">
                    <i class="fas fa-star text-primary text-4xl mb-4"></i>
                    <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-2">جودة عالية</h3>
                    <p class="text-gray-600 dark:text-gray-400">نستخدم أجود المكونات الطازجة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- صفحة القائمة -->
    <div id="menu-page" class="page">
        <div class="container mx-auto px-4 py-8">
            <h1 class="text-3xl font-bold text-gray-800 dark:text-white text-center mb-8">قائمة الطعام</h1>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                    <div class="h-48 bg-gray-300 dark:bg-gray-600"></div>
                    <div class="p-4">
                        <h3 class="font-bold text-lg text-gray-800 dark:text-white mb-2">برجر لحم أنجوس</h3>
                        <p class="text-gray-600 dark:text-gray-400 text-sm mb-4">برجر لحم بقري فاخر مع صلصة خاصة</p>
                        <div class="flex justify-between items-center">
                            <span class="font-bold text-primary">55 ر.س</span>
                            <button class="bg-primary hover:bg-primary/90 text-white py-2 px-4 rounded-lg text-sm">
                                إضافة للسلة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- صفحة من نحن -->
    <div id="about-page" class="page">
        <div class="container mx-auto px-4 py-8">
            <h1 class="text-3xl font-bold text-gray-800 dark:text-white text-center mb-8">من نحن</h1>
            <div class="max-w-4xl mx-auto">
                <p class="text-lg text-gray-600 dark:text-gray-400 text-center mb-8">
                    نحن أكثر من مجرد مطعم، نحن وجهة لتجربة طعام استثنائية تجمع بين النكهات الأصيلة والضيافة العربية الدافئة
                </p>
            </div>
        </div>
    </div>

    <!-- صفحة اتصل بنا -->
    <div id="contact-page" class="page">
        <div class="container mx-auto px-4 py-8">
            <h1 class="text-3xl font-bold text-gray-800 dark:text-white text-center mb-8">اتصل بنا</h1>
            <div class="max-w-2xl mx-auto">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <i class="fas fa-phone text-primary text-xl ml-4"></i>
                            <span class="text-gray-800 dark:text-white">+966 11 123 4567</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-envelope text-primary text-xl ml-4"></i>
                            <span class="text-gray-800 dark:text-white"><EMAIL></span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-map-marker-alt text-primary text-xl ml-4"></i>
                            <span class="text-gray-800 dark:text-white">شارع الملك فهد، الرياض</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- صفحة لوحة التحكم -->
    <div id="dashboard-page" class="page">
        <div class="container mx-auto px-4 py-8">
            <div class="mb-6">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                    <div class="p-6 bg-gradient-to-r from-primary/90 to-primary text-white">
                        <div class="flex items-center">
                            <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center text-2xl font-bold ml-4">
                                أ
                            </div>
                            <div>
                                <h2 class="text-2xl font-bold">مرحباً، أحمد محمد</h2>
                                <p class="opacity-90">نتمنى لك تجربة رائعة معنا</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center ml-4">
                            <i class="fas fa-shopping-bag text-blue-600 dark:text-blue-400 text-xl"></i>
                        </div>
                        <div>
                            <p class="text-2xl font-bold text-gray-800 dark:text-white">8</p>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">إجمالي الطلبات</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center ml-4">
                            <i class="fas fa-calendar-check text-green-600 dark:text-green-400 text-xl"></i>
                        </div>
                        <div>
                            <p class="text-2xl font-bold text-gray-800 dark:text-white">3</p>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">الحجوزات</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center ml-4">
                            <i class="fas fa-star text-yellow-600 dark:text-yellow-400 text-xl"></i>
                        </div>
                        <div>
                            <p class="text-2xl font-bold text-gray-800 dark:text-white">75</p>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">نقاط الولاء</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center ml-4">
                            <i class="fas fa-wallet text-purple-600 dark:text-purple-400 text-xl"></i>
                        </div>
                        <div>
                            <p class="text-2xl font-bold text-gray-800 dark:text-white">750.25</p>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">إجمالي الإنفاق (ر.س)</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- صفحة الملف الشخصي -->
    <div id="profile-page" class="page">
        <div class="container mx-auto px-4 py-8">
            <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-8">الملف الشخصي</h1>
            <div class="max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <div class="flex items-center mb-6">
                    <div class="w-20 h-20 bg-primary rounded-full flex items-center justify-center text-white text-2xl font-bold ml-4">
                        أ
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold text-gray-800 dark:text-white">أحمد محمد</h2>
                        <p class="text-gray-600 dark:text-gray-400"><EMAIL></p>
                    </div>
                </div>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاسم الكامل</label>
                        <input type="text" value="أحمد محمد" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البريد الإلكتروني</label>
                        <input type="email" value="<EMAIL>" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الهاتف</label>
                        <input type="tel" value="+966501234567" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white">
                    </div>
                    <button class="w-full bg-primary hover:bg-primary/90 text-white py-3 rounded-lg transition">
                        حفظ التغييرات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- صفحة طلباتي -->
    <div id="orders-page" class="page">
        <div class="container mx-auto px-4 py-8">
            <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-8">طلباتي</h1>
            <div class="space-y-6">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                    <div class="flex justify-between items-start mb-4">
                        <div>
                            <h3 class="text-lg font-bold text-gray-800 dark:text-white">طلب #1254</h3>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">15 ديسمبر 2024 - 2:30 م</p>
                        </div>
                        <div class="text-right">
                            <span class="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-full text-sm">مكتمل</span>
                            <p class="text-xl font-bold text-gray-800 dark:text-white mt-2">85.50 ر.س</p>
                        </div>
                    </div>
                    <div class="flex space-x-3 space-x-reverse">
                        <button class="text-primary hover:text-primary/80 text-sm">عرض التفاصيل</button>
                        <button class="text-primary hover:text-primary/80 text-sm">إعادة الطلب</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- صفحة حجوزاتي -->
    <div id="reservations-page" class="page">
        <div class="container mx-auto px-4 py-8">
            <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-8">حجوزاتي</h1>
            <div class="space-y-6">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                    <div class="flex justify-between items-start mb-4">
                        <div>
                            <h3 class="text-lg font-bold text-gray-800 dark:text-white">حجز #R1001</h3>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">19 ديسمبر 2024 - 7:00 م</p>
                        </div>
                        <div class="text-right">
                            <span class="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full text-sm">قادم</span>
                            <p class="text-lg font-bold text-gray-800 dark:text-white mt-2">طاولة #8</p>
                        </div>
                    </div>
                    <div class="flex space-x-3 space-x-reverse">
                        <button class="text-primary hover:text-primary/80 text-sm">تعديل الحجز</button>
                        <button class="text-red-600 hover:text-red-700 text-sm">إلغاء الحجز</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
// نظام التنقل بين الصفحات
document.addEventListener('DOMContentLoaded', function() {
    const navLinks = document.querySelectorAll('[data-page]');
    const pages = document.querySelectorAll('.page');
    const userMenuBtn = document.getElementById('userMenuBtn');
    const userMenu = document.getElementById('userMenu');

    // التنقل بين الصفحات
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetPage = this.getAttribute('data-page');
            
            // إخفاء جميع الصفحات
            pages.forEach(page => {
                page.classList.remove('active');
            });
            
            // إظهار الصفحة المطلوبة
            const targetElement = document.getElementById(targetPage + '-page');
            if (targetElement) {
                targetElement.classList.add('active');
            }
            
            // تحديث حالة الروابط
            navLinks.forEach(navLink => {
                navLink.classList.remove('active');
            });
            
            this.classList.add('active');
            
            // إغلاق قائمة المستخدم
            userMenu.classList.add('hidden');
        });
    });

    // قائمة المستخدم
    userMenuBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        userMenu.classList.toggle('hidden');
    });

    // إغلاق قائمة المستخدم عند النقر خارجها
    document.addEventListener('click', function() {
        userMenu.classList.add('hidden');
    });

    userMenu.addEventListener('click', function(e) {
        e.stopPropagation();
    });
});
</script>

</body>
</html>
