<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Review;
use App\Models\User;
use App\Models\Order;

class ReviewSeeder extends Seeder
{
    public function run()
    {
        // إنشاء مستخدمين تجريبيين إذا لم يكونوا موجودين
        $user1 = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'أحمد',
                'last_name' => 'محمد',
                'phone' => '0501234567',
                'password' => bcrypt('password'),
                'user_type' => 'customer',
                'is_active' => true
            ]
        );

        $user2 = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'سارة',
                'last_name' => 'أحمد',
                'phone' => '0507654321',
                'password' => bcrypt('password'),
                'user_type' => 'customer',
                'is_active' => true
            ]
        );

        // إنشاء طلبات تجريبية
        $order1 = Order::firstOrCreate([
            'user_id' => $user1->user_id,
            'total_amount' => 55.00,
            'status' => 'completed'
        ]);

        $order2 = Order::firstOrCreate([
            'user_id' => $user2->user_id,
            'total_amount' => 65.00,
            'status' => 'completed'
        ]);

        // إنشاء تقييمات
        $reviews = [
            [
                'user_id' => $user1->user_id,
                'order_id' => $order1->order_id,
                'rating' => 5,
                'comment' => 'تجربة رائعة! الطعام لذيذ والخدمة ممتازة.',
                'is_approved' => true
            ],
            [
                'user_id' => $user2->user_id,
                'order_id' => $order2->order_id,
                'rating' => 4,
                'comment' => 'أحببت الأجواء والديكور. الطعام كان شهياً.',
                'is_approved' => true
            ]
        ];

        foreach ($reviews as $review) {
            Review::firstOrCreate(
                [
                    'user_id' => $review['user_id'],
                    'order_id' => $review['order_id']
                ],
                $review
            );
        }
    }
}
