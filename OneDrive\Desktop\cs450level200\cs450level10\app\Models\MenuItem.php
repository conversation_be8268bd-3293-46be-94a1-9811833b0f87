<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MenuItem extends Model
{
    use HasFactory;

    protected $primaryKey = 'item_id';

    protected $fillable = [
        'name',
        'price',
        'category',
        'description',
        'is_available',
        'image_path'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'is_available' => 'boolean',
        'created_at' => 'datetime'
    ];

    public function recipes()
    {
        return $this->hasMany(Recipe::class, 'menu_item_id');
    }

    public function recipe()
    {
        return $this->hasMany(Recipe::class, 'menu_item_id');
    }

    public function orderItems()
    {
        return $this->hasMany(OrderItem::class, 'menu_item_id');
    }

    public function ingredients()
    {
        return $this->belongsToMany(Ingredient::class, 'recipes', 'menu_item_id', 'ingredient_id')
            ->withPivot('quantity');
    }

    public function reviews()
    {
        return $this->hasMany(Review::class, 'menu_item_id', 'item_id');
    }
    public function getImageUrlAttribute()
    {
        if (!$this->image_path) {
            return asset('images/default-food.png');
        }

        return asset('storage/' . $this->image_path);
    }
}