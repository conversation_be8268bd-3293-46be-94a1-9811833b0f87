<?php

namespace App\Http\Controllers;

use App\Models\Ingredient;
use App\Models\Inventory;
use App\Models\InventoryTransaction;
use App\Models\Notification;
use App\Models\Expense;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Routing\Controller;

class InventoryController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('admin');
    }

    public function index(Request $request)
    {
        $query = Ingredient::query();

        // معالجة البحث
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = $request->search;
            $query->where('name', 'like', '%' . $searchTerm . '%');
        }

        // معالجة الفلاتر
        if ($request->has('status') && $request->status !== '') {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        if ($request->has('stock_level') && $request->stock_level !== '') {
            if ($request->stock_level === 'low') {
                $query->whereHas('inventory', function($q) {
                    $q->havingRaw('SUM(quantity) < 10');
                });
            } elseif ($request->stock_level === 'out') {
                $query->whereDoesntHave('inventory');
            }
        }

        $ingredients = $query->with(['inventory' => function($query) {
                $query->orderBy('expiry_date');
            }])
            ->orderBy('name')
            ->paginate(15)
            ->appends($request->query());

        // حساب الإحصائيات
        $totalIngredients = Ingredient::count();
        $activeIngredients = Ingredient::where('is_active', true)->count();
        $inactiveIngredients = Ingredient::where('is_active', false)->count();

        // المخزون المنخفض (أقل من 10 وحدات)
        $lowStockCount = Ingredient::whereHas('inventory', function($query) {
            $query->select('ingredient_id')
                  ->groupBy('ingredient_id')
                  ->havingRaw('SUM(quantity) < 10');
        })->count();

        // المكونات بدون مخزون
        $outOfStockCount = Ingredient::whereDoesntHave('inventory')->count();

        // المكونات قريبة الانتهاء (خلال 30 يوم)
        $nearExpiryCount = Inventory::where('expiry_date', '<=', now()->addDays(30))
                                  ->where('expiry_date', '>', now())
                                  ->distinct('ingredient_id')
                                  ->count();

        // إجمالي قيمة المخزون
        $totalInventoryValue = Inventory::join('ingredients', 'inventory.ingredient_id', '=', 'ingredients.ingredient_id')
                                      ->sum(DB::raw('inventory.quantity * inventory.cost_per_unit'));

        // إنشاء تنبيهات للمخزون المنخفض والمنتهي الصلاحية
        $this->createInventoryAlerts();

        return view('admin.inventory.index', compact(
            'ingredients',
            'totalIngredients',
            'activeIngredients',
            'inactiveIngredients',
            'lowStockCount',
            'outOfStockCount',
            'nearExpiryCount',
            'totalInventoryValue'
        ));
    }

    /**
     * إنشاء تنبيهات للمخزون المنخفض والمنتهي الصلاحية
     */
    private function createInventoryAlerts()
    {
        // التحقق من أن المستخدم مسجل دخول
        if (!Auth::check()) {
            return;
        }

        // تنبيهات المخزون المنخفض
        $lowStockIngredients = Ingredient::whereHas('inventory', function($query) {
            $query->select('ingredient_id')
                  ->groupBy('ingredient_id')
                  ->havingRaw('SUM(quantity) < 10');
        })->get();

        foreach ($lowStockIngredients as $ingredient) {
            $existingAlert = Notification::where('type', 'low_stock')
                                       ->where('data->ingredient_id', $ingredient->ingredient_id)
                                       ->where('created_at', '>=', now()->subDays(1))
                                       ->first();

            if (!$existingAlert) {
                Notification::create([
                    'user_id' => Auth::id(),
                    'title' => 'تنبيه مخزون منخفض',
                    'type' => 'low_stock',
                    'message' => "المخزون منخفض للمكون: {$ingredient->name}",
                    'data' => json_encode([
                        'ingredient_id' => $ingredient->ingredient_id,
                        'ingredient_name' => $ingredient->name,
                        'current_stock' => $ingredient->inventory->sum('quantity')
                    ])
                ]);
            }
        }

        // تنبيهات المنتهي الصلاحية
        $expiredItems = Inventory::where('expiry_date', '<=', now()->addDays(7))
                               ->where('expiry_date', '>', now())
                               ->with('ingredient')
                               ->get();

        foreach ($expiredItems as $item) {
            $existingAlert = Notification::where('type', 'expiry_warning')
                                       ->where('data->inventory_id', $item->inventory_id)
                                       ->where('created_at', '>=', now()->subDays(1))
                                       ->first();

            if (!$existingAlert) {
                Notification::create([
                    'user_id' => Auth::id(),
                    'title' => 'تنبيه انتهاء صلاحية',
                    'type' => 'expiry_warning',
                    'message' => "ينتهي صلاحية {$item->ingredient->name} في {$item->expiry_date->format('Y-m-d')}",
                    'data' => json_encode([
                        'inventory_id' => $item->inventory_id,
                        'ingredient_name' => $item->ingredient->name,
                        'expiry_date' => $item->expiry_date->format('Y-m-d')
                    ])
                ]);
            }
        }
    }

    /**
     * تصدير بيانات المخزون إلى CSV
     */
    public function export()
    {
        $ingredients = Ingredient::with(['inventory' => function($query) {
            $query->orderBy('expiry_date');
        }])->orderBy('name')->get();

        $filename = 'inventory_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($ingredients) {
            $file = fopen('php://output', 'w');

            // إضافة BOM للدعم العربي
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            // العناوين
            fputcsv($file, [
                'المكون',
                'الوحدة',
                'الكمية الإجمالية',
                'أقرب تاريخ انتهاء',
                'الحالة',
                'قيمة المخزون'
            ]);

            foreach ($ingredients as $ingredient) {
                $totalQuantity = $ingredient->inventory->sum('quantity');
                $totalValue = $ingredient->inventory->sum(function($inv) {
                    return $inv->quantity * $inv->cost_per_unit;
                });

                $nearestExpiry = $ingredient->inventory->isNotEmpty() && $ingredient->inventory->first()->expiry_date
                    ? $ingredient->inventory->sortBy('expiry_date')->first()->expiry_date->format('Y-m-d')
                    : '-';

                $status = $ingredient->is_active ? 'نشط' : 'غير نشط';

                fputcsv($file, [
                    $ingredient->name,
                    $ingredient->unit,
                    number_format($totalQuantity, 2),
                    $nearestExpiry,
                    $status,
                    number_format($totalValue, 2) . ' د.ل'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    public function create()
    {
        $ingredients = Ingredient::where('is_active', true)->orderBy('name')->get();

        return view('admin.inventory.create', compact('ingredients'));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ingredient_id' => 'required|exists:ingredients,ingredient_id',
            'quantity' => 'required|numeric|min:0.01|max:999999',
            'cost_per_unit' => 'required|numeric|min:0.01|max:999999',
            'expiry_date' => 'nullable|date|after:today',
        ], [
            'ingredient_id.required' => 'يجب اختيار المكون',
            'ingredient_id.exists' => 'المكون المحدد غير موجود',
            'quantity.required' => 'يجب إدخال الكمية',
            'quantity.numeric' => 'الكمية يجب أن تكون رقم',
            'quantity.min' => 'الكمية يجب أن تكون أكبر من 0',
            'quantity.max' => 'الكمية كبيرة جداً',
            'cost_per_unit.required' => 'يجب إدخال تكلفة الوحدة',
            'cost_per_unit.numeric' => 'تكلفة الوحدة يجب أن تكون رقم',
            'cost_per_unit.min' => 'تكلفة الوحدة يجب أن تكون أكبر من 0',
            'cost_per_unit.max' => 'تكلفة الوحدة كبيرة جداً',
            'expiry_date.date' => 'تاريخ الانتهاء غير صحيح',
            'expiry_date.after' => 'تاريخ الانتهاء يجب أن يكون في المستقبل',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        DB::beginTransaction();

        try {
            // Create inventory entry
            $inventory = Inventory::create([
                'ingredient_id' => $request->ingredient_id,
                'quantity' => $request->quantity,
                'cost_per_unit' => $request->cost_per_unit,
                'expiry_date' => $request->expiry_date,
            ]);

            // Record transaction
            $transaction = InventoryTransaction::create([
                'inventory_id' => $inventory->inventory_id,
                'user_id' => Auth::id(),
                'transaction_type' => 'purchase',
                'quantity' => $request->quantity,
            ]);

            // إنشاء مصروف تلقائي لشراء المخزون
            $this->createPurchaseExpense($inventory, $request->quantity, $transaction->transaction_id);

            DB::commit();

            // الحصول على اسم المكون للرسالة
            $ingredient = Ingredient::find($request->ingredient_id);
            $totalCost = $request->quantity * $request->cost_per_unit;

            return redirect()->route('admin.inventory')->with('success',
                "تم إضافة {$request->quantity} {$ingredient->unit} من {$ingredient->name} بتكلفة إجمالية {$totalCost} د.ل"
            );
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'حدث خطأ أثناء إضافة المخزون: ' . $e->getMessage())->withInput();
        }
    }

    public function edit($id)
    {
        $inventory = Inventory::with('ingredient')->findOrFail($id);
        $ingredients = Ingredient::where('is_active', true)->orderBy('name')->get();

        return view('admin.inventory.edit', compact('inventory', 'ingredients'));
    }

    public function update(Request $request, $id)
    {
        $inventory = Inventory::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'ingredient_id' => 'required|exists:ingredients,ingredient_id',
            'quantity' => 'required|numeric|min:0',
            'cost_per_unit' => 'required|numeric|min:0.01',
            'expiry_date' => 'nullable|date',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $oldQuantity = $inventory->quantity;
        $newQuantity = $request->quantity;

        DB::beginTransaction();

        try {
            // Update inventory
            $inventory->update([
                'ingredient_id' => $request->ingredient_id,
                'quantity' => $newQuantity,
                'cost_per_unit' => $request->cost_per_unit,
                'expiry_date' => $request->expiry_date,
            ]);

            // Record transaction if quantity changed
            if ($oldQuantity != $newQuantity) {
                $transactionType = $newQuantity > $oldQuantity ? 'purchase' : 'consumption';
                $transactionQuantity = abs($newQuantity - $oldQuantity);

                $transaction = InventoryTransaction::create([
                    'inventory_id' => $inventory->inventory_id,
                    'user_id' => Auth::id(),
                    'transaction_type' => $transactionType,
                    'quantity' => $transactionQuantity,
                ]);

                // إنشاء مصروف تلقائي إذا كانت زيادة في الكمية (شراء)
                if ($transactionType === 'purchase') {
                    $this->createPurchaseExpense($inventory, $transactionQuantity, $transaction->transaction_id);
                }
            }

            DB::commit();

            return redirect()->route('admin.inventory')->with('success', 'تم تحديث المخزون بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'حدث خطأ أثناء تحديث المخزون: ' . $e->getMessage())->withInput();
        }
    }

    public function delete($id)
    {
        $inventory = Inventory::findOrFail($id);

        // Check if the inventory is used in any transactions
        $hasTransactions = InventoryTransaction::where('inventory_id', $id)->exists();

        if ($hasTransactions) {
            return redirect()->back()->with('error', 'لا يمكن حذف هذا المخزون لأنه مرتبط بمعاملات');
        }

        $inventory->delete();

        return redirect()->route('admin.inventory')->with('success', 'تم حذف المخزون بنجاح');
    }

    // إضافة طرق إضافية للتعامل مع المكونات
    public function createIngredient()
    {
        return view('admin.inventory.create_ingredient');
    }

    public function storeIngredient(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100|unique:ingredients,name',
            'unit' => 'required|string|max:20',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        Ingredient::create([
            'name' => $request->name,
            'unit' => $request->unit,
            'is_active' => $request->has('is_active'),
        ]);

        return redirect()->route('admin.inventory')->with('success', 'تم إضافة المكون بنجاح');
    }

    public function editIngredient($id)
    {
        $ingredient = Ingredient::findOrFail($id);

        return view('admin.inventory.edit_ingredient', compact('ingredient'));
    }

    public function updateIngredient(Request $request, $id)
    {
        $ingredient = Ingredient::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100|unique:ingredients,name,' . $id . ',ingredient_id',
            'unit' => 'required|string|max:20',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $ingredient->update([
            'name' => $request->name,
            'unit' => $request->unit,
            'is_active' => $request->has('is_active'),
        ]);

        return redirect()->route('admin.inventory')->with('success', 'تم تحديث المكون بنجاح');
    }

    public function deleteIngredient($id)
    {
        $ingredient = Ingredient::findOrFail($id);

        // Check if the ingredient is used in any recipes
        $hasRecipes = DB::table('recipes')->where('ingredient_id', $id)->exists();

        if ($hasRecipes) {
            return redirect()->back()->with('error', 'لا يمكن حذف هذا المكون لأنه مستخدم في وصفات');
        }

        // Check if the ingredient has any inventory
        $hasInventory = Inventory::where('ingredient_id', $id)->exists();

        if ($hasInventory) {
            return redirect()->back()->with('error', 'لا يمكن حذف هذا المكون لأنه موجود في المخزون');
        }

        $ingredient->delete();

        return redirect()->route('admin.inventory')->with('success', 'تم حذف المكون بنجاح');
    }

    // عرض صفحة حركات المخزون
    public function transactions()
    {
        $transactions = InventoryTransaction::with(['inventory.ingredient', 'user'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view('admin.inventory.transactions', compact('transactions'));
    }

    // عرض تقرير المخزون منخفض المستوى
    public function lowStock()
    {
        $lowStockItems = DB::table('inventory')
            ->join('ingredients', 'inventory.ingredient_id', '=', 'ingredients.ingredient_id')
            ->select('ingredients.name', 'ingredients.unit', DB::raw('SUM(inventory.quantity) as total_quantity'))
            ->groupBy('ingredients.name', 'ingredients.unit')
            ->having('total_quantity', '<', 10) // قيمة الحد الأدنى
            ->get();

        // إنشاء إشعارات للمسؤولين حول المكونات منخفضة المخزون
        $this->createLowStockNotifications($lowStockItems);

        return view('admin.inventory.low_stock', compact('lowStockItems'));
    }

    private function createLowStockNotifications($lowStockItems)
    {
        // إيجاد المستخدمين المسؤولين
        $admins = DB::table('users')
            ->where('user_type', 'admin')
            ->where('is_active', true)
            ->get();

        foreach ($lowStockItems as $item) {
            $message = 'المكون ' . $item->name . ' أصبح منخفض المخزون (' . $item->total_quantity . ' ' . $item->unit . ')';

            foreach ($admins as $admin) {
                // تحقق مما إذا كان هناك إشعار مماثل في اليوم نفسه
                $existingNotification = Notification::where('user_id', $admin->user_id)
                    ->where('message', $message)
                    ->whereDate('created_at', today())
                    ->exists();

                if (!$existingNotification) {
                    Notification::create([
                        'user_id' => $admin->user_id,
                        'message' => $message,
                        'is_read' => false,
                    ]);
                }
            }
        }
    }

    /**
     * إنشاء مصروف تلقائي عند شراء مخزون جديد
     */
    private function createPurchaseExpense($inventory, $quantity, $transactionId)
    {
        try {
            // الحصول على معلومات المكون
            $ingredient = Ingredient::find($inventory->ingredient_id);

            if (!$ingredient) {
                return;
            }

            // حساب التكلفة الإجمالية
            $totalCost = $inventory->cost_per_unit * $quantity;

            // إنشاء وصف المصروف
            $description = "شراء مخزون: {$ingredient->name} ({$quantity} {$ingredient->unit})";

            // إنشاء المصروف التلقائي
            Expense::create([
                'amount' => $totalCost,
                'category' => 'ingredients',
                'description' => $description,
                'expense_date' => now(),
                'recorded_by' => Auth::id(),
                'payment_method' => 'purchase',
                'is_automatic' => true,
                'inventory_transaction_id' => $transactionId
            ]);

        } catch (\Exception $e) {
            // تسجيل الخطأ ولكن لا نوقف العملية
            Log::warning('خطأ في إنشاء مصروف تلقائي لشراء المخزون:', [
                'inventory_id' => $inventory->inventory_id,
                'quantity' => $quantity,
                'error' => $e->getMessage()
            ]);
        }
    }
}