@extends('employee.layouts.app')

@section('title', 'تفاصيل الحجز - نظام إدارة المطعم')

@section('page-title', 'تفاصيل الحجز')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="mb-6 flex flex-wrap items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800 dark:text-white">تفاصيل الحجز</h1>
            <p class="text-gray-600 dark:text-gray-400">عرض تفاصيل الحجز رقم {{ $reservation->reservation_id }}</p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-2 space-x-reverse">
            <a href="{{ route('employee.reservations.edit', $reservation->reservation_id) }}" class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-50">
                <i class="fas fa-edit ml-2"></i>
                تعديل الحجز
            </a>
            <a href="{{ route('employee.reservations') }}" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
                <i class="fas fa-arrow-right ml-2"></i>
                العودة للحجوزات
            </a>
        </div>
    </div>

    @if(session('success'))
    <div class="bg-green-100 border-r-4 border-green-500 text-green-700 p-4 mb-6 rounded-md dark:bg-green-900/30 dark:text-green-500 dark:border-green-500">
        <p>{{ session('success') }}</p>
    </div>
    @endif

    @if(session('error'))
    <div class="bg-red-100 border-r-4 border-red-500 text-red-700 p-4 mb-6 rounded-md dark:bg-red-900/30 dark:text-red-500 dark:border-red-500">
        <p>{{ session('error') }}</p>
    </div>
    @endif

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- معلومات الحجز الأساسية -->
        <div class="md:col-span-2">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-bold text-gray-800 dark:text-white">معلومات الحجز</h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">رقم الحجز</h3>
                            <p class="text-base text-gray-900 dark:text-white">{{ $reservation->reservation_id }}</p>
                        </div>

                        <div>
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">حالة الحجز</h3>
                            <div>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                    {{ $reservation->status == 'confirmed' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                                       ($reservation->status == 'canceled' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' :
                                       'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300') }}">
                                    {{ $reservation->status == 'confirmed' ? 'مؤكد' :
                                       ($reservation->status == 'canceled' ? 'ملغي' :
                                       ($reservation->status == 'completed' ? 'مكتمل' : $reservation->status)) }}
                                </span>
                            </div>
                        </div>

                        <div>
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">وقت الحجز</h3>
                            <p class="text-base text-gray-900 dark:text-white">{{ \Carbon\Carbon::parse($reservation->reservation_time)->format('Y-m-d H:i') }}</p>
                        </div>

                        <div>
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">مدة الحجز</h3>
                            <p class="text-base text-gray-900 dark:text-white">{{ $reservation->duration }} دقيقة</p>
                        </div>

                        <div>
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">تاريخ الإنشاء</h3>
                            <p class="text-base text-gray-900 dark:text-white">{{ $reservation->created_at->format('Y-m-d H:i') }}</p>
                        </div>

                        <div>
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">آخر تحديث</h3>
                            <p class="text-base text-gray-900 dark:text-white">{{ $reservation->updated_at->format('Y-m-d H:i') }}</p>
                        </div>

                        @if($reservation->notes)
                        <div class="md:col-span-2">
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">ملاحظات</h3>
                            <p class="text-base text-gray-900 dark:text-white">{{ $reservation->notes }}</p>
                        </div>
                        @endif
                    </div>

                    @if($reservation->status == 'confirmed')
                    <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">تغيير حالة الحجز</h3>
                        <div class="flex space-x-2 space-x-reverse">
                            <form action="{{ route('employee.reservations.update-status', $reservation->reservation_id) }}" method="POST" class="inline-block">
                                @csrf
                                @method('PUT')
                                <input type="hidden" name="status" value="completed">
                                <button type="submit" class="inline-flex items-center px-3 py-1.5 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50">
                                    <i class="fas fa-check ml-1"></i>
                                    إكمال الحجز
                                </button>
                            </form>

                            <form action="{{ route('employee.reservations.update-status', $reservation->reservation_id) }}" method="POST" class="inline-block">
                                @csrf
                                @method('PUT')
                                <input type="hidden" name="status" value="canceled">
                                <button type="submit" class="inline-flex items-center px-3 py-1.5 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50">
                                    <i class="fas fa-times ml-1"></i>
                                    إلغاء الحجز
                                </button>
                            </form>
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            @if($reservation->status == 'confirmed')
            <div class="mt-6 bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-bold text-gray-800 dark:text-white">إجراءات سريعة</h2>
                </div>
                <div class="p-6 space-y-4">
                    <div class="flex flex-wrap gap-4">
                        <a href="#" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                            <i class="fas fa-print ml-2"></i>
                            طباعة تفاصيل الحجز
                        </a>

                        <a href="#" class="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50">
                            <i class="fas fa-bell ml-2"></i>
                            إرسال تذكير للعميل
                        </a>
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- معلومات العميل والطاولة -->
        <div class="md:col-span-1">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
                <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-bold text-gray-800 dark:text-white">معلومات العميل</h2>
                </div>
                <div class="p-6">
                    @if(isset($reservation->user))
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center overflow-hidden mr-4">
                            @if($reservation->user->profile_image)
                            <img src="{{ asset('storage/' . $reservation->user->profile_image) }}" alt="{{ $reservation->user->first_name }}" class="w-full h-full object-cover">
                            @else
                            <div class="text-2xl text-gray-400 dark:text-gray-500">
                                <i class="fas fa-user"></i>
                            </div>
                            @endif
                        </div>
                        <div>
                            <h3 class="text-base font-medium text-gray-900 dark:text-white">{{ $reservation->user->first_name }} {{ $reservation->user->last_name }}</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $reservation->user->user_type == 'customer' ? 'عميل' : $reservation->user->user_type }}</p>
                        </div>
                    </div>

                    <div class="space-y-3">
                        <div class="flex items-center">
                            <i class="fas fa-envelope text-gray-400 dark:text-gray-500 w-5"></i>
                            <span class="text-sm text-gray-700 dark:text-gray-300 mr-2">{{ $reservation->user->email }}</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-phone text-gray-400 dark:text-gray-500 w-5"></i>
                            <span class="text-sm text-gray-700 dark:text-gray-300 mr-2">{{ $reservation->user->phone }}</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-calendar text-gray-400 dark:text-gray-500 w-5"></i>
                            <span class="text-sm text-gray-700 dark:text-gray-300 mr-2">عضو منذ {{ $reservation->user->created_at->format('Y-m-d') }}</span>
                        </div>
                    </div>
                    @else
                    <div class="text-center py-4">
                        <div class="text-3xl text-gray-300 dark:text-gray-600 mb-2">
                            <i class="fas fa-user-slash"></i>
                        </div>
                        <p class="text-gray-500 dark:text-gray-400">معلومات العميل غير متوفرة</p>
                    </div>
                    @endif
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-bold text-gray-800 dark:text-white">معلومات الطاولة</h2>
                </div>
                <div class="p-6">
                    @if(isset($reservation->table))
                    <div class="text-center mb-4">
                        <div class="w-20 h-20 mx-auto bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-2">
                            <span class="text-2xl font-bold text-primary">{{ $reservation->table->table_number }}</span>
                        </div>
                        <h3 class="text-base font-medium text-gray-900 dark:text-white">طاولة رقم {{ $reservation->table->table_number }}</h3>
                    </div>

                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-500 dark:text-gray-400">السعة:</span>
                            <span class="text-sm text-gray-700 dark:text-gray-300">{{ $reservation->table->capacity }} أشخاص</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-500 dark:text-gray-400">الموقع:</span>
                            <span class="text-sm text-gray-700 dark:text-gray-300">{{ $reservation->table->location ?? 'الصالة الرئيسية' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-500 dark:text-gray-400">الحالة الحالية:</span>
                            <span class="text-sm">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                    {{ $reservation->table->status == 'available' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                                       ($reservation->table->status == 'occupied' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' :
                                       ($reservation->table->status == 'reserved' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                                       'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300')) }}">
                                    {{ $reservation->table->status == 'available' ? 'متاحة' :
                                       ($reservation->table->status == 'occupied' ? 'مشغولة' :
                                       ($reservation->table->status == 'reserved' ? 'محجوزة' : 'غير متاحة')) }}
                                </span>
                            </span>
                        </div>
                    </div>

                    <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                        <a href="{{ route('employee.tables.show', $reservation->table->table_id) }}" class="inline-flex items-center text-sm text-primary hover:text-primary-dark">
                            <i class="fas fa-external-link-alt ml-1"></i>
                            عرض تفاصيل الطاولة
                        </a>
                    </div>
                    @else
                    <div class="text-center py-4">
                        <div class="text-3xl text-gray-300 dark:text-gray-600 mb-2">
                            <i class="fas fa-chair"></i>
                        </div>
                        <p class="text-gray-500 dark:text-gray-400">معلومات الطاولة غير متوفرة</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
