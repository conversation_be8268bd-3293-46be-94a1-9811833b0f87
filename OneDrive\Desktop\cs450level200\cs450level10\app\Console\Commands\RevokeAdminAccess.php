<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;

class RevokeAdminAccess extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:revoke-admin-access {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'إزالة صلاحية الوصول للوحة الإدارة من موظف';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');

        // البحث عن المستخدم
        $user = User::where('email', $email)->first();

        if (!$user) {
            $this->error("المستخدم بالإيميل {$email} غير موجود");
            return 1;
        }

        if ($user->user_type === 'admin') {
            $this->error("لا يمكن إزالة صلاحيات المدير");
            return 1;
        }

        // إزالة صلاحية الوصول للوحة الإدارة
        $user->revokePermissionTo('dashboard.admin');

        // إزالة جميع الصلاحيات الإدارية
        $adminPermissions = [
            'users.view', 'users.create', 'users.edit', 'users.delete', 'users.permissions',
            'menu.view', 'menu.create', 'menu.edit', 'menu.delete',
            'orders.view', 'orders.create', 'orders.edit', 'orders.delete', 'orders.status',
            'reservations.view', 'reservations.create', 'reservations.edit', 'reservations.delete', 'reservations.status',
            'inventory.view', 'inventory.create', 'inventory.edit', 'inventory.delete', 'inventory.export',
            'ingredients.view', 'ingredients.create', 'ingredients.edit', 'ingredients.delete',
            'expenses.view', 'expenses.create', 'expenses.edit', 'expenses.delete',
            'reports.view', 'reports.financial', 'reports.sales', 'reports.inventory', 'reports.export',
            'tables.view', 'tables.create', 'tables.edit', 'tables.delete', 'tables.status',
            'payments.view', 'payments.create', 'payments.edit',
            'notifications.view', 'notifications.create', 'notifications.send', 'notifications.delete',
            'settings.view', 'settings.edit'
        ];

        foreach ($adminPermissions as $permission) {
            if ($user->hasPermissionTo($permission)) {
                $user->revokePermissionTo($permission);
            }
        }

        // الاحتفاظ بالصلاحيات الأساسية للموظف
        $employeePermissions = [
            'orders.view', 'orders.create', 'orders.status',
            'reservations.view', 'reservations.create', 'reservations.status',
            'tables.view', 'tables.status',
            'payments.view', 'payments.create',
            'dashboard.employee'
        ];

        foreach ($employeePermissions as $permission) {
            $user->givePermissionTo($permission);
        }

        $this->info("✅ تم إزالة صلاحية الوصول للوحة الإدارة بنجاح");
        $this->line("الموظف: {$user->first_name} {$user->last_name} ({$user->email})");
        $this->line("تم الاحتفاظ بالصلاحيات الأساسية للموظف");

        return 0;
    }
}
