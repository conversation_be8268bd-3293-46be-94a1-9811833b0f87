@include('customer.partials.head')

<body class="bg-gray-50 dark:bg-gray-900">

@include('customer.partials.header')

<!-- المحتوى الرئيسي -->
<main class="min-h-screen pt-20">
    <div class="container mx-auto px-4 py-8">
        <!-- عنوان الصفحة -->
        <div class="flex items-center mb-8">
            <a href="{{ route('customer.cart') }}"
               class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 p-2 rounded-lg transition ml-4">
                <i class="fas fa-arrow-right"></i>
            </a>
            <div>
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-2">إتمام الطلب</h1>
                <p class="text-gray-600 dark:text-gray-400">أكمل بياناتك لإتمام عملية الشراء</p>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- نموذج الدفع -->
            <div class="lg:col-span-2">
                <form id="checkoutForm" action="{{ route('customer.orders.store') }}" method="POST">
                    @csrf

                    <!-- نوع الطلب -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-6">
                        <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-6">
                            <i class="fas fa-truck text-primary ml-2"></i>
                            نوع الطلب
                        </h2>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                            <label class="flex items-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 order-type-option">
                                <input type="radio" name="order_type" value="delivery" class="ml-3" {{ ($orderType ?? 'delivery') === 'delivery' ? 'checked' : '' }}>
                                <div class="flex items-center">
                                    <i class="fas fa-truck text-primary text-xl ml-3"></i>
                                    <div>
                                        <div class="font-medium text-gray-800 dark:text-white">توصيل</div>
                                        <div class="text-sm text-gray-600 dark:text-gray-400">رسوم التوصيل: 10 د.ل</div>
                                    </div>
                                </div>
                            </label>

                            <label class="flex items-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 order-type-option">
                                <input type="radio" name="order_type" value="pickup" class="ml-3" {{ ($orderType ?? 'delivery') === 'pickup' ? 'checked' : '' }}>
                                <div class="flex items-center">
                                    <i class="fas fa-store text-green-500 text-xl ml-3"></i>
                                    <div>
                                        <div class="font-medium text-gray-800 dark:text-white">استلام من المطعم</div>
                                        <div class="text-sm text-gray-600 dark:text-gray-400">بدون رسوم إضافية</div>
                                    </div>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- معلومات التوصيل -->
                    <div id="deliveryInfo" class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-6">
                        <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-6">
                            <i class="fas fa-map-marker-alt text-primary ml-2"></i>
                            معلومات التوصيل
                        </h2>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاسم الأول</label>
                                <input type="text" name="first_name" value="{{ auth()->user()->first_name }}"
                                       class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاسم الأخير</label>
                                <input type="text" name="last_name" value="{{ auth()->user()->last_name }}"
                                       class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white" required>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الهاتف</label>
                                <input type="tel" name="phone" value="{{ auth()->user()->phone }}"
                                       class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البريد الإلكتروني</label>
                                <input type="email" name="email" value="{{ auth()->user()->email }}"
                                       class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white" required>
                            </div>
                        </div>

                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">العنوان</label>
                            <textarea name="address" rows="3"
                                      class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                      placeholder="أدخل عنوان التوصيل بالتفصيل..." required></textarea>
                        </div>
                    </div>

                    <!-- طريقة الدفع -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-6">
                        <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-6">
                            <i class="fas fa-credit-card text-primary ml-2"></i>
                            طريقة الدفع
                        </h2>

                        <div class="space-y-4">
                            <!-- الدفع نقداً - متاح دائماً -->
                            <label class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                                <input type="radio" name="payment_method" value="cash" class="ml-3" checked>
                                <div class="flex items-center">
                                    <i class="fas fa-money-bill-wave text-green-500 text-xl ml-3"></i>
                                    <div>
                                        <div class="font-medium text-gray-800 dark:text-white">
                                            <span class="delivery-text">الدفع عند التوصيل</span>
                                            <span class="pickup-text" style="display: none;">الدفع في المطعم</span>
                                        </div>
                                        <div class="text-sm text-gray-600 dark:text-gray-400">
                                            <span class="delivery-text">ادفع نقداً عند استلام الطلب</span>
                                            <span class="pickup-text" style="display: none;">ادفع نقداً في المطعم</span>
                                        </div>
                                    </div>
                                </div>
                            </label>

                            <!-- الدفع الإلكتروني - للتوصيل فقط -->
                            <div class="electronic-payment-options">
                                <label class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <input type="radio" name="payment_method" value="card" class="ml-3">
                                    <div class="flex items-center">
                                        <i class="fas fa-credit-card text-blue-500 text-xl ml-3"></i>
                                        <div>
                                            <div class="font-medium text-gray-800 dark:text-white">بطاقة ائتمان</div>
                                            <div class="text-sm text-gray-600 dark:text-gray-400">ادفع بالبطاقة الائتمانية</div>
                                        </div>
                                    </div>
                                </label>

                                <label class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <input type="radio" name="payment_method" value="wallet" class="ml-3">
                                    <div class="flex items-center">
                                        <i class="fas fa-wallet text-purple-500 text-xl ml-3"></i>
                                        <div>
                                            <div class="font-medium text-gray-800 dark:text-white">محفظة إلكترونية</div>
                                            <div class="text-sm text-gray-600 dark:text-gray-400">ادفع عبر المحفظة الإلكترونية</div>
                                        </div>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- ملاحظات إضافية -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
                        <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-6">
                            <i class="fas fa-sticky-note text-primary ml-2"></i>
                            ملاحظات إضافية
                        </h2>

                        <textarea name="notes" rows="4"
                                  class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                  placeholder="أي ملاحظات خاصة للطلب..."></textarea>
                    </div>
                </form>
            </div>

            <!-- ملخص الطلب -->
            <div class="lg:col-span-1">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 sticky top-24">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-6">ملخص الطلب</h2>

                    <!-- عناصر الطلب -->
                    <div class="space-y-3 mb-6 max-h-60 overflow-y-auto">
                        @foreach($cartItems as $item)
                        <div class="flex items-center justify-between text-sm">
                            <div class="flex items-center">
                                <span class="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs ml-2">{{ $item->quantity }}</span>
                                <span class="text-gray-800 dark:text-white">{{ $item->menuItem->name }}</span>
                            </div>
                            <span class="font-medium text-gray-800 dark:text-white">{{ number_format($item->subtotal, 2) }} د.ل</span>
                        </div>
                        @endforeach
                    </div>

                    <!-- تفاصيل التكلفة -->
                    <div class="space-y-3 mb-6 border-t border-gray-200 dark:border-gray-700 pt-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600 dark:text-gray-400">المجموع الفرعي:</span>
                            <span class="font-medium text-gray-800 dark:text-white">{{ number_format($total, 2) }} د.ل</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600 dark:text-gray-400">الضريبة (15%):</span>
                            <span class="font-medium text-gray-800 dark:text-white">{{ number_format($tax, 2) }} د.ل</span>
                        </div>
                        <div class="flex justify-between text-sm" id="deliveryFeeRow">
                            <span class="text-gray-600 dark:text-gray-400">رسوم التوصيل:</span>
                            <span class="font-medium text-gray-800 dark:text-white" id="deliveryFeeAmount">{{ number_format($delivery, 2) }} د.ل</span>
                        </div>
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-3">
                            <div class="flex justify-between">
                                <span class="text-lg font-semibold text-gray-800 dark:text-white">المجموع الكلي:</span>
                                <span class="text-lg font-bold text-primary" id="finalTotalAmount">{{ number_format($finalTotal, 2) }} د.ل</span>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="space-y-3">
                        <button type="submit" form="checkoutForm"
                                class="w-full bg-primary hover:bg-primary/90 text-white font-bold py-3 px-6 rounded-lg transition">
                            <i class="fas fa-check ml-2"></i>
                            تأكيد الطلب
                        </button>
                        <a href="{{ route('customer.cart') }}"
                           class="w-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-3 px-6 rounded-lg transition text-center block">
                            <i class="fas fa-arrow-right ml-2"></i>
                            العودة للسلة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

@include('customer.partials.footer')

<script>
document.addEventListener('DOMContentLoaded', function() {
    // التحكم في نوع الطلب
    const orderTypeRadios = document.querySelectorAll('input[name="order_type"]');
    const deliveryInfo = document.getElementById('deliveryInfo');
    const deliveryFeeRow = document.getElementById('deliveryFeeRow');
    const deliveryFeeAmount = document.getElementById('deliveryFeeAmount');
    const finalTotalAmount = document.getElementById('finalTotalAmount');

    // القيم الأساسية
    const subtotal = parseFloat('{{ $total }}');
    const tax = parseFloat('{{ $tax }}');
    const deliveryFee = parseFloat('{{ $delivery }}');

    orderTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'pickup') {
                // استلام من المطعم - إخفاء معلومات التوصيل وإلغاء رسوم التوصيل
                deliveryInfo.style.display = 'none';
                deliveryFeeAmount.textContent = '0.00 د.ل';

                // تحديث المجموع الكلي
                const newTotal = subtotal + tax;
                finalTotalAmount.textContent = newTotal.toFixed(2) + ' د.ل';

                // تحديث نص رسوم التوصيل
                deliveryFeeRow.querySelector('.text-gray-600').textContent = 'رسوم التوصيل (ملغاة):';
                deliveryFeeAmount.classList.add('line-through', 'text-gray-400');

                // تحديث الحقول المطلوبة
                const addressField = document.querySelector('textarea[name="address"]');
                if (addressField) {
                    addressField.removeAttribute('required');
                }

                // إخفاء خيارات الدفع الإلكتروني
                const electronicPaymentOptions = document.querySelector('.electronic-payment-options');
                if (electronicPaymentOptions) {
                    electronicPaymentOptions.style.display = 'none';
                }

                // تغيير نص الدفع النقدي
                document.querySelectorAll('.delivery-text').forEach(el => el.style.display = 'none');
                document.querySelectorAll('.pickup-text').forEach(el => el.style.display = 'inline');

                // تحديد الدفع النقدي تلقائياً
                const cashRadio = document.querySelector('input[name="payment_method"][value="cash"]');
                if (cashRadio) {
                    cashRadio.checked = true;
                }

            } else {
                // توصيل - إظهار معلومات التوصيل وإضافة رسوم التوصيل
                deliveryInfo.style.display = 'block';
                deliveryFeeAmount.textContent = deliveryFee.toFixed(2) + ' د.ل';

                // تحديث المجموع الكلي
                const newTotal = subtotal + tax + deliveryFee;
                finalTotalAmount.textContent = newTotal.toFixed(2) + ' د.ل';

                // تحديث نص رسوم التوصيل
                deliveryFeeRow.querySelector('.text-gray-600').textContent = 'رسوم التوصيل:';
                deliveryFeeAmount.classList.remove('line-through', 'text-gray-400');

                // تحديث الحقول المطلوبة
                const addressField = document.querySelector('textarea[name="address"]');
                if (addressField) {
                    addressField.setAttribute('required', 'required');
                }

                // إظهار خيارات الدفع الإلكتروني
                const electronicPaymentOptions = document.querySelector('.electronic-payment-options');
                if (electronicPaymentOptions) {
                    electronicPaymentOptions.style.display = 'block';
                }

                // تغيير نص الدفع النقدي
                document.querySelectorAll('.delivery-text').forEach(el => el.style.display = 'inline');
                document.querySelectorAll('.pickup-text').forEach(el => el.style.display = 'none');
            }

            // تحديث تصميم الخيارات
            document.querySelectorAll('.order-type-option').forEach(option => {
                option.classList.remove('border-primary', 'bg-primary/5');
                option.classList.add('border-gray-300', 'dark:border-gray-600');
            });

            this.closest('.order-type-option').classList.remove('border-gray-300', 'dark:border-gray-600');
            this.closest('.order-type-option').classList.add('border-primary', 'bg-primary/5');
        });
    });

    // تطبيق التصميم الأولي للخيار المحدد
    const checkedOption = document.querySelector('input[name="order_type"]:checked');
    if (checkedOption) {
        checkedOption.closest('.order-type-option').classList.remove('border-gray-300', 'dark:border-gray-600');
        checkedOption.closest('.order-type-option').classList.add('border-primary', 'bg-primary/5');

        // تطبيق الحالة الأولية
        if (checkedOption.value === 'pickup') {
            // إخفاء معلومات التوصيل وخيارات الدفع الإلكتروني
            deliveryInfo.style.display = 'none';
            const electronicPaymentOptions = document.querySelector('.electronic-payment-options');
            if (electronicPaymentOptions) {
                electronicPaymentOptions.style.display = 'none';
            }

            // تغيير نص الدفع النقدي
            document.querySelectorAll('.delivery-text').forEach(el => el.style.display = 'none');
            document.querySelectorAll('.pickup-text').forEach(el => el.style.display = 'inline');

            // تحديث رسوم التوصيل
            deliveryFeeAmount.textContent = '0.00 د.ل';
            deliveryFeeRow.querySelector('.text-gray-600').textContent = 'رسوم التوصيل (ملغاة):';
            deliveryFeeAmount.classList.add('line-through', 'text-gray-400');

            // تحديث المجموع الكلي
            const newTotal = subtotal + tax;
            finalTotalAmount.textContent = newTotal.toFixed(2) + ' د.ل';
        }
    }
});
</script>

@include('customer.partials.scripts')

</body>
</html>
