@extends('layouts.admin')

@section('title', 'التقارير - لوحة تحكم Eat Hub')

@section('page-title', 'التقارير')

@section('content')
<!-- العنوان الرئيسي -->
<div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
    <div>
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-2">التقارير الشاملة</h2>
        <p class="text-gray-600 dark:text-gray-400">نظام تقارير متكامل يغطي جميع جوانب المطعم</p>
    </div>
    <div class="flex space-x-2 space-x-reverse mt-4 md:mt-0">
        <button onclick="exportMasterReport('excel')" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center">
            <i class="fas fa-file-excel mr-2"></i>
            تصدير شامل Excel
        </button>
        <button onclick="exportMasterReport('pdf')" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center">
            <i class="fas fa-file-pdf mr-2"></i>
            تصدير شامل PDF
        </button>
    </div>
</div>

<!-- فلاتر التاريخ العامة -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
    <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">فلاتر التاريخ العامة</h3>
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">من تاريخ</label>
            <input type="date" id="global_start_date" value="{{ date('Y-m-01') }}"
                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">إلى تاريخ</label>
            <input type="date" id="global_end_date" value="{{ date('Y-m-d') }}"
                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">فترات سريعة</label>
            <select id="quick_period" onchange="setQuickPeriod()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                <option value="">اختر فترة</option>
                <option value="today">اليوم</option>
                <option value="yesterday">أمس</option>
                <option value="this_week">هذا الأسبوع</option>
                <option value="last_week">الأسبوع الماضي</option>
                <option value="this_month">هذا الشهر</option>
                <option value="last_month">الشهر الماضي</option>
                <option value="this_year">هذا العام</option>
            </select>
        </div>
        <div class="flex items-end">
            <button onclick="applyGlobalFilters()" class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center justify-center">
                <i class="fas fa-filter mr-2"></i>
                تطبيق الفلاتر
            </button>
        </div>
    </div>
</div>

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
    <!-- تقرير المبيعات -->
    @can('reports.sales')
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-all">
        <div class="flex justify-between items-start mb-4">
            <div>
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-2">تقرير المبيعات</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm">عرض تفاصيل المبيعات حسب الفترة الزمنية</p>
            </div>
            <div class="rounded-full bg-blue-100 dark:bg-blue-900/30 p-3">
                <i class="fas fa-chart-line text-blue-500 text-xl"></i>
            </div>
        </div>
        <div class="space-y-2">
            <a href="{{ route('admin.reports.sales') }}" class="inline-block bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-md transition-all w-full text-center">
                <i class="fas fa-eye mr-2"></i>
                <span>عرض التقرير</span>
            </a>
            <div class="flex space-x-2 space-x-reverse">
                <button onclick="exportReport('sales', 'excel')" class="flex-1 bg-green-500 hover:bg-green-600 text-white py-1 px-2 rounded text-sm">
                    <i class="fas fa-file-excel mr-1"></i>
                    Excel
                </button>
                <button onclick="exportReport('sales', 'pdf')" class="flex-1 bg-red-500 hover:bg-red-600 text-white py-1 px-2 rounded text-sm">
                    <i class="fas fa-file-pdf mr-1"></i>
                    PDF
                </button>
                <button onclick="exportReport('sales', 'csv')" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-1 px-2 rounded text-sm">
                    <i class="fas fa-file-csv mr-1"></i>
                    CSV
                </button>
            </div>
        </div>
    </div>
    @endcan

    <!-- التقرير المالي -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-all border-2 border-green-200 dark:border-green-600">
        <div class="flex justify-between items-start mb-4">
            <div>
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-2">💰 التقرير المالي</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm">الأرباح الصافية والإجمالية بدقة عالية</p>
            </div>
            <div class="rounded-full bg-emerald-100 dark:bg-emerald-900/30 p-3">
                <i class="fas fa-coins text-emerald-500 text-xl"></i>
            </div>
        </div>
        <div class="space-y-2">
            <a href="{{ route('admin.reports.financial') }}" class="inline-block bg-emerald-500 hover:bg-emerald-600 text-white font-bold py-2 px-4 rounded-md transition-all w-full text-center">
                <i class="fas fa-eye mr-2"></i>
                <span>عرض التقرير</span>
            </a>
            <div class="flex space-x-2 space-x-reverse">
                <button onclick="exportReport('financial', 'excel')" class="flex-1 bg-green-500 hover:bg-green-600 text-white py-1 px-2 rounded text-sm">
                    <i class="fas fa-file-excel mr-1"></i>
                    Excel
                </button>
                <button onclick="exportReport('financial', 'pdf')" class="flex-1 bg-red-500 hover:bg-red-600 text-white py-1 px-2 rounded text-sm">
                    <i class="fas fa-file-pdf mr-1"></i>
                    PDF
                </button>
                <button onclick="exportReport('financial', 'csv')" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-1 px-2 rounded text-sm">
                    <i class="fas fa-file-csv mr-1"></i>
                    CSV
                </button>
            </div>
        </div>
    </div>

    <!-- تقرير المصروفات -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-all">
        <div class="flex justify-between items-start mb-4">
            <div>
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-2">تقرير المصروفات</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm">عرض تفاصيل المصروفات حسب الفئة والفترة الزمنية</p>
            </div>
            <div class="rounded-full bg-red-100 dark:bg-red-900/30 p-3">
                <i class="fas fa-money-bill-wave text-red-500 text-xl"></i>
            </div>
        </div>
        <a href="{{ route('admin.reports.expenses') }}" class="mt-4 inline-block bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md transition-all w-full text-center">
            <i class="fas fa-eye ml-2"></i>
            <span>عرض التقرير</span>
        </a>
    </div>

    <!-- تقرير المخزون -->
    @can('reports.inventory')
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-all">
        <div class="flex justify-between items-start mb-4">
            <div>
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-2">تقرير المخزون</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm">عرض حالة المخزون والمنتجات منخفضة المخزون</p>
            </div>
            <div class="rounded-full bg-green-100 dark:bg-green-900/30 p-3">
                <i class="fas fa-boxes text-green-500 text-xl"></i>
            </div>
        </div>
        <a href="{{ route('admin.reports.inventory') }}" class="mt-4 inline-block bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md transition-all w-full text-center">
            <i class="fas fa-eye ml-2"></i>
            <span>عرض التقرير</span>
        </a>
    </div>
    @endcan

    <!-- تقرير الطلبات -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-all">
        <div class="flex justify-between items-start mb-4">
            <div>
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-2">تقرير الطلبات</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm">عرض تفاصيل الطلبات وحالتها</p>
            </div>
            <div class="rounded-full bg-yellow-100 dark:bg-yellow-900/30 p-3">
                <i class="fas fa-shopping-cart text-yellow-500 text-xl"></i>
            </div>
        </div>
        <a href="{{ route('admin.reports.orders') }}" class="mt-4 inline-block bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md transition-all w-full text-center">
            <i class="fas fa-eye ml-2"></i>
            <span>عرض التقرير</span>
        </a>
    </div>

    <!-- تقرير العملاء -->
    @can('reports.customers')
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-all">
        <div class="flex justify-between items-start mb-4">
            <div>
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-2">تقرير العملاء</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm">عرض تفاصيل العملاء ونشاطهم</p>
            </div>
            <div class="rounded-full bg-purple-100 dark:bg-purple-900/30 p-3">
                <i class="fas fa-users text-purple-500 text-xl"></i>
            </div>
        </div>
        <a href="{{ route('admin.reports.customers') }}" class="mt-4 inline-block bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md transition-all w-full text-center">
            <i class="fas fa-eye ml-2"></i>
            <span>عرض التقرير</span>
        </a>
    </div>
    @endcan

    <!-- تقرير مقارنة الأداء -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-all">
        <div class="flex justify-between items-start mb-4">
            <div>
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-2">تقرير مقارنة الأداء</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm">مقارنة الأداء بين فترات زمنية مختلفة</p>
            </div>
            <div class="rounded-full bg-indigo-100 dark:bg-indigo-900/30 p-3">
                <i class="fas fa-chart-bar text-indigo-500 text-xl"></i>
            </div>
        </div>
        <a href="{{ route('admin.reports.performance') }}" class="mt-4 inline-block bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md transition-all w-full text-center">
            <i class="fas fa-eye ml-2"></i>
            <span>عرض التقرير</span>
        </a>
    </div>

    <!-- تقرير الربحية الشامل -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-all border-2 border-yellow-200 dark:border-yellow-600">
        <div class="flex justify-between items-start mb-4">
            <div>
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-2">💰 تقرير الربحية الشامل</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm">تحليل مفصل للأرباح وربحية المنتجات</p>
            </div>
            <div class="rounded-full bg-yellow-100 dark:bg-yellow-900/30 p-3">
                <i class="fas fa-coins text-yellow-500 text-xl"></i>
            </div>
        </div>
        <div class="space-y-2">
            <a href="{{ route('admin.reports.profitability') }}" class="inline-block bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-2 px-4 rounded-md transition-all w-full text-center">
                <i class="fas fa-eye mr-2"></i>
                <span>عرض التقرير</span>
            </a>
            <div class="flex space-x-2 space-x-reverse">
                <button onclick="exportReport('profitability', 'excel')" class="flex-1 bg-green-500 hover:bg-green-600 text-white py-1 px-2 rounded text-sm">
                    <i class="fas fa-file-excel mr-1"></i>
                    Excel
                </button>
                <button onclick="exportReport('profitability', 'pdf')" class="flex-1 bg-red-500 hover:bg-red-600 text-white py-1 px-2 rounded text-sm">
                    <i class="fas fa-file-pdf mr-1"></i>
                    PDF
                </button>
                <button onclick="exportReport('profitability', 'csv')" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-1 px-2 rounded text-sm">
                    <i class="fas fa-file-csv mr-1"></i>
                    CSV
                </button>
            </div>
        </div>
    </div>

    <!-- تقرير مقارنة الفترات -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-all border-2 border-purple-200 dark:border-purple-600">
        <div class="flex justify-between items-start mb-4">
            <div>
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-2">📊 مقارنة الفترات</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm">مقارنة الأداء بين فترتين زمنيتين</p>
            </div>
            <div class="rounded-full bg-purple-100 dark:bg-purple-900/30 p-3">
                <i class="fas fa-chart-line text-purple-500 text-xl"></i>
            </div>
        </div>
        <div class="space-y-2">
            <a href="{{ route('admin.reports.period-comparison') }}" class="inline-block bg-purple-500 hover:bg-purple-600 text-white font-bold py-2 px-4 rounded-md transition-all w-full text-center">
                <i class="fas fa-eye mr-2"></i>
                <span>عرض التقرير</span>
            </a>
            <div class="flex space-x-2 space-x-reverse">
                <button onclick="exportReport('period-comparison', 'excel')" class="flex-1 bg-green-500 hover:bg-green-600 text-white py-1 px-2 rounded text-sm">
                    <i class="fas fa-file-excel mr-1"></i>
                    Excel
                </button>
                <button onclick="exportReport('period-comparison', 'pdf')" class="flex-1 bg-red-500 hover:bg-red-600 text-white py-1 px-2 rounded text-sm">
                    <i class="fas fa-file-pdf mr-1"></i>
                    PDF
                </button>
                <button onclick="exportReport('period-comparison', 'csv')" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-1 px-2 rounded text-sm">
                    <i class="fas fa-file-csv mr-1"></i>
                    CSV
                </button>
            </div>
        </div>
    </div>
</div>

<!-- قسم التقارير المتقدمة -->
<div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 rounded-lg p-6 mb-6 border-2 border-blue-200 dark:border-blue-600">
    <div class="flex items-center justify-between mb-4">
        <div>
            <h3 class="text-xl font-bold text-gray-800 dark:text-white">🚀 التقارير المتقدمة</h3>
            <p class="text-gray-600 dark:text-gray-400">تقارير شاملة ومتقدمة لتحليل عميق للبيانات</p>
        </div>
        <div class="rounded-full bg-blue-100 dark:bg-blue-900/30 p-4">
            <i class="fas fa-rocket text-blue-500 text-2xl"></i>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
            <h4 class="font-semibold text-gray-800 dark:text-white mb-2">📈 تحليل الاتجاهات</h4>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">تحليل اتجاهات المبيعات والأرباح</p>
            <button class="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-3 rounded text-sm">
                قريباً
            </button>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
            <h4 class="font-semibold text-gray-800 dark:text-white mb-2">🎯 تحليل العملاء</h4>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">تحليل سلوك وتفضيلات العملاء</p>
            <button class="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-3 rounded text-sm">
                قريباً
            </button>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
            <h4 class="font-semibold text-gray-800 dark:text-white mb-2">🔮 التنبؤات</h4>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">توقعات المبيعات والطلب</p>
            <button class="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-3 rounded text-sm">
                قريباً
            </button>
        </div>
    </div>
</div>

<!-- JavaScript للتصدير والفلاتر -->
<script>
// تصدير التقارير
function exportReport(reportType, format) {
    const startDate = document.getElementById('global_start_date').value;
    const endDate = document.getElementById('global_end_date').value;

    const url = `/admin/reports/export/${reportType}?format=${format}&start_date=${startDate}&end_date=${endDate}`;
    window.open(url, '_blank');
}

// تصدير التقرير الشامل
function exportMasterReport(format) {
    const startDate = document.getElementById('global_start_date').value;
    const endDate = document.getElementById('global_end_date').value;

    const url = `/admin/reports/export/master?format=${format}&start_date=${startDate}&end_date=${endDate}`;
    window.open(url, '_blank');
}

// تطبيق الفترات السريعة
function setQuickPeriod() {
    const period = document.getElementById('quick_period').value;
    const today = new Date();
    let startDate, endDate;

    switch(period) {
        case 'today':
            startDate = endDate = today.toISOString().split('T')[0];
            break;
        case 'yesterday':
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            startDate = endDate = yesterday.toISOString().split('T')[0];
            break;
        case 'this_week':
            const startOfWeek = new Date(today);
            startOfWeek.setDate(today.getDate() - today.getDay());
            startDate = startOfWeek.toISOString().split('T')[0];
            endDate = today.toISOString().split('T')[0];
            break;
        case 'last_week':
            const lastWeekEnd = new Date(today);
            lastWeekEnd.setDate(today.getDate() - today.getDay() - 1);
            const lastWeekStart = new Date(lastWeekEnd);
            lastWeekStart.setDate(lastWeekEnd.getDate() - 6);
            startDate = lastWeekStart.toISOString().split('T')[0];
            endDate = lastWeekEnd.toISOString().split('T')[0];
            break;
        case 'this_month':
            startDate = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
            endDate = today.toISOString().split('T')[0];
            break;
        case 'last_month':
            const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
            const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
            startDate = lastMonth.toISOString().split('T')[0];
            endDate = lastMonthEnd.toISOString().split('T')[0];
            break;
        case 'this_year':
            startDate = new Date(today.getFullYear(), 0, 1).toISOString().split('T')[0];
            endDate = today.toISOString().split('T')[0];
            break;
        default:
            return;
    }

    document.getElementById('global_start_date').value = startDate;
    document.getElementById('global_end_date').value = endDate;
}

// تطبيق الفلاتر العامة
function applyGlobalFilters() {
    const startDate = document.getElementById('global_start_date').value;
    const endDate = document.getElementById('global_end_date').value;

    // يمكن إضافة منطق إضافي هنا لتحديث التقارير
    showNotification('تم تطبيق الفلاتر بنجاح', 'success');
}

// إظهار الإشعارات
function showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg text-white ${
        type === 'success' ? 'bg-green-500' :
        type === 'error' ? 'bg-red-500' :
        type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : type === 'warning' ? 'exclamation' : 'info'}-circle mr-2"></i>
            <span>${message}</span>
        </div>
    `;

    // إضافة الإشعار إلى الصفحة
    document.body.appendChild(notification);

    // إزالة الإشعار بعد 3 ثوان
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// تحديث أزرار التصدير عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة أزرار التصدير لجميع البطاقات
    const reportCards = document.querySelectorAll('.bg-white.dark\\:bg-gray-800');

    reportCards.forEach(card => {
        const title = card.querySelector('h3');
        if (title) {
            const reportType = title.textContent.includes('المبيعات') ? 'sales' :
                             title.textContent.includes('المالي') ? 'financial' :
                             title.textContent.includes('الموظفين') ? 'employee-performance' :
                             title.textContent.includes('المخزون') ? 'inventory' :
                             title.textContent.includes('العملاء') ? 'customers' : null;

            if (reportType && !card.querySelector('.export-buttons')) {
                const existingButton = card.querySelector('a[href*="reports"]');
                if (existingButton && !existingButton.nextElementSibling?.classList.contains('export-buttons')) {
                    const exportDiv = document.createElement('div');
                    exportDiv.className = 'flex space-x-2 space-x-reverse export-buttons';
                    exportDiv.innerHTML = `
                        <button onclick="exportReport('${reportType}', 'excel')" class="flex-1 bg-green-500 hover:bg-green-600 text-white py-1 px-2 rounded text-sm">
                            <i class="fas fa-file-excel mr-1"></i>
                            Excel
                        </button>
                        <button onclick="exportReport('${reportType}', 'pdf')" class="flex-1 bg-red-500 hover:bg-red-600 text-white py-1 px-2 rounded text-sm">
                            <i class="fas fa-file-pdf mr-1"></i>
                            PDF
                        </button>
                        <button onclick="exportReport('${reportType}', 'csv')" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-1 px-2 rounded text-sm">
                            <i class="fas fa-file-csv mr-1"></i>
                            CSV
                        </button>
                    `;

                    const parentDiv = existingButton.parentElement;
                    if (parentDiv && !parentDiv.classList.contains('space-y-2')) {
                        parentDiv.classList.add('space-y-2');
                        parentDiv.appendChild(exportDiv);
                    }
                }
            }
        }
    });
});
</script>

<style>
.card-hover {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* تحسين أزرار التصدير */
.export-buttons button {
    transition: all 0.2s ease-in-out;
}

.export-buttons button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* تحسين الفلاتر */
#quick_period:focus,
#global_start_date:focus,
#global_end_date:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* تحسين البطاقات المميزة */
.border-green-200 {
    position: relative;
    overflow: hidden;
}

.border-green-200::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #10b981, #059669);
}

/* تحسين الإشعارات */
.notification-enter {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
</style>

@endsection
