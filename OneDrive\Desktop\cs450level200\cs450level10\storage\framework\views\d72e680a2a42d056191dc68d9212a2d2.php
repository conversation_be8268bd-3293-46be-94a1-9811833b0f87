<script>
document.addEventListener('DOMContentLoaded', function() {
    // عناصر البحث
    const searchToggle = document.getElementById('searchToggle');
    const searchModal = document.getElementById('searchModal');
    const closeSearch = document.getElementById('closeSearch');
    const searchInput = document.getElementById('searchInput');
    const searchResults = document.getElementById('searchResults');
    const searchLoading = document.getElementById('searchLoading');
    const noResults = document.getElementById('noResults');

    // عناصر الإشعارات
    const notificationsToggle = document.getElementById('notificationsToggle');
    const notificationsDropdown = document.getElementById('notificationsDropdown');
    const notificationCount = document.getElementById('notificationCount');
    const notificationsList = document.getElementById('notificationsList');
    const markAllRead = document.getElementById('markAllRead');

    let searchTimeout;

    // === وظائف البحث ===
    
    // فتح نافذة البحث
    if (searchToggle) {
        searchToggle.addEventListener('click', function() {
            searchModal.classList.remove('hidden');
            searchInput.focus();
        });
    }

    // إغلاق نافذة البحث
    if (closeSearch) {
        closeSearch.addEventListener('click', function() {
            searchModal.classList.add('hidden');
            searchInput.value = '';
            hideSearchResults();
        });
    }

    // إغلاق البحث عند الضغط خارج النافذة
    if (searchModal) {
        searchModal.addEventListener('click', function(e) {
            if (e.target === searchModal) {
                searchModal.classList.add('hidden');
                searchInput.value = '';
                hideSearchResults();
            }
        });
    }

    // البحث أثناء الكتابة
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();
            
            clearTimeout(searchTimeout);
            
            if (query.length < 2) {
                hideSearchResults();
                return;
            }

            searchTimeout = setTimeout(() => {
                performSearch(query);
            }, 300);
        });

        // البحث عند الضغط على Enter
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const query = this.value.trim();
                if (query.length >= 2) {
                    // الانتقال لصفحة النتائج المفصلة
                    window.location.href = `/search?q=${encodeURIComponent(query)}`;
                }
            }
        });
    }

    // تنفيذ البحث
    function performSearch(query) {
        showSearchLoading();

        fetch('/search/quick', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ q: query })
        })
        .then(response => response.json())
        .then(data => {
            hideSearchLoading();
            
            if (data.success && data.results.length > 0) {
                displaySearchResults(data.results);
            } else {
                showNoResults();
            }
        })
        .catch(error => {
            console.error('Search error:', error);
            hideSearchLoading();
            showNoResults();
        });
    }

    // عرض نتائج البحث
    function displaySearchResults(results) {
        searchResults.innerHTML = '';
        
        results.forEach(result => {
            const resultElement = document.createElement('div');
            resultElement.className = 'p-3 hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-200 dark:border-gray-600 cursor-pointer';
            
            resultElement.innerHTML = `
                <div class="flex items-start space-x-3 space-x-reverse">
                    <div class="flex-shrink-0">
                        <i class="${result.icon} text-primary"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <h4 class="text-sm font-medium text-gray-800 dark:text-white truncate">${result.title}</h4>
                        <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">${result.description}</p>
                        ${result.price ? `<span class="text-xs text-primary font-medium">${result.price}</span>` : ''}
                        ${result.date ? `<span class="text-xs text-gray-500">${result.date}</span>` : ''}
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-chevron-left text-gray-400 text-xs"></i>
                    </div>
                </div>
            `;

            resultElement.addEventListener('click', function() {
                if (result.url) {
                    window.location.href = result.url;
                }
            });

            searchResults.appendChild(resultElement);
        });

        // إضافة رابط "عرض جميع النتائج"
        const viewAllElement = document.createElement('div');
        viewAllElement.className = 'p-3 text-center border-t border-gray-200 dark:border-gray-600';
        viewAllElement.innerHTML = `
            <a href="/search?q=${encodeURIComponent(searchInput.value)}" class="text-primary hover:text-primary/80 text-sm font-medium">
                عرض جميع النتائج
            </a>
        `;
        searchResults.appendChild(viewAllElement);

        showSearchResults();
    }

    // إظهار/إخفاء عناصر البحث
    function showSearchResults() {
        searchResults.classList.remove('hidden');
        noResults.classList.add('hidden');
    }

    function hideSearchResults() {
        searchResults.classList.add('hidden');
        noResults.classList.add('hidden');
    }

    function showSearchLoading() {
        searchLoading.classList.remove('hidden');
        hideSearchResults();
    }

    function hideSearchLoading() {
        searchLoading.classList.add('hidden');
    }

    function showNoResults() {
        noResults.classList.remove('hidden');
        searchResults.classList.add('hidden');
    }

    // === وظائف الإشعارات ===

    // فتح/إغلاق قائمة الإشعارات
    if (notificationsToggle) {
        notificationsToggle.addEventListener('click', function(e) {
            e.stopPropagation();
            
            if (notificationsDropdown.classList.contains('hidden')) {
                notificationsDropdown.classList.remove('hidden');
                loadNotifications();
            } else {
                notificationsDropdown.classList.add('hidden');
            }
        });
    }

    // إغلاق الإشعارات عند الضغط خارجها
    document.addEventListener('click', function(e) {
        if (notificationsDropdown && !notificationsDropdown.contains(e.target) && !notificationsToggle.contains(e.target)) {
            notificationsDropdown.classList.add('hidden');
        }
    });

    // تحديد جميع الإشعارات كمقروءة
    if (markAllRead) {
        markAllRead.addEventListener('click', function() {
            fetch('/customer/notifications/read-all', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateNotificationCount(0);
                    loadNotifications();
                }
            })
            .catch(error => console.error('Error marking notifications as read:', error));
        });
    }

    // تحميل الإشعارات
    function loadNotifications() {
        fetch('/customer/notifications/latest')
        .then(response => response.json())
        .then(data => {
            displayNotifications(data.notifications || []);
        })
        .catch(error => {
            console.error('Error loading notifications:', error);
            notificationsList.innerHTML = '<div class="p-4 text-center text-gray-500">حدث خطأ في تحميل الإشعارات</div>';
        });
    }

    // عرض الإشعارات
    function displayNotifications(notifications) {
        if (notifications.length === 0) {
            notificationsList.innerHTML = '<div class="p-4 text-center text-gray-500 dark:text-gray-400">لا توجد إشعارات جديدة</div>';
            return;
        }

        notificationsList.innerHTML = '';
        
        notifications.forEach(notification => {
            const notificationElement = document.createElement('div');
            notificationElement.className = `p-3 hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-200 dark:border-gray-600 cursor-pointer ${!notification.is_read ? 'bg-blue-50 dark:bg-blue-900/20' : ''}`;
            
            notificationElement.innerHTML = `
                <div class="flex items-start space-x-3 space-x-reverse">
                    <div class="flex-shrink-0">
                        <i class="fas fa-bell text-primary"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <h4 class="text-sm font-medium text-gray-800 dark:text-white">${notification.title || 'إشعار'}</h4>
                        <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">${notification.message}</p>
                        <span class="text-xs text-gray-500">${formatDate(notification.created_at)}</span>
                    </div>
                    ${!notification.is_read ? '<div class="flex-shrink-0"><div class="w-2 h-2 bg-primary rounded-full"></div></div>' : ''}
                </div>
            `;

            notificationElement.addEventListener('click', function() {
                markNotificationAsRead(notification.notification_id);
                if (notification.action_url) {
                    window.location.href = notification.action_url;
                }
            });

            notificationsList.appendChild(notificationElement);
        });
    }

    // تحديد إشعار كمقروء
    function markNotificationAsRead(notificationId) {
        fetch(`/customer/notifications/${notificationId}/read`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(() => {
            updateNotificationCount();
        })
        .catch(error => console.error('Error marking notification as read:', error));
    }

    // تحديث عداد الإشعارات
    function updateNotificationCount(count = null) {
        if (count !== null) {
            if (count > 0) {
                notificationCount.textContent = count;
                notificationCount.classList.remove('hidden');
            } else {
                notificationCount.classList.add('hidden');
            }
        } else {
            // جلب العدد من الخادم
            fetch('/customer/notifications/count')
            .then(response => response.json())
            .then(data => {
                if (data.count > 0) {
                    notificationCount.textContent = data.count;
                    notificationCount.classList.remove('hidden');
                } else {
                    notificationCount.classList.add('hidden');
                }
            })
            .catch(error => console.error('Error getting notification count:', error));
        }
    }

    // تنسيق التاريخ
    function formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInMinutes = Math.floor((now - date) / (1000 * 60));
        
        if (diffInMinutes < 1) return 'الآن';
        if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;
        if (diffInMinutes < 1440) return `منذ ${Math.floor(diffInMinutes / 60)} ساعة`;
        return `منذ ${Math.floor(diffInMinutes / 1440)} يوم`;
    }

    // تحديث عداد الإشعارات عند تحميل الصفحة
    if (notificationCount) {
        updateNotificationCount();
        
        // تحديث العداد كل 30 ثانية
        setInterval(updateNotificationCount, 30000);
    }
});
</script>
<?php /**PATH C:\Users\<USER>\OneDrive\Desktop\cs450level200\cs450level10\resources\views/customer/partials/search-notifications.blade.php ENDPATH**/ ?>