@extends('customer.layouts.simple')

@section('title', 'قائمة الطعام - Eat Hub')

@section('content')

<!-- المحتوى الرئيسي -->
<main class="min-h-screen pt-20">
    <div class="container mx-auto px-4 py-8">
        <!-- عنوان الصفحة -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-800 dark:text-white mb-4">قائمة الطعام</h1>
            <p class="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                اكتشف مجموعة متنوعة من الأطباق الشهية المحضرة بعناية من أفضل المكونات
            </p>
        </div>

        <!-- فلاتر الفئات -->
        <div class="flex flex-wrap justify-center gap-4 mb-8">
            <button class="category-filter active px-6 py-2 rounded-full bg-primary text-white font-medium transition" data-category="all">
                جميع الأطباق
            </button>
            @foreach($categories as $key => $name)
            <button class="category-filter px-6 py-2 rounded-full bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 hover:bg-primary hover:text-white font-medium transition" data-category="{{ $key }}">
                {{ $name }}
            </button>
            @endforeach
        </div>

        <!-- عناصر القائمة -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @forelse($menuItems as $category => $items)
                @foreach($items as $item)
                <div class="menu-item bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300" data-category="{{ $item->category }}">
                    <div class="h-48 overflow-hidden relative">
                        <img src="{{ $item->image_path ?? 'https://images.unsplash.com/photo-1513104890138-7c749659a591?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80' }}"
                             alt="{{ $item->name }}"
                             class="w-full h-full object-cover hover:scale-105 transition-transform duration-300">
                        @if($item->is_featured)
                        <div class="absolute top-4 right-4 bg-primary text-white rounded-full py-1 px-3 text-sm font-semibold">
                            مميز
                        </div>
                        @endif
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="font-bold text-lg text-gray-800 dark:text-white">{{ $item->name }}</h3>
                            <span class="font-bold text-primary text-lg">{{ number_format($item->price, 2) }} د.ل</span>
                        </div>
                        <p class="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-2">{{ $item->description }}</p>
                        <div class="flex justify-between items-center">
                            <div class="text-yellow-400 flex text-sm">
                                @for($i = 1; $i <= 5; $i++)
                                    <i class="fas fa-star"></i>
                                @endfor
                                <span class="text-gray-600 dark:text-gray-400 mr-1">4.5</span>
                            </div>
                            @auth
                            <button class="add-to-cart bg-primary hover:bg-primary/90 text-white py-2 px-4 rounded-full text-sm transition"
                                    onclick="addToCart({{ $item->item_id }}, '{{ $item->name }}', {{ $item->price }})"
                                    data-item-id="{{ $item->item_id }}">
                                <i class="fas fa-plus ml-1"></i>إضافة للسلة
                            </button>
                            @else
                            <a href="{{ route('login') }}" class="bg-gray-400 hover:bg-gray-500 text-white py-2 px-4 rounded-full text-sm transition">
                                <i class="fas fa-sign-in-alt ml-1"></i>سجل دخول للطلب
                            </a>
                            @endauth
                        </div>
                    </div>
                </div>
                @endforeach
            @empty
            <div class="col-span-full text-center py-12">
                <i class="fas fa-utensils text-6xl text-gray-300 dark:text-gray-600 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">لا توجد عناصر في القائمة</h3>
                <p class="text-gray-500 dark:text-gray-500">سيتم إضافة عناصر جديدة قريباً</p>
            </div>
            @endforelse
        </div>
    </div>

@push('scripts')
<!-- سكريبت فلترة الفئات -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const categoryFilters = document.querySelectorAll('.category-filter');
    const menuItems = document.querySelectorAll('.menu-item');

    categoryFilters.forEach(filter => {
        filter.addEventListener('click', function() {
            const category = this.dataset.category;

            // تحديث الأزرار النشطة
            categoryFilters.forEach(f => f.classList.remove('active', 'bg-primary', 'text-white'));
            categoryFilters.forEach(f => f.classList.add('bg-white', 'dark:bg-gray-800', 'text-gray-700', 'dark:text-gray-300', 'border', 'border-gray-200', 'dark:border-gray-700'));

            this.classList.add('active', 'bg-primary', 'text-white');
            this.classList.remove('bg-white', 'dark:bg-gray-800', 'text-gray-700', 'dark:text-gray-300', 'border', 'border-gray-200', 'dark:border-gray-700');

            // فلترة العناصر
            menuItems.forEach(item => {
                if (category === 'all' || item.dataset.category === category) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });

});

// إضافة للسلة
function addToCart(itemId, itemName, itemPrice) {
    const button = document.querySelector(`[data-item-id="${itemId}"]`);
    const originalText = button.innerHTML;

    // تغيير النص أثناء التحميل
    button.innerHTML = '<i class="fas fa-spinner fa-spin ml-1"></i>جاري الإضافة...';
    button.disabled = true;

    fetch('/customer/cart/add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            menu_item_id: itemId,
            quantity: 1
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحديث عداد السلة في الـ header
            updateCartCount(data.cart_count);

            // تغيير النص مؤقتاً
            button.innerHTML = '<i class="fas fa-check ml-1"></i>تم الإضافة!';
            button.classList.remove('bg-primary', 'hover:bg-primary/90');
            button.classList.add('bg-green-500');

            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('bg-green-500');
                button.classList.add('bg-primary', 'hover:bg-primary/90');
                button.disabled = false;
            }, 2000);

            showNotification(data.message, 'success');
        } else {
            button.innerHTML = originalText;
            button.disabled = false;
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        button.innerHTML = originalText;
        button.disabled = false;
        showNotification('حدث خطأ أثناء إضافة العنصر للسلة', 'error');
    });
}

// تحديث عداد السلة
function updateCartCount(count) {
    const cartCount = document.getElementById('cartCount');
    if (cartCount) {
        cartCount.textContent = count;
    }
}

// عرض الإشعارات
function showNotification(message, type) {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `fixed top-20 left-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full ${
        type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'} ml-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // إظهار الإشعار
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // إخفاء الإشعار بعد 3 ثوان
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}
</script>
@endpush
@endsection
