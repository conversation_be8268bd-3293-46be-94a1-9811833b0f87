/* نظام تقييم النجوم التفاعلي */
.star-rating {
    display: flex;
    direction: ltr;
    gap: 4px;
}

.star-rating .star {
    font-size: 1.5rem;
    color: #d1d5db; /* gray-300 */
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
}

.star-rating .star:hover {
    color: #fbbf24; /* yellow-400 */
    transform: scale(1.1);
}

.star-rating .star.active {
    color: #f59e0b; /* yellow-500 */
}

.star-rating .star.filled {
    color: #f59e0b; /* yellow-500 */
}

/* تأثيرات إضافية */
.star-rating .star:hover ~ .star {
    color: #d1d5db; /* gray-300 */
}

.star-rating:hover .star {
    color: #d1d5db; /* gray-300 */
}

.star-rating:hover .star:hover,
.star-rating:hover .star:hover ~ .star {
    color: #fbbf24; /* yellow-400 */
}

/* نجوم للعرض فقط (غير تفاعلية) */
.star-display {
    display: flex;
    direction: ltr;
    gap: 2px;
}

.star-display .star {
    font-size: 1rem;
    color: #f59e0b; /* yellow-500 */
}

.star-display .star.empty {
    color: #d1d5db; /* gray-300 */
}

/* تحسينات للوضع المظلم */
.dark .star-rating .star {
    color: #4b5563; /* gray-600 */
}

.dark .star-rating .star:hover {
    color: #fbbf24; /* yellow-400 */
}

.dark .star-rating .star.active,
.dark .star-rating .star.filled {
    color: #f59e0b; /* yellow-500 */
}

.dark .star-display .star.empty {
    color: #4b5563; /* gray-600 */
}

/* أحجام مختلفة للنجوم */
.star-rating.small .star {
    font-size: 1rem;
}

.star-rating.large .star {
    font-size: 2rem;
}

.star-display.small .star {
    font-size: 0.875rem;
}

.star-display.large .star {
    font-size: 1.25rem;
}

/* تأثير النبض للنجوم النشطة */
.star-rating .star.pulse {
    animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 640px) {
    .star-rating .star {
        font-size: 1.25rem;
        padding: 4px;
    }
    
    .star-rating.small .star {
        font-size: 1rem;
        padding: 2px;
    }
}
